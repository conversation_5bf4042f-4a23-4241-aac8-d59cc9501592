﻿using Prism.Commands;
using Prism.Mvvm;
using System.Collections.Generic;
using System.Collections.Specialized;

namespace NCX.Mobile.Models
{
    public class BrowsableItemCollectionViewModel<TItem> : BindableBase where TItem : class
    {
        TItem _currentItem;
        int? _currentPosition;
        int _itemCount;
        IList<TItem> _itemCollection;

        public int? CurrentPosition
        {
            get { return _currentPosition; }
            set
            {
                if (_currentPosition != value)
                {
                    SetProperty(ref _currentPosition, value);
                    UpdateCurrentItemAndPosition();
                }
            }
        }

        public TItem CurrentItem
        {
            get { return _currentItem; }
            private set { SetProperty(ref _currentItem, value); }
        }

        public int ItemCount
        {
            get { return _itemCount; }
            private set { SetProperty(ref _itemCount, value); }
        }

        public DelegateCommand SetNextItemCurrentCommand { get; private set; }
        public DelegateCommand SetPreviousItemCurrentCommand { get; private set; }

        public BrowsableItemCollectionViewModel(IList<TItem> itemCollection)
        {
            _itemCollection = itemCollection;

            _currentPosition = 0;
            UpdateCurrentItemAndPosition();

            SetNextItemCurrentCommand = new DelegateCommand(ExecuteSetNextItemCurrentCommand, CanExecuteSetNextItemCurrentCommand).ObservesProperty(() => CurrentPosition);
            SetPreviousItemCurrentCommand = new DelegateCommand(ExecuteSetPreviousItemCurrentCommand, CanExecuteSetPreviousItemCurrentCommand).ObservesProperty(() => CurrentPosition);

            var notifyCollectionChanged = itemCollection as INotifyCollectionChanged;
            notifyCollectionChanged.CollectionChanged += (s, e) =>
            {
                SetNextItemCurrentCommand.RaiseCanExecuteChanged();
                SetPreviousItemCurrentCommand.RaiseCanExecuteChanged();

                ItemCount = itemCollection.Count;

                UpdateCurrentItemAndPosition();
            };
        }

        void ExecuteSetNextItemCurrentCommand()
        {
            ++CurrentPosition;
        }

        bool CanExecuteSetNextItemCurrentCommand()
        {
            if (CurrentPosition == null)
            {
                return false;
            }

            return CurrentPosition < _itemCollection.Count - 1;
        }

        void ExecuteSetPreviousItemCurrentCommand()
        {
            --CurrentPosition;
        }

        bool CanExecuteSetPreviousItemCurrentCommand()
        {
            if (CurrentPosition == null)
            {
                return false;
            }

            return CurrentPosition > 0;
        }

        void UpdateCurrentItemAndPosition()
        {
            if (_itemCollection.Count == 0)
            {
                CurrentPosition = null;
                CurrentItem = null;
            }
            else if (CurrentPosition == null || CurrentPosition.Value > _itemCollection.Count - 1)
            {
                CurrentPosition = _itemCollection.Count - 1;
            }
            else
            {
                CurrentItem = _itemCollection[(int)CurrentPosition];
            }
        }
    }
}
