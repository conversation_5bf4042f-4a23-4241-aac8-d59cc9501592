﻿using System.Linq;

namespace NCX.Mobile.Models
{
    public class LoadItemViewModel
    {
        public Haul Haul { get; }
        public Load Load { get; }
        public LoadOperation FirstPickup { get; }
        public LoadOperation LastDropoff { get; }

        public LoadItemViewModel(Haul haul)
        {
            Haul = haul ?? throw new ArgumentNullException(nameof(haul));
            Load = Haul.Loads?.SingleOrDefault() ?? throw new InvalidOperationException("Haul has no loads");

            var pickups = Load.Pickups?.OrderBy(d => d.ExpectedDate).ToList() ?? new List<LoadOperation>();
            var dropoffs = Load.Dropoffs?.OrderBy(d => d.ExpectedDate).ToList() ?? new List<LoadOperation>();

            FirstPickup = pickups.FirstOrDefault() ?? throw new InvalidOperationException("Load has no pickups");
            LastDropoff = dropoffs.LastOrDefault() ?? throw new InvalidOperationException("Load has no dropoffs");
        }
    }
}
