﻿using System.Collections.Generic;
using System.Linq;

namespace NCX.Mobile.Models
{
    public class CargoItemViewModel
    {
        public string Id { get; private set; }
        public bool IsPickup { get; set; }
        public string Description { get; set; }
        public string UnitType { get; set; }
        public int UnitCount { get; set; }
        public decimal UnitWeight { get; set; }
        public string WeightType { get; set; }
        public bool IsPalletized { get; set; }
        public int TotalPallets { get; set; }

        public static IEnumerable<CargoItemViewModel> GetLoadCargoItems(Load load)
        {
            var operations = load.Operations.SelectMany(loadOp => loadOp.Cargos.Select(cargo => new CargoItemViewModel()
            {
                Id = cargo.Id,
                IsPickup = true,
                Description = cargo.Description,
                UnitType = cargo.UnitType,
                UnitCount = cargo.TotalUnits,
                UnitWeight = cargo.TotalWeight,
                WeightType = cargo.WeightType,
                IsPalletized = cargo.IsPalletized,
                TotalPallets = cargo.TotalPallets,
            }));

            return operations;
        }
    }
}
