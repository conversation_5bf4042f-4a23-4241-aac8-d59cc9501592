﻿using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace NCX.Mobile.Models
{
    public class LoadItemGroup : IEnumerable<LoadItemViewModel>
    {
        readonly IEnumerable<LoadItemViewModel> _loadItems;

        public LoadStatus LoadStatus
        {
            get; private set;
        }

        public LoadItemGroup(IEnumerable<LoadItemViewModel> loadItems)
        {
            _loadItems = loadItems;
            LoadStatus = loadItems.First().Load.Status;
        }

        public IEnumerator<LoadItemViewModel> GetEnumerator()
        {
            return _loadItems.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return _loadItems.GetEnumerator();
        }
    }
}
