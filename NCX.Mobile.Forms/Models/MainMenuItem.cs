﻿using System.Collections.Generic;
using System.Windows.Input;

namespace NCX.Mobile.Models
{
    public class MainMenuItem : List<MainMenuItem>
    {
        public string Icon { get; private set; }

        public string Title { get; private set; }

        public ICommand Command { get; private set; }

        public bool IsChild { get; private set; }

        public MainMenuItem(string icon, string title, ICommand command = null, bool isChild = false)
        {
            Icon = icon;
            Title = title;
            Command = command;
            IsChild = isChild;
        }
    }
}