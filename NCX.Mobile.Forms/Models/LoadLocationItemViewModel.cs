﻿using System.Linq;
using Prism.Mvvm;

namespace NCX.Mobile.Models
{
    public class LoadLocationItemViewModel : BindableBase
    {
        Haul _haul;

        public LoadOperation LoadOperation { get; private set; }

        // Position in the location collection
        public int ItemPosition { get; private set; }
        public int ItemTotalCount { get; private set; }

        public LoadLocationItemViewModel(Haul haul, int loadOperationId)
        {
            _haul = haul;
            var load = haul.Loads.Single();
            LoadOperation = load.GetLoadOperation(loadOperationId);
        }

        public LoadLocationItemViewModel(Haul haul, int loadOperationId, int itemPosition, int itemTotalCount)
            : this(haul, loadOperationId)
        {
            ItemPosition = itemPosition;

            ItemTotalCount = itemTotalCount;
        }
    }
}
