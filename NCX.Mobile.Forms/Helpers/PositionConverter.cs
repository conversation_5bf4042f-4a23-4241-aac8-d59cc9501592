﻿namespace NCX.Mobile.Helpers
{
    using Shiny.Locations;
    using Microsoft.Maui.Controls.Maps;

    public static class PositionConverter
    {
        /// <summary>
        /// Converts Shiny.Locations.Position to Microsoft.Maui.Controls.Maps.Position.
        /// </summary>
        /// <param name="shinyPosition">The Shiny position.</param>
        /// <returns>The MAUI Position.</returns>
        public static Location ToMauiPosition(this Shiny.Locations.Position shinyPosition)
        {
            return new Location(shinyPosition.Latitude, shinyPosition.Longitude);
        }

        /// <summary>
        /// Converts Microsoft.Maui.Controls.Maps.Position to Shiny.Locations.Position.
        /// </summary>
        /// <param name="mauiPosition">The MAUI position.</param>
        /// <returns>The Shiny Position.</returns>
        public static Shiny.Locations.Position ToShinyPosition(this Position mauiPosition)
        {
            return new Shiny.Locations.Position(mauiPosition.Latitude, mauiPosition.Longitude);
        }
    }
}
