﻿using Microsoft.Maui.Controls;

namespace NCX.Mobile.Helpers
{
    public static class ButtonEx
    {
        public static readonly BindableProperty IsLinkButtonProperty =
            BindableProperty.CreateAttached("IsLinkButton", typeof(bool), typeof(ButtonEx), false);

        public static bool GetIsLinkButton(BindableObject view)
        {
            return (bool)view.GetValue(IsLinkButtonProperty);
        }

        public static void SetIsLinkButton(BindableObject view, bool value)
        {
            view.SetValue(IsLinkButtonProperty, value);
        }
    }
}
