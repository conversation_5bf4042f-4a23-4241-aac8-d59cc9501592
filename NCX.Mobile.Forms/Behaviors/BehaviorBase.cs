﻿using System;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Behaviors
{
    /// <summary>
    /// A base class for creating behaviors that can be attached to bindable objects.
    /// </summary>
    /// <typeparam name="T">The type of the bindable object.</typeparam>
    public class BehaviorBase<T> : Behavior<T> where T : BindableObject
    {
        /// <summary>
        /// Gets the associated object to which the behavior is attached.
        /// </summary>
        public T AssociatedObject { get; private set; }

        /// <summary>
        /// Called when the behavior is attached to a bindable object.
        /// </summary>
        /// <param name="bindable">The bindable object.</param>
        protected override void OnAttachedTo(T bindable)
        {
            base.OnAttachedTo(bindable);
            AssociatedObject = bindable;

            if (bindable.BindingContext != null)
            {
                BindingContext = bindable.BindingContext;
            }

            bindable.BindingContextChanged += OnBindingContextChanged;
        }

        /// <summary>
        /// Called when the behavior is detaching from a bindable object.
        /// </summary>
        /// <param name="bindable">The bindable object.</param>
        protected override void OnDetachingFrom(T bindable)
        {
            base.OnDetachingFrom(bindable);
            bindable.BindingContextChanged -= OnBindingContextChanged;
            AssociatedObject = null;
        }

        /// <summary>
        /// Handles the BindingContextChanged event of the associated object.
        /// </summary>
        /// <param name="sender">The sender.</param>
        /// <param name="e">The event arguments.</param>
        void OnBindingContextChanged(object sender, EventArgs e)
        {
            OnBindingContextChanged();
        }

        /// <summary>
        /// Handles changes to the binding context.
        /// </summary>
        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();
            BindingContext = AssociatedObject.BindingContext;
        }
    }
}
