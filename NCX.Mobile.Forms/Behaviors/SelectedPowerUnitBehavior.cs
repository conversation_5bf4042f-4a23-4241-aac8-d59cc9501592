﻿using System;
using System.Collections.Specialized;
using System.Linq;
using System.Runtime.CompilerServices;
using Microsoft.Maui.Controls;
using Prism.Events;
using NCX.Mobile.Models.Api;
using NCX.Mobile.Events;
using Microsoft.Extensions.DependencyInjection;

namespace NCX.Mobile.Behaviors
{
    /// <summary>
    /// A behavior that synchronizes the selected PowerUnit in a Picker with a FuelStop model.
    /// </summary>
    public class SelectedPowerUnitBehavior : Behavior<Picker>
    {
        /// <summary>
        /// Bindable property for the FuelStop model.
        /// </summary>
        public static readonly BindableProperty FuelStopProperty =
            BindableProperty.Create(
                propertyName: nameof(FuelStop),
                returnType: typeof(FuelStop),
                declaringType: typeof(SelectedPowerUnitBehavior),
                defaultValue: null,
                defaultBindingMode: BindingMode.TwoWay);

        /// <summary>
        /// Gets or sets the FuelStop model.
        /// </summary>
        public FuelStop FuelStop
        {
            get => (FuelStop)GetValue(FuelStopProperty);
            set => SetValue(FuelStopProperty, value);
        }

        private IEventAggregator _eventAggregator;
        private Picker _picker;

        /// <summary>
        /// Called when the behavior is attached to a Picker.
        /// Initializes services, subscribes to events, and attaches to events.
        /// </summary>
        /// <param name="bindable">The Picker to which the behavior is attached.</param>
        protected override void OnAttachedTo(Picker bindable)
        {
            base.OnAttachedTo(bindable);

            _picker = bindable;

            // Resolve IEventAggregator using the service provider
            _eventAggregator = Application.Current?.Handler?.MauiContext?.Services.GetService<IEventAggregator>();

            if (_eventAggregator != null)
            {
                _eventAggregator.GetEvent<ForceRefreshEvent>().Subscribe(OnForceRefreshEventPublished);
            }

            // Subscribe to SelectedIndexChanged event
            bindable.SelectedIndexChanged += Picker_SelectedIndexChanged;

            // Subscribe to ItemsSource collection changes if possible
            if (bindable.ItemsSource is INotifyCollectionChanged incc)
            {
                incc.CollectionChanged += OnCollectionChanged;
            }

            // Initialize selection based on FuelStop
            UpdateSelectedItem();
        }

        /// <summary>
        /// Called when the behavior is detaching from a Picker.
        /// Unsubscribes from events.
        /// </summary>
        /// <param name="bindable">The Picker from which the behavior is detaching.</param>
        protected override void OnDetachingFrom(Picker bindable)
        {
            base.OnDetachingFrom(bindable);

            if (_eventAggregator != null)
            {
                _eventAggregator.GetEvent<ForceRefreshEvent>().Unsubscribe(OnForceRefreshEventPublished);
            }

            // Unsubscribe from SelectedIndexChanged event
            bindable.SelectedIndexChanged -= Picker_SelectedIndexChanged;

            // Unsubscribe from ItemsSource collection changes if possible
            if (bindable.ItemsSource is INotifyCollectionChanged incc)
            {
                incc.CollectionChanged -= OnCollectionChanged;
            }

            _picker = null;
        }

        /// <summary>
        /// Handles the SelectedIndexChanged event of the Picker.
        /// Updates the FuelStop's PowerUnitId based on the selected item.
        /// </summary>
        /// <param name="sender">The Picker that triggered the event.</param>
        /// <param name="e">Event arguments.</param>
        private void Picker_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (FuelStop == null)
                return;

            if (_picker.SelectedItem is PowerUnit selectedPowerUnit)
            {
                FuelStop.PowerUnitId = selectedPowerUnit.Id;
            }
        }

        /// <summary>
        /// Handles the CollectionChanged event of the Picker's ItemsSource.
        /// Sets the selected item based on the FuelStop's PowerUnitId.
        /// </summary>
        /// <param name="sender">The ItemsSource that triggered the event.</param>
        /// <param name="e">Event arguments.</param>
        private void OnCollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            UpdateSelectedItem();
        }

        /// <summary>
        /// Updates the Picker's selected item based on the FuelStop's PowerUnitId.
        /// </summary>
        private void UpdateSelectedItem()
        {
            if (_picker == null || _picker.ItemsSource == null)
                return;

            // Ensure ItemsSource is a collection that can be enumerated
            var items = _picker.ItemsSource.Cast<object>().ToList();
            if (!items.Any())
                return;

            if (FuelStop == null)
                return;

            if (FuelStop.PowerUnitId == 0)
            {
                // Select the first item if PowerUnitId is not set
                _picker.SelectedItem = items.FirstOrDefault();
            }
            else
            {
                // Select the item matching the PowerUnitId
                var selectedItem = items.FirstOrDefault(x => ((PowerUnit)x).Id == FuelStop.PowerUnitId);
                _picker.SelectedItem = selectedItem;
            }
        }

        /// <summary>
        /// Called when the BindingContext changes.
        /// Subscribes to ItemsSource collection changes and initializes selection.
        /// </summary>
        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();

            if (_picker == null)
                return;

            if (_picker.ItemsSource is INotifyCollectionChanged incc)
            {
                incc.CollectionChanged += OnCollectionChanged;
            }

            // Initialize selection based on the new BindingContext
            UpdateSelectedItem();
        }

        /// <summary>
        /// Called when a property is changing.
        /// Unsubscribes from ItemsSource collection changes if BindingContext is changing.
        /// </summary>
        /// <param name="propertyName">The name of the property that is changing.</param>
        protected override void OnPropertyChanging([CallerMemberName] string propertyName = null)
        {
            if (propertyName == nameof(BindingContext))
            {
                if (_picker?.ItemsSource is INotifyCollectionChanged incc)
                {
                    incc.CollectionChanged -= OnCollectionChanged;
                }
            }

            base.OnPropertyChanging(propertyName);
        }

        /// <summary>
        /// Handles the ForceRefreshEvent by forcing the layout of the associated Picker if it is focused.
        /// </summary>
        private void OnForceRefreshEventPublished()
        {
            if (_picker != null && _picker.IsFocused)
            {
                _picker.Dispatcher.Dispatch(() =>
                {
                    // Force a layout refresh by reassigning the current height
                    _picker.HeightRequest = _picker.Height;
                });
            }
        }
    }
}
