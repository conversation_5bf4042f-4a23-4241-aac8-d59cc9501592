﻿using System;
using System.Reflection;
using System.Windows.Input;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Behaviors
{
    /// <summary>
    /// A behavior that binds an event to a command, allowing events to be handled in the ViewModel.
    /// </summary>
    public class EventToCommandBehavior : BehaviorBase<View>
    {
        Delegate eventHandler;

        /// <summary>
        /// The name of the event to subscribe to.
        /// </summary>
        public static readonly BindableProperty EventNameProperty =
            BindableProperty.Create(
                nameof(EventName),
                typeof(string),
                typeof(EventToCommandBehavior),
                null,
                propertyChanged: OnEventNameChanged);

        /// <summary>
        /// The command to execute when the event is fired.
        /// </summary>
        public static readonly BindableProperty CommandProperty =
            BindableProperty.Create(
                nameof(Command),
                typeof(ICommand),
                typeof(EventToCommandBehavior),
                null);

        /// <summary>
        /// The parameter to pass to the command.
        /// </summary>
        public static readonly BindableProperty CommandParameterProperty =
            BindableProperty.Create(
                nameof(CommandParameter),
                typeof(object),
                typeof(EventToCommandBehavior),
                null);

        /// <summary>
        /// An optional converter to convert event arguments to command parameters.
        /// </summary>
        public static readonly BindableProperty InputConverterProperty =
            BindableProperty.Create(
                nameof(Converter),
                typeof(IValueConverter),
                typeof(EventToCommandBehavior),
                null);

        /// <summary>
        /// Gets or sets the name of the event to subscribe to.
        /// </summary>
        public string EventName
        {
            get => (string)GetValue(EventNameProperty);
            set => SetValue(EventNameProperty, value);
        }

        /// <summary>
        /// Gets or sets the command to execute when the event is fired.
        /// </summary>
        public ICommand Command
        {
            get => (ICommand)GetValue(CommandProperty);
            set => SetValue(CommandProperty, value);
        }

        /// <summary>
        /// Gets or sets the parameter to pass to the command.
        /// </summary>
        public object CommandParameter
        {
            get => GetValue(CommandParameterProperty);
            set => SetValue(CommandParameterProperty, value);
        }

        /// <summary>
        /// Gets or sets the converter to convert event arguments to command parameters.
        /// </summary>
        public IValueConverter Converter
        {
            get => (IValueConverter)GetValue(InputConverterProperty);
            set => SetValue(InputConverterProperty, value);
        }

        /// <summary>
        /// Attaches the behavior to the bindable object and registers the event.
        /// </summary>
        /// <param name="bindable">The bindable object.</param>
        protected override void OnAttachedTo(View bindable)
        {
            base.OnAttachedTo(bindable);
            RegisterEvent(EventName);
        }

        /// <summary>
        /// Detaches the behavior from the bindable object and deregisters the event.
        /// </summary>
        /// <param name="bindable">The bindable object.</param>
        protected override void OnDetachingFrom(View bindable)
        {
            base.OnDetachingFrom(bindable);
            DeregisterEvent(EventName);
        }

        /// <summary>
        /// Registers the specified event with the associated object.
        /// </summary>
        /// <param name="name">The name of the event.</param>
        void RegisterEvent(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                return;
            }

            EventInfo eventInfo = AssociatedObject.GetType().GetRuntimeEvent(name);
            if (eventInfo == null)
            {
                throw new ArgumentException($"EventToCommandBehavior: Can't register the '{EventName}' event.");
            }

            MethodInfo methodInfo = typeof(EventToCommandBehavior).GetTypeInfo().GetDeclaredMethod(nameof(OnEvent));
            if (methodInfo == null)
            {
                throw new ArgumentException($"EventToCommandBehavior: Unable to find method '{nameof(OnEvent)}'.");
            }

            eventHandler = methodInfo.CreateDelegate(eventInfo.EventHandlerType, this);
            eventInfo.AddEventHandler(AssociatedObject, eventHandler);
        }

        /// <summary>
        /// Deregisters the specified event from the associated object.
        /// </summary>
        /// <param name="name">The name of the event.</param>
        void DeregisterEvent(string name)
        {
            if (string.IsNullOrWhiteSpace(name) || eventHandler == null)
            {
                return;
            }

            EventInfo eventInfo = AssociatedObject.GetType().GetRuntimeEvent(name);
            if (eventInfo == null)
            {
                throw new ArgumentException($"EventToCommandBehavior: Can't de-register the '{EventName}' event.");
            }

            eventInfo.RemoveEventHandler(AssociatedObject, eventHandler);
            eventHandler = null;
        }

        /// <summary>
        /// Invoked when the specified event is fired.
        /// </summary>
        /// <param name="sender">The event sender.</param>
        /// <param name="eventArgs">The event arguments.</param>
        void OnEvent(object sender, object eventArgs)
        {
            if (Command == null)
            {
                return;
            }

            object resolvedParameter;
            if (CommandParameter != null)
            {
                resolvedParameter = CommandParameter;
            }
            else if (Converter != null)
            {
                resolvedParameter = Converter.Convert(eventArgs, typeof(object), null, null);
            }
            else
            {
                resolvedParameter = eventArgs;
            }

            if (Command.CanExecute(resolvedParameter))
            {
                Command.Execute(resolvedParameter);
            }
        }

        /// <summary>
        /// Handles changes to the EventName property by re-registering the event.
        /// </summary>
        /// <param name="bindable">The bindable object.</param>
        /// <param name="oldValue">The old event name.</param>
        /// <param name="newValue">The new event name.</param>
        static void OnEventNameChanged(BindableObject bindable, object oldValue, object newValue)
        {
            var behavior = (EventToCommandBehavior)bindable;
            if (behavior.AssociatedObject == null)
            {
                return;
            }

            string oldEventName = (string)oldValue;
            string newEventName = (string)newValue;

            behavior.DeregisterEvent(oldEventName);
            behavior.RegisterEvent(newEventName);
        }
    }
}
