﻿using System;
using Microsoft.Maui.Controls;
using Prism.Events;
using NCX.Mobile.Events; // Ensure this namespace is correctly referenced

namespace NCX.Mobile.Behaviors
{
    /// <summary>
    /// A behavior that forces the layout of a Page when a ForceRefreshEvent is published.
    /// </summary>
    public class ForcePageLayoutBehavior : BehaviorBase<Page>
    {
        private readonly IEventAggregator _eventAggregator;

        /// <summary>
        /// Initializes a new instance of the <see cref="ForcePageLayoutBehavior"/> class.
        /// </summary>
        /// <param name="eventAggregator">The Prism event aggregator.</param>
        public ForcePageLayoutBehavior(IEventAggregator eventAggregator)
        {
            _eventAggregator = eventAggregator ?? throw new ArgumentNullException(nameof(eventAggregator));
            _eventAggregator.GetEvent<ForceRefreshEvent>().Subscribe(OnForceRefreshEventPublished);
        }

        /// <summary>
        /// Called when the behavior is detaching from the bindable object.
        /// </summary>
        /// <param name="bindable">The bindable object.</param>
        protected override void OnDetachingFrom(Page bindable)
        {
            base.OnDetachingFrom(bindable);
            _eventAggregator.GetEvent<ForceRefreshEvent>().Unsubscribe(OnForceRefreshEventPublished);
        }

        /// <summary>
        /// Handles the ForceRefreshEvent by forcing the layout if the page is focused.
        /// </summary>
        private void OnForceRefreshEventPublished()
        {
            if (AssociatedObject.IsFocused)
            {
                Device.BeginInvokeOnMainThread(() =>
                {
                    AssociatedObject.ForceLayout();
                });
            }
        }
    }
}
