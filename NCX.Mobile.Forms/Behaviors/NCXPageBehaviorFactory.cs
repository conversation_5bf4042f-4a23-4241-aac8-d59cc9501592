﻿using System;
using Microsoft.Maui.Controls;
using Prism.Behaviors; // Ensure this namespace is correct
using Prism.Events;
using NCX.Mobile.Events;

namespace NCX.Mobile.Behaviors
{
    /// <summary>
    /// A custom behavior factory that applies specific behaviors to ContentPage instances.
    /// </summary>
    public class NCXPageBehaviorFactory : IPageBehaviorFactory
    {
        private readonly IEventAggregator _eventAggregator;

        /// <summary>
        /// Initializes a new instance of the <see cref="NCXPageBehaviorFactory"/> class.
        /// Subscribes to the ForceRefreshEvent.
        /// </summary>
        /// <param name="eventAggregator">The Prism event aggregator.</param>
        public NCXPageBehaviorFactory(IEventAggregator eventAggregator)
        {
            _eventAggregator = eventAggregator ?? throw new ArgumentNullException(nameof(eventAggregator));
            _eventAggregator.GetEvent<ForceRefreshEvent>().Subscribe(OnForceRefreshEventPublished);
        }

        /// <summary>
        /// Applies content page behaviors by adding the ForcePageLayoutBehavior.
        /// </summary>
        /// <param name="page">The ContentPage to which behaviors are applied.</param>
        public void ApplyPageBehaviors(Page page)
        {
            if (page == null)
                throw new ArgumentNullException(nameof(page));

            page.Behaviors.Add(new ForcePageLayoutBehavior(_eventAggregator));
        }

        /// <summary>
        /// Handles the ForceRefreshEvent by forcing the layout of the associated ContentPage if it is focused.
        /// </summary>
        private void OnForceRefreshEventPublished()
        {
            // Implement logic to force layout on relevant pages
            // This may require tracking pages or using navigation events
        }
    }
}
