﻿using NCX.Mobile.Converters;
using Microsoft.Maui.Controls;
using System;

namespace NCX.Mobile.Behaviors
{
    /// <summary>
    /// A behavior that binds the ListView's ItemTapped event to a command and ensures items are deselected after tapping.
    /// </summary>
    public class ListViewSelectedItemBehavior : EventToCommandBehavior
    {
        /// <summary>
        /// Static converter instance to convert event arguments to command parameters.
        /// </summary>
        public static ListViewItemTappedCommandParameterConverter ListViewItemTappedCommandParameterConverter { get; }

        // Static constructor to initialize the converter
        static ListViewSelectedItemBehavior()
        {
            ListViewItemTappedCommandParameterConverter = new ListViewItemTappedCommandParameterConverter();
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ListViewSelectedItemBehavior"/> class.
        /// Sets the event name and converter for the behavior.
        /// </summary>
        public ListViewSelectedItemBehavior()
        {
            EventName = nameof(ListView.ItemTapped);
            Converter = ListViewItemTappedCommandParameterConverter;
        }

        /// <summary>
        /// Attaches the behavior to the bindable object and subscribes to the ItemSelected event.
        /// </summary>
        /// <param name="bindable">The bindable object (ListView).</param>
        protected override void OnAttachedTo(BindableObject bindable)
        {
            base.OnAttachedTo(bindable);

            if (bindable is ListView listView)
            {
                listView.ItemSelected += ListViewSelectedItemBehavior_ItemSelected;
            }
            else
            {
                throw new ArgumentException($"ListViewSelectedItemBehavior can only be attached to ListView, but was attached to {bindable.GetType()}");
            }
        }

        /// <summary>
        /// Detaches the behavior from the bindable object and unsubscribes from the ItemSelected event.
        /// </summary>
        /// <param name="bindable">The bindable object (ListView).</param>
        protected override void OnDetachingFrom(BindableObject bindable)
        {
            base.OnDetachingFrom(bindable);

            if (bindable is ListView listView)
            {
                listView.ItemSelected -= ListViewSelectedItemBehavior_ItemSelected;
            }
        }

        /// <summary>
        /// Handles the ItemSelected event to deselect the item.
        /// </summary>
        /// <param name="sender">The ListView that triggered the event.</param>
        /// <param name="e">The event arguments.</param>
        private void ListViewSelectedItemBehavior_ItemSelected(object sender, SelectedItemChangedEventArgs e)
        {
            if (e.SelectedItem == null)
            {
                return;
            }

            if (sender is ListView listView)
            {
                listView.SelectedItem = null;
            }
        }
    }
}
