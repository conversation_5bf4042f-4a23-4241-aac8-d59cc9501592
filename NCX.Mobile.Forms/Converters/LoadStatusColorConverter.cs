﻿using NCX.Mobile.Models;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Graphics;

namespace NCX.Mobile.Converters
{
    public class LoadStatusColorConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not LoadStatus status)
                return null;

            switch (status)
            {
                case LoadStatus.Pending:
                case LoadStatus.Viewed:
                    return (Color)Application.Current.Resources["StatusPendingColor"];
                default:
                    return (Color)Application.Current.Resources["StatusEnRouteColor"];
            }
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
