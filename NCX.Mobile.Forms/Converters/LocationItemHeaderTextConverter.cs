﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using NCX.Mobile.Models;
using NCX.Mobile.Properties;

namespace NCX.Mobile.Converters
{
    public class LocationItemHeaderTextConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            switch (value)
            {
                case null:
                    return string.Empty;
                case LoadOperationType loadOperationType:
                    return loadOperationType == LoadOperationType.Pickup
                        ? Resources.LoadPickupDetailsText
                        : Resources.LoadDropoffDetailsText;
                case LoadLocationItemViewModel item:
                    return string.Format(Resources.CargoItemHeaderFormatText, item.ItemPosition + 1, item.ItemTotalCount);
                default:
                    return string.Empty;
            }
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
