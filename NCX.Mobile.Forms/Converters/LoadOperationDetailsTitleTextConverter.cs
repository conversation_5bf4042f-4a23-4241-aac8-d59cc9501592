﻿using NCX.Mobile.Models;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using NCX.Mobile.Properties;

namespace NCX.Mobile.Converters
{
    public class LoadOperationDetailsTitleTextConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not LoadOperationType loadOperationType)
                throw new ArgumentException($"Unexpected value of {nameof(value)}");

            switch (loadOperationType)
            {
                case LoadOperationType.Pickup:
                    return Resources.LoadPickupDetailsText;
                case LoadOperationType.Dropoff:
                    return Resources.LoadDropoffDetailsText;
                default:
                    throw new ArgumentException($"Unexpected value of {nameof(value)}");
            }
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
