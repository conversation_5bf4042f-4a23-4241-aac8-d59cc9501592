﻿using System;
using System.Collections;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Graphics;

namespace NCX.Mobile.Converters
{
    public class ListViewAlternateBkgndColorConverter : IValueConverter
    {
        static readonly Lazy<Color> RowAlternateColor = new Lazy<Color>(() =>
        {
            if (Application.Current?.Resources.TryGetValue("RowAlternateColor", out var colorObj) == true && colorObj is Color color)
                return color;
            return Colors.LightGray; // fallback color if not defined
        });

        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value == null || parameter is not ListView listView || listView.ItemsSource is not IList list)
            {
                return Colors.White;
            }

            int index = list.IndexOf(value);
            return (index % 2 == 0) ? Colors.White : RowAlternateColor.Value;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
