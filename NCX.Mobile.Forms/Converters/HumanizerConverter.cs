﻿using Humanizer;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class HumanizerConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            return value?.ToString().Humanize();
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            return value?.ToString().DehumanizeTo(targetType);
        }
    }
}
