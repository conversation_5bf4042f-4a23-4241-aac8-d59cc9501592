﻿using NCX.Mobile.Models;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Graphics;

namespace NCX.Mobile.Converters
{
    public class LocationStatusBtnBkgndColorConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is LoadOperationStatus status && parameter is LoadOperationStatus paramStatus)
            {
                return status == paramStatus
                    ? (Color)Application.Current.Resources["BtnFormColor"]
                    : (Color)Application.Current.Resources["BtnDisabledColor"];
            }
            return null;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
