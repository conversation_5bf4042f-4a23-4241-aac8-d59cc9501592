﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class LocationOperatingHoursDayConverter : IValueConverter
    {
        static readonly DateTime _DateTimeBase = new DateTime(2017, 7, 31, 0, 0, 0);

        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is int days)
            {
                return _DateTimeBase.AddDays(days);
            }
            throw new ArgumentException("Expected value to be an integer representing days.");
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
