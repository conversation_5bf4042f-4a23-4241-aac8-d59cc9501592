﻿using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class SelectLocationToUpdateHeaderConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value == null)
                return null;

            return ((LoadOperationType)value) == LoadOperationType.Pickup
                ? Resources.SelectLocationToUpdatePickupHeader
                : Resources.SelectLocationToUpdateDropoffHeader;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
