﻿using System;
using System.Collections.Generic;
using System.Globalization;
using Microsoft.Maui.Controls;
using NCX.Mobile.Properties;

namespace NCX.Mobile.Converters
{
    public class CargoUnitTypeConverter : IValueConverter
    {
        private Dictionary<string, string>? _unitTypeToDesc;
        private CultureInfo? _currCulture;

        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (_currCulture == null || !_currCulture.Equals(culture))
            {
                _currCulture = culture;
                _unitTypeToDesc = CreateDictionary();
            }

            if (value is not string key)
                return value;

            return (_unitTypeToDesc != null && _unitTypeToDesc.TryGetValue(key, out string? desc)) ? desc : value;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        private static Dictionary<string, string> CreateDictionary()
        {
            return new Dictionary<string, string>
            {
                { "bag", Resources.CargoUnitBag },
                { "binCardboard", Resources.CargoUnitBinCardboard },
                { "binPlastic", Resources.CargoUnitBinPlastic },
                { "box", Resources.CargoUnitBox },
                { "bulk", Resources.CargoUnitBulk },
                { "carton", Resources.CargoUnitCarton },
                { "crate", Resources.CargoUnitCrate },
                { "container", Resources.CargoUnitContainer },
                { "rpc", Resources.CargoUnitRpc },
                { "tote", Resources.CargoUnitTote }
                // Add more key-value pairs here
            };
        }
    }
}
