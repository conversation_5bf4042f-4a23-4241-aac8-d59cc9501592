﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using NCX.Mobile.Models;
using NCX.Mobile.Properties;

namespace NCX.Mobile.Converters
{
    public class PhotoViewerStatusTextConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            var viewModel = value as BrowsableItemCollectionViewModel<PODItem>;

            if (viewModel == null || viewModel.CurrentPosition == null)
            {
                return null;
            }

            return string.Format(Resources.PhotoViewerStatusText, viewModel.CurrentPosition + 1, viewModel.ItemCount);
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
