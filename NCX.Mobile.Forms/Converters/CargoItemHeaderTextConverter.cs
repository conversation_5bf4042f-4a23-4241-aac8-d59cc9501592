﻿using NCX.Mobile.Helpers;
using System;
using System.Collections;
using System.Globalization;
using Microsoft.Maui.Controls;
using NCX.Mobile.Properties;
using System.Linq;
using NCX.Mobile.Models;
using System.Collections.Generic;

namespace NCX.Mobile.Converters
{
    public class CargoItemHeaderTextConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value == null)
            {
                return string.Empty;
            }

            if (value is bool b)
            {
                return b ? Resources.CargoDetailsText : Resources.CargoDestinationDetailsText;
            }
            else
            {
                var bindable = parameter as BindableObject;
                var itemsSource = bindable != null ? BindableLayout.GetItemsSource(bindable) : null;

                IList cargoList;
                if (!(itemsSource is IList list))
                {
                    cargoList = itemsSource?.Cast<object>().ToList() ?? new List<object>();
                }
                else
                {
                    cargoList = list;
                }

                if (value is not CargoItemViewModel cargoItemViewModel)
                {
                    return string.Empty;
                }

                var castedList = cargoList.Cast<CargoItemViewModel>();
                var foundItem = castedList.First(x => x.Id == cargoItemViewModel.Id);
                var currentIndex = cargoList.IndexOf(foundItem);

                return string.Format(Resources.CargoItemHeaderFormatText, currentIndex + 1, cargoList.Count);
            }
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
