﻿using NCX.Mobile.Models;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class LoadOperationStatusIconConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value == null)
                return null;

            var loadOperation = (LoadOperation)value;

            if (loadOperation.SourceType == LoadOperationType.Pickup)
            {
                switch (loadOperation.Status)
                {
                    case LoadOperationStatus.Pending:
                        return "pickup_pending";
                    case LoadOperationStatus.En_Route:
                    case LoadOperationStatus.Checked_In:
                        return "pickup_in_process";
                    case LoadOperationStatus.Completed:
                        return "pickup_completed";
                    case LoadOperationStatus.Canceled:
                        return "pickup_canceled";
                    default:
                        return string.Empty;
                }
            }
            else
            {
                switch (loadOperation.Status)
                {
                    case LoadOperationStatus.Pending:
                        return "dropoff_pending";
                    case LoadOperationStatus.En_Route:
                    case LoadOperationStatus.Checked_In:
                        return "dropoff_in_process";
                    case LoadOperationStatus.Completed:
                        return "dropoff_completed";
                    case LoadOperationStatus.Canceled:
                        return "dropoff_canceled";
                    default:
                        return string.Empty;
                }
            }
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
