﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Microsoft.Maui.Controls;
using NCX.Mobile.Models;
using NCX.Mobile.Properties;

namespace NCX.Mobile.Converters
{
    public class FirstLoadLocationSourceTypeConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is IEnumerable<LoadLocationItemViewModel> locations && locations.Any())
            {
                return locations.First().LoadOperation.SourceType == LoadOperationType.Pickup
                    ? Resources.SelectLocationToUpdatePickupHeader
                    : Resources.SelectLocationToUpdateDropoffHeader;
            }
            return null;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
