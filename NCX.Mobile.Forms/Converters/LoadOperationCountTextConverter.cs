﻿using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class LoadOperationCountTextConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (parameter == null)
                return string.Empty;

            if (!(parameter is LoadOperationType loadOperationType))
                throw new ArgumentException($"Unexpected type of {nameof(parameter)}");

            int count = value is int i ? i : 0;

            switch (loadOperationType)
            {
                case LoadOperationType.Pickup:
                    return count > 1
                        ? string.Format(Resources.PickUpCountPlural, count)
                        : string.Format(Resources.PickUpCountSingular, count);
                case LoadOperationType.Dropoff:
                    return count > 1
                        ? string.Format(Resources.DropoffCountPlural, count)
                        : string.Format(Resources.DropoffCountSingular, count);
                default:
                    throw new ArgumentException($"Unexpected value of {nameof(parameter)}");
            }
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
