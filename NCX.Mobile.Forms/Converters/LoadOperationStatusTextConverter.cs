﻿using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class LoadOperationStatusTextConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not LoadOperationStatus status)
                throw new ArgumentException($"Unexpected value of {nameof(value)}");

            switch (status)
            {
                case LoadOperationStatus.Pending:
                    return Resources.PendingText;
                case LoadOperationStatus.En_Route:
                    return Resources.EnRouteText;
                case LoadOperationStatus.Checked_In:
                    return Resources.CheckedInText;
                case LoadOperationStatus.Completed:
                    return Resources.CompletedText;
                case LoadOperationStatus.Canceled:
                    return Resources.CanceledText;
                case LoadOperationStatus.Deleted:
                    return Resources.DeletedText;
                default:
                    throw new ArgumentException($"Unexpected value of {nameof(value)}");
            }
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
