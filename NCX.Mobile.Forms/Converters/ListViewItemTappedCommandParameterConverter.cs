﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    /// <summary>
    /// Converts the ItemTappedEventArgs to the tapped item, allowing the command to receive the item as a parameter.
    /// </summary>
    public class ListViewItemTappedCommandParameterConverter : IValueConverter
    {
        /// <summary>
        /// Converts the ItemTappedEventArgs to the tapped item.
        /// </summary>
        /// <param name="value">The event arguments from the ItemTapped event.</param>
        /// <param name="targetType">The target binding type.</param>
        /// <param name="parameter">An optional parameter to pass to the converter.</param>
        /// <param name="culture">The culture to use in the converter.</param>
        /// <returns>The tapped item if available; otherwise, null.</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ItemTappedEventArgs tappedEventArgs)
            {
                return tappedEventArgs.Item;
            }

            return null;
        }

        /// <summary>
        /// Not implemented. Converts back from the target to the source.
        /// </summary>
        /// <param name="value">The value produced by the binding target.</param>
        /// <param name="targetType">The target binding type.</param>
        /// <param name="parameter">An optional parameter to pass to the converter.</param>
        /// <param name="culture">The culture to use in the converter.</param>
        /// <returns>Nothing. Always throws a NotImplementedException.</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
