﻿using System;
using System.Collections;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class ObjectToBoolConverter : IValueConverter
    {
        public virtual object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value == null)
            {
                return false;
            }

            if (value is string str)
            {
                return !string.IsNullOrWhiteSpace(str);
            }
            if (value is bool boolVal)
            {
                return boolVal;
            }
            else if (value is int intVal)
            {
                return intVal > 0;
            }
            else if (value is IList list && (parameter as string) != "TrueIfEmpty")
            {
                return list.Count > 0;
            }
            else if (value is decimal decimalVal)
            {
                return decimalVal > 0;
            }
            else if (value is Enum)
            {
                if (parameter != null)
                {
                    try
                    {
                        return value.Equals(Enum.Parse(targetType, parameter as string));
                    }
                    catch
                    {
                        return false;
                    }
                }
                return true;
            }
            else
            {
                return true;
            }
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
