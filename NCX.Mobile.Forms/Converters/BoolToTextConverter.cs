﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using NCX.Mobile.Properties;

namespace NCX.Mobile.Converters
{
    public class BoolToTextConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is bool b)
            {
                return b ? Resources.YesText : Resources.NoText;
            }
            return null;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
