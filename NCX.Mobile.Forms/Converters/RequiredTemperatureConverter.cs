﻿using NCX.Mobile.Models;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class RequiredTemperatureConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value == null)
                return null;

            if (value is not Haul haul)
                return null;

            string temperatureTypeSymbol = haul.TemperatureType == "c" ? "°C" : "°F";
            return $"{haul.RequiredTemperature} {temperatureTypeSymbol}";
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
