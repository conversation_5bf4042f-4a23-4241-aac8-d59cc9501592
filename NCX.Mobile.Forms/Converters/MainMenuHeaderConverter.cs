﻿using NCX.Mobile.Properties;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class MainMenuHeaderConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is string str)
            {
                return string.Format(Resources.MainMenuHeaderFormat, str);
            }
            return null;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
