﻿using NCX.Mobile.Models;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Devices.Sensors;

namespace NCX.Mobile.Converters
{
    public class LoadLocationAddressToMapPositionConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is OrganizationAddress address)
            {
                return new Microsoft.Maui.Devices.Sensors.Location(address.Latitude, address.Longitude);
            }
            return null;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
