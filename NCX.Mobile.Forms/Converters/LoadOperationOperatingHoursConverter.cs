﻿using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class LoadOperationOperatingHoursConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value == null)
            {
                return string.Empty;
            }

            var hours = (LocationOperatingHour)value;
            return hours.AllDay
                ? Resources.AllDayHoursOfOperation
                : $"{hours.Open.ToString("t", culture)} - {hours.Close.ToString("t", culture)}";
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
