﻿using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class LoadDelayReasonToTextConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value == null) return null;

            switch ((LoadDelayReason)value)
            {
                case LoadDelayReason.Flat_Tire:
                    return Resources.DelayReasonFlatTireText;
                case LoadDelayReason.Road_Closure:
                    return Resources.DelayReasonRoadClosureText;
                case LoadDelayReason.Heavy_Traffic:
                    return Resources.DelayReasonHeavyTrafficText;
                case LoadDelayReason.Bad_Weather:
                    return Resources.DelayReasonBadWeatherText;
                case LoadDelayReason.Minor_Breakdown:
                    return Resources.DelayReasonBreakdownText;
                case LoadDelayReason.Minor_Accident:
                    return Resources.DelayReasonAccidentText;
                default:
                    return null;
            }
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
