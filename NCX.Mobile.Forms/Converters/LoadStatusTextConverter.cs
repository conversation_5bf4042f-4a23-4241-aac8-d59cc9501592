﻿using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class LoadStatusTextConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not LoadStatus status)
                return null;

            switch (status)
            {
                case LoadStatus.Setup:
                case LoadStatus.Pending:
                case LoadStatus.Viewed:
                    return Resources.PendingText;
                case LoadStatus.En_Route:
                    return Resources.EnRouteText;
                case LoadStatus.Checked_In:
                    return Resources.CheckedInText;
                case LoadStatus.Completed:
                    return Resources.CompletedText;
                case LoadStatus.Canceled:
                    return Resources.CanceledText;
                case LoadStatus.Deleted:
                    return Resources.DeletedText;
                default:
                    return nameof(value);
            }
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
