﻿using NCX.Mobile.Models;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class LoadOperationTypeToIconConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not LoadOperationType loadOperationType)
                throw new ArgumentException($"Unexpected value of {nameof(value)}");

            switch (loadOperationType)
            {
                case LoadOperationType.Pickup:
                    return Application.Current.Resources["PickupIconStyle"] as Style;
                case LoadOperationType.Dropoff:
                    return Application.Current.Resources["DropoffIconStyle"] as Style;
                default:
                    throw new ArgumentException($"Unexpected value of {nameof(value)}");
            }
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
