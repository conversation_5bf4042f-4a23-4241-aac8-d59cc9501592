﻿using NCX.Mobile.Models;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Graphics;

namespace NCX.Mobile.Converters
{
    public class LoadOperationTypeToColorConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not LoadOperationType loadOperationType)
                throw new ArgumentException($"Unexpected value of {nameof(value)}");

            switch (loadOperationType)
            {
                case LoadOperationType.Pickup:
                    return (Color)Application.Current.Resources["LoadOperationPickupStatusColor"];
                case LoadOperationType.Dropoff:
                    return (Color)Application.Current.Resources["LoadOperationDropoffStatusColor"];
                default:
                    throw new ArgumentException($"Unexpected value of {nameof(value)}");
            }
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
