﻿using NCX.Mobile.Properties;
using System;
using System.Collections.Generic;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class EquipmentTypeConverter : IValueConverter
    {
        private Dictionary<string, string>? _equipmentTypeToDesc;
        private CultureInfo? _currCulture;

        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (_currCulture == null || !_currCulture.Equals(culture))
            {
                _currCulture = culture;
                _equipmentTypeToDesc = CreateDictionary();
            }

            if (value is not string key)
                return value;

            return (_equipmentTypeToDesc != null && _equipmentTypeToDesc.TryGetValue(key, out string? desc)) ? desc : value;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        private static Dictionary<string, string> CreateDictionary()
        {
            return new Dictionary<string, string>
            {
                { "refrigeratedVan",  Resources.EquipmentRefrigeratedVan },
                { "dryVan",  Resources.EquipmentDryVan },
                { "ventilatedVan",  Resources.EquipmentVentilatedVan },
                { "airRideVan",  Resources.EquipmentAirRideVan },
                { "vanwithCurtains",  Resources.EquipmentVanwithCurtains },
                { "flatbed",  Resources.EquipmentFlatbed },
                { "flatbedwithSides",  Resources.EquipmentFlatbedwithSides },
                { "flatbedwithTarps",  Resources.EquipmentFlatbedwithTarps },
                { "flatbedStretch",  Resources.EquipmentFlatbedStretch },
                { "flatbedSideKit",  Resources.EquipmentFlatbedSideKit },
                { "autoCarrier",  Resources.EquipmentAutoCarrier },
                { "beverageTrailer",  Resources.EquipmentBeverageTrailer },
                { "intermodal20Container",  Resources.EquipmentIntermodal20Container },
                { "intermodal40Container",  Resources.EquipmentIntermodal40Container },
                { "livestockTrailerCattle",  Resources.EquipmentLivestockTrailerCattle },
                { "livestockTrailerHogs",  Resources.EquipmentLivestockTrailerHogs },
                { "livestockTrailerChickens",  Resources.EquipmentLivestockTrailerChickens },
                { "loggerTrailer",  Resources.EquipmentLoggerTrailer },
                { "boatTrailerSingle",  Resources.EquipmentBoatTrailerSingle },
                { "boatTrailerDouble",  Resources.EquipmentBoatTrailerDouble },
                { "removableGooseneckRGN",  Resources.EquipmentRemovableGooseneckRGN },
                { "conestogaTrailer",  Resources.EquipmentConestogaTrailer },
                { "doubleDrop",  Resources.EquipmentDoubleDrop },
                { "doubleDropStretch",  Resources.EquipmentDoubleDropStretch },
                { "doubleDropExtendable",  Resources.EquipmentDoubleDropExtendable },
                { "doubleDropSideKit",  Resources.EquipmentDoubleDropSideKit },
                { "bTrainCombo",  Resources.EquipmentBTrainCombo },
                { "singleDrop",  Resources.EquipmentSingleDrop },
                { "singleDropStretch",  Resources.EquipmentSingleDropStretch },
                { "singleDropExtendable",  Resources.EquipmentSingleDropExtendable },
                { "singleDropSideKit",  Resources.EquipmentSingleDropSideKit },
                { "dumpTrailer",  Resources.EquipmentDumpTrailer },
                { "endDumpTrailer",  Resources.EquipmentEndDumpTrailer },
                { "halfRoundEndDumpTrailer",  Resources.EquipmentHalfRoundEndDumpTrailer },
                { "bottomDumpTrailer",  Resources.EquipmentBottomDumpTrailer },
                { "fuelTankSingle",  Resources.EquipmentFuelTankSingle },
                { "fuelTankDouble",  Resources.EquipmentFuelTankDouble },
                { "hopperGrainSingle",  Resources.EquipmentHopperGrainSingle },
                { "hopperGrainDouble",  Resources.EquipmentHopperGrainDouble },
                { "hopperSingle",  Resources.EquipmentHopperSingle },
                { "hopperDouble",  Resources.EquipmentHopperDouble },
                { "liveFloor",  Resources.EquipmentLiveFloor },
                { "saddlemount",  Resources.EquipmentSaddlemount },
                { "pneumatic",  Resources.EquipmentPneumatic },
                { "lowboy",  Resources.EquipmentLowboy },
                { "maxiCube",  Resources.EquipmentMaxiCube },
                { "powerUnitOnly",  Resources.EquipmentPowerUnitOnly },
                { "fracTankSquare",  Resources.EquipmentFracTankSquare },
                { "fracTankRoundBottom",  Resources.EquipmentFracTankRoundBottom },
                { "tankerFoodGrade",  Resources.EquipmentTankerFoodGrade },
                { "tankerWater",  Resources.EquipmentTankerWater },
                { "tankerVacuum",  Resources.EquipmentTankerVacuum },
                { "tankerPetroleum",  Resources.EquipmentTankerPetroleum },
                { "tankerChemical",  Resources.EquipmentTankerChemical },
                { "specializedTrailer",  Resources.EquipmentSpecializedTrailer }
                // Add more key-value pairs here
            };
        }
    }
}
