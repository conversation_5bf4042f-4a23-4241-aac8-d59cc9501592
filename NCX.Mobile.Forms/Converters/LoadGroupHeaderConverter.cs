﻿using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using System;
using System.Globalization;
using System.Linq;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class LoadGroupHeaderConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value == null)
                return null;

            var loadItemGroup = (LoadItemGroup)value;

            if (loadItemGroup.LoadStatus == LoadStatus.Pending || loadItemGroup.LoadStatus == LoadStatus.Viewed)
            {
                return loadItemGroup.Count() == 1 ? Resources.PendingLoadHeader : Resources.PendingLoadsHeader;
            }
            else
            {
                return Resources.ActiveLoadHeader;
            }
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
