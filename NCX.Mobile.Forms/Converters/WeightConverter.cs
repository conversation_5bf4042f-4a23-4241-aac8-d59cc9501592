﻿using System;
using System.Globalization;
using NCX.Mobile.Models;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class CargoItemWeightConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            var cargoItem = value as CargoItemViewModel;

            if (cargoItem == null)
            {
                return null;
            }

            return $"{cargoItem.UnitWeight.ToString("0,0.##", culture)} {cargoItem.WeightType}";
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
