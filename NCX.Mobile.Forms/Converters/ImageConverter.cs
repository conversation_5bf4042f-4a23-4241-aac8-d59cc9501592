﻿using System;
using System.Globalization;
using System.IO;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class ImageConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            switch (value)
            {
                case string str:
                    return FromData(System.Convert.FromBase64String(str));
                case byte[] data:
                    return FromData(data);
                default:
                    return null;
            }
        }

        private ImageSource FromData(byte[] data) =>
            ImageSource.FromStream(() => new MemoryStream(data));

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
