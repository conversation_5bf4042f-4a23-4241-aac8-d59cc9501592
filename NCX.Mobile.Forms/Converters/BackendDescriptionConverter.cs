﻿using NCX.Mobile.Helpers;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class BackendDescriptionConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            string? apiHost = value as string;
            if (string.IsNullOrEmpty(apiHost))
                return null;

            switch (apiHost.ToLowerInvariant())
            {
                case Constants.HostDev:
                    return "DEV SERVER";
                case Constants.HostQA3:
                    return "QA3 SERVER";
                case Constants.HostQA4:
                    return "QA4 SERVER";
                case Constants.HostProd:
                    return string.Empty;
                default:
                    return "CUSTOM SERVER";
            }
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
