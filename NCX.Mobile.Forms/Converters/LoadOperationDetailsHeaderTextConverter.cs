﻿using NCX.Mobile.Models;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using NCX.Mobile.Properties;

namespace NCX.Mobile.Converters
{
    public class LoadOperationDetailsHeaderTextConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not LoadOperationType loadOperationType)
                throw new ArgumentException($"Unexpected value of {nameof(value)}");

            return loadOperationType switch
            {
                LoadOperationType.Pickup => Resources.PickupLocationText,
                LoadOperationType.Dropoff => Resources.DropOffLocationText,
                _ => throw new ArgumentException($"Unexpected value of {nameof(value)}")
            };
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
