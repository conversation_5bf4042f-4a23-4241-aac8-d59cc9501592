﻿using NCX.Mobile.Models;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Maps;

namespace NCX.Mobile.Converters
{
    public class LoadLocationAddressToMapPinsConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not NCX.Mobile.Models.Location location)
                return null;

            return new Pin[]
            {
                new Pin
                {
                    Location = new Microsoft.Maui.Devices.Sensors.Location(location.Address.Latitude, location.Address.Longitude),
                    Label = location.CompanyName
                }
            };
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
