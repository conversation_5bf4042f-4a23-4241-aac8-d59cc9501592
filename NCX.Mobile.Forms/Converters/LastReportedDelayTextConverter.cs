﻿using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Converters
{
    public class LastReportedDelayTextConverter : IValueConverter
    {
        static Lazy<LoadDelayReasonToTextConverter> LoadDelayReasonToTextConverter =
            new Lazy<LoadDelayReasonToTextConverter>(() => new LoadDelayReasonToTextConverter());

        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value != null)
            {
                var loadDelay = (LoadDelay)value;
                LoadDelayReason reason = loadDelay.Reason;
                object? reasonText = LoadDelayReasonToTextConverter.Value.Convert(reason, typeof(string), null, culture);
                return string.Format(Resources.LastReportedDelayText, reasonText);
            }
            return null;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
