﻿using NCX.Mobile.Models;
using System.Linq;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Controls
{
    public class MainMenuItemSelector : DataTemplateSelector
    {
        public DataTemplate MenuItemTemplate { get; set; }
        public DataTemplate DropdownMenuItemTemplate { get; set; }
        public DataTemplate DropdownChildMenuItemTemplate { get; set; }

        protected override DataTemplate OnSelectTemplate(object item, BindableObject container)
        {
            var menuItem = (MainMenuItem)item;

            if (menuItem.IsChild)
            {
                return DropdownChildMenuItemTemplate;
            }
            else
            {
                return menuItem.Count() == 0 ? MenuItemTemplate : DropdownMenuItemTemplate;
            }
        }
    }
}
