﻿using Microsoft.Maui.Controls;
using NCX.Mobile.Controls.Common;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;

namespace NCX.Mobile.Controls
{
    public class DropdownListViewItemView : ContentControl
    {
        Label _arrowIcon;
        bool _isOpen;

        protected override void OnChildAdded(Element child)
        {
            base.OnChildAdded(child);

            _arrowIcon = ((View)child).FindByName<Label>("_arrowIcon");

            GestureRecognizers.Add(new TapGestureRecognizer
            {
                Command = new Command(o => OnTapped())
            });
        }

        void OnTapped()
        {
            var itemsSource = GetItemsSource();
            if (itemsSource == null)
            {
                // Log or handle the case where the items source isn't available
                System.Diagnostics.Debug.WriteLine("Failed to get FlatHierachyItemsDataSource.");
                return;
            }

            _isOpen = !_isOpen;
            _arrowIcon.Text = _isOpen ? FontIcons.AngleDown : FontIcons.AngleRight;

            if (_isOpen)
            {
                itemsSource.Expand((MainMenuItem)BindingContext);
            }
            else
            {
                itemsSource.Collapse((MainMenuItem)BindingContext);
            }
        }

        FlatHierachyItemsDataSource<MainMenuItem> GetItemsSource()
        {
            var listView = GetParentListView();
            if (listView == null)
            {
                System.Diagnostics.Debug.WriteLine("Parent ListView not found.");
                return null;
            }

            var itemsSource = listView.ItemsSource;
            if (itemsSource is FlatHierachyItemsDataSource<MainMenuItem> flatSource)
            {
                return flatSource;
            }

            // Debug the actual type if the cast fails
            System.Diagnostics.Debug.WriteLine($"ItemsSource type mismatch. Expected: FlatHierachyItemsDataSource<MainMenuItem>, Actual: {itemsSource?.GetType().FullName}");
            return null;
        }

        ListView GetParentListView()
        {
            Element current = this;
            while (current != null)
            {
                if (current is ListView listView)
                {
                    return listView;
                }
                current = current.Parent;
            }
            System.Diagnostics.Debug.WriteLine("No ListView found in parent hierarchy.");
            return null;
        }
    }
}