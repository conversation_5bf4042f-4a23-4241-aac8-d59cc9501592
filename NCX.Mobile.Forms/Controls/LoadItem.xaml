﻿<?xml version="1.0" encoding="UTF-8"?>
<StackLayout xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:ncx="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             x:Name="this"
             x:Class="NCX.Mobile.Controls.LoadItem">

    <!-- Header -->
    <StackLayout Orientation="Horizontal" BindingContext="{x:Reference this}">
        <StackLayout HorizontalOptions="FillAndExpand">
            <!-- Status -->
            <Label Text="{Binding Load.Status, Converter={StaticResource LoadStatusTextConverter}}"
                   TextColor="{Binding Load.Status, Converter={StaticResource LoadStatusColorConverter}}"
                   IsVisible="{Binding Load.Status, Converter={StaticResource LoadInfoStatusVisibilityConverter}}"
                   VerticalOptions="Center"
                   FontSize="Medium" />
            <!-- Load Id-->
            <Label Text="{Binding Load.Eid}" FontSize="Medium" />
        </StackLayout>

        <!-- Details button -->
        <Button Style="{StaticResource ButtonHyperlinkStyle}"
                Text="{Binding Source={x:Static ncx:Resources.ShowDetails}}"
                HorizontalOptions="End"
                VerticalOptions="Start"
                IsVisible="{Binding ShowLoadDetailsCommand, Converter={StaticResource ObjectToBoolConverter}}"
                Command="{Binding ShowLoadDetailsCommand}"
                CommandParameter="{Binding Haul}"
                Margin="0, -10, 0, 0"/>
    </StackLayout>

    <!-- First pickup and last drop-off info -->
    <Grid ColumnSpacing="0" Margin="0, 10, 0, 0" BindingContext="{x:Reference this}">
        <Grid.ColumnDefinitions>
            <ColumnDefinition />
            <ColumnDefinition />
        </Grid.ColumnDefinitions>
        <!-- First pickup info -->
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <Label Style="{StaticResource PickupIconStyle}" FontSize="16" />
            <StackLayout Spacing="0" BindingContext="{Binding FirstPickup}" Margin="10, 0, 0, 0" Grid.Column="1">
                <Label Text="{Binding Source={x:Static ncx:Resources.FirstPickup}}"
                       FontSize="Micro"
                       TextColor="{StaticResource LightTextColor}" />
                <Label Text="{Binding Location.CompanyName}" FontSize="Medium" />
                <Label Text="{Binding Location.Address, Converter={StaticResource OperationAddressConverter}}"
                       TextColor="{StaticResource LightTextColor}" FontSize="Small" />
                <Label Text="{Binding ExpectedDate, StringFormat='{}{0:MMM d, yyyy}'}"
                       TextColor="{StaticResource LightTextColor}" FontSize="Small" />
            </StackLayout>
        </Grid>

        <!-- Last dropoff info -->
        <Grid Grid.Column="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <Label Style="{StaticResource DropoffIconStyle}" FontSize="16" />
            <StackLayout Spacing="0" BindingContext="{Binding LastDropoff}" Margin="10, 0, 0, 0" Grid.Column="1">
                <Label Text="{Binding Source={x:Static ncx:Resources.FinalDropOff}}"
                       FontSize="Micro"
                       TextColor="{StaticResource LightTextColor}" />
                <Label Text="{Binding Location.CompanyName}" FontSize="Medium" />
                <Label Text="{Binding Location.Address, Converter={StaticResource OperationAddressConverter}}"
                       TextColor="{StaticResource LightTextColor}" FontSize="Small" />
                <Label Text="{Binding ExpectedDate, StringFormat='{}{0:MMM d, yyyy}'}"
                       TextColor="{StaticResource LightTextColor}" FontSize="Small" />
            </StackLayout>
        </Grid>
    </Grid>

    <BoxView Style="{StaticResource SeparatorStyle}" Margin="-10, 10" />

    <!-- Haul info -->
    <Grid RowSpacing="8" ColumnSpacing="14" BindingContext="{x:Reference this}">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Equipment type -->
        <Label Text="{Binding Source={x:Static ncx:Resources.EquipmentTypeText}}" TextColor="{StaticResource RowHeaderColor}" />
        <Label Text="{Binding Haul.EquipmentType, Converter={StaticResource EquipmentTypeConverter}}" Grid.Column="1" />
        <BoxView Grid.ColumnSpan="2" Style="{StaticResource SeparatorStyle}" Grid.Row="1" />

        <!-- Trailer Length -->
        <Label Text="{Binding Source={x:Static ncx:Resources.TrailerLengthText}}" Grid.Row="2" TextColor="{StaticResource RowHeaderColor}" />
        <Label Text="{Binding Haul.TrailerLength, StringFormat='{0}\''}" Grid.Column="1" Grid.Row="2" />
        <BoxView Grid.ColumnSpan="2" Style="{StaticResource SeparatorStyle}" Grid.Row="3" />

        <!-- Required Temperature -->
        <Label Text="{Binding Source={x:Static ncx:Resources.RequiredTemperatureText}}" Grid.Row="4"
               TextColor="{StaticResource RowHeaderColor}" IsVisible="{Binding Haul.RequiredTemperature, Converter={StaticResource ObjectToBoolConverter}}" />
        <Label Text="{Binding Haul, Converter={StaticResource RequiredTemperatureConverter}}" Grid.Column="1" Grid.Row="4"
               IsVisible="{Binding Haul.RequiredTemperature, Converter={StaticResource ObjectToBoolConverter}}" />
        <BoxView Grid.ColumnSpan="2" Style="{StaticResource SeparatorStyle}" Grid.Row="5"
                 Margin="-10, 0" IsVisible="{Binding Haul.RequiredTemperature, Converter={StaticResource ObjectToBoolConverter}}" />
    </Grid>
</StackLayout>
