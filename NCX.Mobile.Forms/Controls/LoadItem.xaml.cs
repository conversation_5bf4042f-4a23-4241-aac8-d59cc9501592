﻿using System;
using System.Windows.Input;
using NCX.Mobile.Models;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Controls
{
    public partial class LoadItem
    {
        public static readonly BindableProperty HaulProperty =
            BindableProperty.Create(nameof(Haul), typeof(Haul), typeof(LoadItem), null);

        public static readonly BindableProperty LoadProperty =
            BindableProperty.Create(nameof(Load), typeof(Load), typeof(LoadItem), null);

        public static readonly BindableProperty FirstPickupProperty =
            BindableProperty.Create(nameof(FirstPickup), typeof(LoadOperation), typeof(LoadItem), null);

        public static readonly BindableProperty LastDropoffProperty =
            BindableProperty.Create(nameof(LastDropoff), typeof(LoadOperation), typeof(LoadItem), null);

        public static readonly BindableProperty ShowLoadDetailsCommandProperty =
            BindableProperty.Create(nameof(ShowLoadDetailsCommand), typeof(ICommand), typeof(LoadItem), null);

        public LoadItem()
        {
            InitializeComponent();
        }

        public Haul Haul
        {
            get => (Haul)GetValue(HaulProperty);
            set => SetValue(HaulProperty, value);
        }

        public Load Load
        {
            get => (Load)GetValue(LoadProperty);
            set => SetValue(LoadProperty, value);
        }

        public LoadOperation FirstPickup
        {
            get => (LoadOperation)GetValue(FirstPickupProperty);
            set => SetValue(FirstPickupProperty, value);
        }

        public LoadOperation LastDropoff
        {
            get => (LoadOperation)GetValue(LastDropoffProperty);
            set => SetValue(LastDropoffProperty, value);
        }

        public ICommand ShowLoadDetailsCommand
        {
            get => (ICommand)GetValue(ShowLoadDetailsCommandProperty);
            set => SetValue(ShowLoadDetailsCommandProperty, value);
        }
    }
}
