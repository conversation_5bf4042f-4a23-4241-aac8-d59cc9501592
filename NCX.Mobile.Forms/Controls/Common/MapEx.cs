﻿using System.Collections.Generic;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Maps;
using Microsoft.Maui.Maps;
using NCX.Mobile.Helpers; // Reference to the helper methods
using Shiny.Locations;

namespace NCX.Mobile.Controls.Common
{
    /// <summary>
    /// A custom Map control with bindable properties for pins and position.
    /// </summary>
    public class MapEx : Microsoft.Maui.Controls.Maps.Map
    {
        public static readonly BindableProperty MapPinsProperty =
            BindableProperty.Create(
                propertyName: nameof(MapPins),
                returnType: typeof(IEnumerable<Pin>),
                declaringType: typeof(MapEx),
                defaultValue: null,
                propertyChanged: OnMapPinsPropertyChanged);

        public static readonly BindableProperty MapPositionProperty =
            BindableProperty.Create(
                propertyName: nameof(MapPosition),
                returnType: typeof(Position), // Use MAUI Maps Position
                declaringType: typeof(MapEx),
                defaultValue: default(Position),
                propertyChanged: OnMapPositionPropertyChanged);

        public static readonly BindableProperty LastMoveToRegionProperty =
            BindableProperty.Create(
                nameof(LastMoveToRegion),
                typeof(MapSpan),
                typeof(MapEx),
                default(MapSpan?));

        public MapEx() : base()
        {
            // Optionally, initialize default behaviors or settings here
        }

        public IEnumerable<Pin> MapPins
        {
            get => (IEnumerable<Pin>)GetValue(MapPinsProperty);
            set => SetValue(MapPinsProperty, value);
        }

        public Position MapPosition
        {
            get => (Position)GetValue(MapPositionProperty);
            set => SetValue(MapPositionProperty, value);
        }

        public MapSpan? LastMoveToRegion
        {
            get => (MapSpan?)GetValue(LastMoveToRegionProperty);
            set => SetValue(LastMoveToRegionProperty, value);
        }


        private static void OnMapPinsPropertyChanged(BindableObject bindable, object oldValue, object newValue)
        {
            if (bindable is MapEx mapEx)
            {
                mapEx.Pins.Clear();

                if (newValue is IEnumerable<Pin> newPins)
                {
                    foreach (var pin in newPins)
                    {
                        mapEx.Pins.Add(pin);
                    }
                }
            }
        }

        private static void OnMapPositionPropertyChanged(BindableObject bindable, object oldValue, object newValue)
        {
            if (bindable is MapEx mapEx && newValue is Position position)
            {
                mapEx.MoveToRegion(MapSpan.FromCenterAndRadius(position.ToMauiPosition(), Distance.FromMiles(1)));
            }
        }

        public void MoveToRegion(MapSpan region)
        {
            LastMoveToRegion = region;
            base.MoveToRegion(region);
        }

    }
}
