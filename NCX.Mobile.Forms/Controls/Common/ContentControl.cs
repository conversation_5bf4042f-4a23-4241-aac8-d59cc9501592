﻿using System;
using System.Linq;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Controls.Common
{
    /// <summary>
    /// A control that displays content based on a specified DataTemplate.
    /// </summary>
    public class ContentControl : ContentView
    {
        /// <summary>
        /// Bindable property for the ContentTemplate.
        /// </summary>
        public static readonly BindableProperty ContentTemplateProperty =
            BindableProperty.Create(
                propertyName: nameof(ContentTemplate),
                returnType: typeof(DataTemplate),
                declaringType: typeof(ContentControl),
                defaultValue: null,
                propertyChanged: OnContentTemplateChanged);

        /// <summary>
        /// Gets or sets the DataTemplate used to display content.
        /// </summary>
        public DataTemplate ContentTemplate
        {
            get => (DataTemplate)GetValue(ContentTemplateProperty);
            set => SetValue(ContentTemplateProperty, value);
        }

        /// <summary>
        /// Called when the ContentTemplate property changes.
        /// Updates the Content based on the new template.
        /// </summary>
        /// <param name="bindable">The ContentControl instance.</param>
        /// <param name="oldValue">The old DataTemplate.</param>
        /// <param name="newValue">The new DataTemplate.</param>
        private static void OnContentTemplateChanged(BindableObject bindable, object oldValue, object newValue)
        {
            if (bindable is ContentControl contentControl)
            {
                var newTemplate = newValue as DataTemplate;
                var content = newTemplate?.CreateContent();

                if (content is View view)
                {
                    contentControl.Content = view;
                }
                else if (content is ViewCell viewCell)
                {
                    contentControl.Content = viewCell.View;
                }

                contentControl.UpdateContentBinding();
            }
        }

        /// <summary>
        /// Called when the BindingContext changes.
        /// Updates the BindingContext of the Content.
        /// </summary>
        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();
            UpdateContentBinding();
        }

        /// <summary>
        /// Updates the BindingContext of the Content to match the ContentControl's BindingContext.
        /// </summary>
        private void UpdateContentBinding()
        {
            if (Content != null)
            {
                Content.BindingContext = BindingContext;
            }
        }
    }
}
