﻿using System.Linq;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Compatibility;

namespace NCX.Mobile.Controls.Common
{
    public class RadioButton : ContentView
    {
        public static readonly BindableProperty GroupNameProperty = BindableProperty.Create(
            nameof(GroupName),
            typeof(string),
            typeof(RadioButton));

        public static readonly BindableProperty IsCheckedProperty = BindableProperty.Create(
            nameof(IsChecked),
            typeof(bool),
            typeof(RadioButton),
            default(bool));

        public string GroupName
        {
            get => (string)GetValue(GroupNameProperty);
            set => SetValue(GroupNameProperty, value);
        }

        public bool IsChecked
        {
            get => (bool)GetValue(IsCheckedProperty);
            set => SetValue(IsCheckedProperty, value);
        }

        public RadioButton()
        {
            // Initialize a tap gesture to simulate toggle behavior
            var tapGesture = new TapGestureRecognizer();
            tapGesture.Tapped += OnTapped;
            GestureRecognizers.Add(tapGesture);
        }

        private void OnTapped(object sender, System.EventArgs e)
        {
            // Toggle check state
            IsChecked = !IsChecked;
            RadioButton_Checked();
        }

        private void RadioButton_Checked()
        {
            UpdateCheckStateInGroup();
        }

        private void UpdateCheckStateInGroup()
        {
            if (string.IsNullOrEmpty(GroupName) || !IsChecked)
                return;

            if (Parent is Layout<View> parentLayout)
            {
                var siblingChecked = parentLayout.Children
                    .OfType<RadioButton>()
                    .Where(b => b != this && b.GroupName == GroupName && b.IsChecked)
                    .SingleOrDefault();

                if (siblingChecked != null)
                {
                    siblingChecked.IsChecked = false;
                }
            }
        }
    }
}
