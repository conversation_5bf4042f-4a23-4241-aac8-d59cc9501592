﻿using System;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Controls.Common
{
    public class ToggleButton : Button
    {
        public static readonly BindableProperty IsCheckedProperty = BindableProperty.Create(
            nameof(IsChecked),
            typeof(bool),
            typeof(ToggleButton),
            defaultValue: false,
            propertyChanged: (bindable, oldValue, newValue) =>
            {
                ((ToggleButton)bindable).Checked?.Invoke(bindable, new ToggledEventArgs((bool)newValue));
            },
            defaultBindingMode: BindingMode.TwoWay);

        public bool IsChecked
        {
            get => (bool)GetValue(IsCheckedProperty);
            set => SetValue(IsCheckedProperty, value);
        }

        public event EventHandler<ToggledEventArgs> Checked;
    }
}
