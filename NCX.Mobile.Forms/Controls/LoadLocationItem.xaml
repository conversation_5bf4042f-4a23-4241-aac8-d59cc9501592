﻿<?xml version="1.0" encoding="utf-8" ?>
<StackLayout xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:NCX.Mobile.Controls"
             xmlns:ccontrols="clr-namespace:NCX.Mobile.Controls.Common"
             Orientation="Horizontal"
             x:Name="this"
             x:Class="NCX.Mobile.Controls.LoadLocationItem">

    <StackLayout HorizontalOptions="StartAndExpand"
                 BindingContext="{x:Reference this}">
        <!-- Company name -->
        <Label Text="{Binding LoadOperation.Location.CompanyName}"
               FontSize="Medium" />

        <!-- Address -->
        <Label Text="{Binding LoadOperation.Location.Address, Converter={StaticResource AddressConverter}}"
               TextColor="{StaticResource HyperlinkColor}"
               FontSize="Medium">
            <Label.GestureRecognizers>
                <TapGestureRecognizer Command="{Binding StartMapDirectionsTaskCommand}"
                                      CommandParameter="{Binding LoadOperation}"/>
            </Label.GestureRecognizers>
        </Label>

        <!-- Contact name -->
        <Label Text="{Binding LoadOperation.Location.ContactName}"
               FontSize="Medium"
               IsVisible="{Binding LoadOperation.Location.ContactName, Converter={StaticResource ObjectToBoolConverter}}" />

        <!-- Contact phone -->
        <Label Text="{Binding LoadOperation.Location.ContactNumber}"
               TextColor="{StaticResource HyperlinkColor}"
               IsVisible="{Binding LoadOperation.Location.ContactNumber, Converter={StaticResource ObjectToBoolConverter}}"
               FontSize="Medium">
            <Label.GestureRecognizers>
                <TapGestureRecognizer Command="{Binding CallLocationContactCommand}"
                                      CommandParameter="{Binding LoadOperation}" />
            </Label.GestureRecognizers>
        </Label>
    </StackLayout>

    <ccontrols:MapEx BindingContext="{x:Reference this}"
                     HorizontalOptions="End"
                     WidthRequest="100"
                     VerticalOptions="FillAndExpand"
                     Margin="10,0,0,0"
                     IsEnabled="False"
                     MapPins="{Binding LoadOperation.Location, Converter={StaticResource LoadLocationAddressToMapPinsConverter}}"
                     MapPosition="{Binding LoadOperation.Location.Address, Converter={StaticResource LoadLocationAddressToMapPositionConverter}}"
                     MapType="Street" />
</StackLayout>
