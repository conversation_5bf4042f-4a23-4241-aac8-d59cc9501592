﻿using System.Windows.Input;
using NCX.Mobile.Models;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Controls
{
    public partial class LoadLocationItem
    {
        public static readonly BindableProperty LoadOperationProperty =
            BindableProperty.Create(nameof(LoadOperation), typeof(LoadOperation), typeof(LoadLocationItem), null);

        public static readonly BindableProperty ItemPositionProperty =
            BindableProperty.Create(nameof(ItemPosition), typeof(int), typeof(LoadLocationItem), 0);

        public static readonly BindableProperty ItemTotalCountProperty =
            BindableProperty.Create(nameof(ItemTotalCount), typeof(int), typeof(LoadLocationItem), 0);

        public static readonly BindableProperty StartMapDirectionsTaskCommandProperty =
            BindableProperty.Create(nameof(StartMapDirectionsTaskCommand), typeof(ICommand), typeof(LoadLocationItem), null);

        public static readonly BindableProperty CallLocationContactCommandProperty =
            BindableProperty.Create(nameof(CallLocationContactCommand), typeof(ICommand), typeof(LoadLocationItem), null);

        public LoadLocationItem()
        {
            InitializeComponent();
        }

        public LoadOperation LoadOperation
        {
            get => (LoadOperation)GetValue(LoadOperationProperty);
            set => SetValue(LoadOperationProperty, value);
        }

        public int ItemPosition
        {
            get => (int)GetValue(ItemPositionProperty);
            set => SetValue(ItemPositionProperty, value);
        }

        public int ItemTotalCount
        {
            get => (int)GetValue(ItemTotalCountProperty);
            set => SetValue(ItemTotalCountProperty, value);
        }

        public ICommand StartMapDirectionsTaskCommand
        {
            get => (ICommand)GetValue(StartMapDirectionsTaskCommandProperty);
            set => SetValue(StartMapDirectionsTaskCommandProperty, value);
        }

        public ICommand CallLocationContactCommand
        {
            get => (ICommand)GetValue(CallLocationContactCommandProperty);
            set => SetValue(CallLocationContactCommandProperty, value);
        }
    }
}
