﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net8.0;net8.0-ios;net8.0-maccatalyst</TargetFrameworks>
		<ImplicitUsings>enable</ImplicitUsings>
		<RootNamespace>NCX.Mobile</RootNamespace>
		<AssemblyName>NCX.Mobile.Forms</AssemblyName>
		<LangVersion>latest</LangVersion>
		<Nullable>enable</Nullable>
		<OutputType>Library</OutputType>
		<UseMaui>true</UseMaui>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Humanizer.Core" Version="2.14.1" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="8.0.100" />
		<PackageReference Include="Microsoft.Maui.Controls.Maps" Version="8.0.100" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Prism.Core" Version="9.0.537" />
		<PackageReference Include="Prism.DryIoc.Maui" Version="9.0.537" />
		<PackageReference Include="Refit" Version="8.0.0" />
		<PackageReference Include="Shiny.Locations" Version="3.3.4" />
		<PackageReference Include="System.Threading.Tasks.Extensions" Version="4.6.0" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\NCX.Mobile.Abstractions\NCX.Mobile.Abstractions.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <PackageReference Update="Microsoft.Maui.Controls.Compatibility" Version="8.0.100" />
	</ItemGroup>
</Project>
