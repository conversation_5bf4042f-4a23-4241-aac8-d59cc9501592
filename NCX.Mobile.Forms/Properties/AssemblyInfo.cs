﻿using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Xaml;

[assembly: XamlCompilation(XamlCompilationOptions.Compile)]
[assembly: XmlnsDefinition("http://nationalcarrierexchange.com", "NCX.Mobile.Behaviors")]
[assembly: XmlnsDefinition("http://nationalcarrierexchange.com", "NCX.Mobile.Controls")]
[assembly: XmlnsDefinition("http://nationalcarrierexchange.com", "NCX.Mobile.Controls.Common")]
[assembly: XmlnsDefinition("http://nationalcarrierexchange.com", "NCX.Mobile.Converters")]
[assembly: XmlnsDefinition("http://nationalcarrierexchange.com", "NCX.Mobile.Effects")]
[assembly: XmlnsDefinition("http://nationalcarrierexchange.com", "NCX.Mobile.Helpers", AssemblyName = "NCX.Mobile.Forms")]
[assembly: XmlnsDefinition("http://nationalcarrierexchange.com", "NCX.Mobile.Models")]
