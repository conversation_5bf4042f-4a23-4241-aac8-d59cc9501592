﻿using NCX.Logging.Enums;
using NCX.Logging.Interfaces;
using NCX.Logging.Models;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace NCX.Logging.Services
{
    public class LogService : ILogService
    {
        readonly ILogProcessor _logProcessor;

        public LogService(ILogProcessor logProcessor)
        {
            _logProcessor = logProcessor;
        }

        public void LogCritical(string source, string message, Exception exception = null, [CallerMemberName] string memberName = "")
        {
            ProcessLog(source, message, LogType.Log, LogPriority.Critical, memberName, null, exception);
        }

        public void LogCritical(string source, string message, Dictionary<string, string> data, Exception exception = null, [CallerMemberName] string memberName = "")
        {
            ProcessLog(source, message, LogType.Log, LogPriority.Critical, memberName, data, exception);
        }

        public void LogDebug(string source, string message, Exception exception = null, [CallerMemberName] string memberName = "")
        {
            ProcessLog(source, message, LogType.Log, LogPriority.Low, memberName, null, exception);
        }

        public void LogDebug(string source, string message, Dictionary<string, string> data, Exception exception = null, [CallerMemberName] string memberName = "")
        {
            ProcessLog(source, message, LogType.Log, LogPriority.Low, memberName, data, exception);
        }

        public void LogHigh(string source, string message, Exception exception = null, [CallerMemberName] string memberName = "")
        {
            ProcessLog(source, message, LogType.Log, LogPriority.High, memberName, null, exception);
        }

        public void LogHigh(string source, string message, Dictionary<string, string> data, Exception exception = null, [CallerMemberName] string memberName = "")
        {
            ProcessLog(source, message, LogType.Log, LogPriority.High, memberName, data, exception);
        }

        public void LogWarn(string source, string message, Exception exception = null, [CallerMemberName] string memberName = "")
        {
            ProcessLog(source, message, LogType.Log, LogPriority.Medium, memberName, null, exception);
        }

        public void LogWarn(string source, string message, Dictionary<string, string> data, Exception exception = null, [CallerMemberName] string memberName = "")
        {
            ProcessLog(source, message, LogType.Log, LogPriority.Medium, memberName, data, exception);
        }

        public void Track(string source, string message, Dictionary<string, string> data = null, [CallerMemberName] string memberName = "")
        {
            ProcessLog(source, message, LogType.Track, LogPriority.Low, memberName, data);
        }

        void ProcessLog(string source, string message, LogType type, LogPriority priority, string memberName, Dictionary<string, string> data = null, Exception exception = null)
        {
            try
            {
                _logProcessor.ProcessLogRequest(new LogParameter
                {
                    Source = source,
                    Type = type,
                    MemberName = memberName,
                    Priority = priority,
                    Message = message,
                    Data = data,
                    Exception = exception,
                });
            }
            catch (Exception ex)
            {
                Microsoft.AppCenter.Crashes.Crashes.TrackError(ex);
                Debug.WriteLine($"ProcessLog Exception: {ex.Message}");
            }
        }
    }
}
