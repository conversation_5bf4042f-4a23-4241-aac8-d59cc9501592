﻿using NCX.Logging.Interfaces;
using NCX.Logging.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace NCX.Logging.Services
{
    public class LogProcessor : ILogProcessor
    {
        readonly object _lock = new object();

        #region Command Register/Unregister 

        Dictionary<Type, ILogCommand> _commands = new Dictionary<Type, ILogCommand>();

        public List<ILogCommand> RegisteredCommands
        {
            get
            {
                lock (_lock)
                {
                    return _commands.Values.ToList();
                }
            }
        }

        public void RegisterCommand(ILogCommand command)
        {
            if (command == null)
                return;

            lock (_lock)
            {
                _commands[command.GetType()] = command;
            }
        }

        public void UnregisterCommand(Type type)
        {
            if (type == null)
                return;

            lock (_lock)
            {
                if (_commands.ContainsKey(type))
                    _commands.Remove(type);
            }
        }

        public void UnregisterCommand(ILogCommand command)
        {
            UnregisterCommand(command.GetType());
        }

        #endregion

        #region Processing Log Requests

        public Action<LogParameter> MissingCommandAction { get; set; }
        public Action<LogParameter> DefaultAction { get; set; }
        public Action<LogParameter> PreCommandAction { get; set; }
        public Action<LogParameter> PostCommandAction { get; set; }

        public bool ProcessLogRequest(LogParameter parameter)
        {
            bool didExecute = false;

            DefaultAction?.Invoke(parameter);

            lock (_lock)
            {
                foreach (var kvp in _commands)
                {
                    if (!kvp.Value.CanExecute(parameter))
                    {
                        // Skipping command
                        continue;
                    }

                    didExecute = true;

                    PreCommandAction?.Invoke(parameter);

                    kvp.Value.Execute(parameter);

                    PostCommandAction?.Invoke(parameter);
                }
            }

            if (!didExecute)
            {
                MissingCommandAction?.Invoke(parameter);
            }

            return didExecute;
        }

        #endregion
    }
}
