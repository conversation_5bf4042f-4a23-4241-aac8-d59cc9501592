﻿using NCX.Logging.Enums;
using NCX.Logging.Interfaces;
using NCX.Logging.Models;

namespace NCX.Logging.Commands
{
    public class ExceptionLogCommand : ILogCommand
    {
        public bool CanExecute(LogParameter parameter)
        {
            return (parameter.Type == LogType.Log && parameter.Exception != null);
        }

        public void Execute(LogParameter parameter)
        {
        }
    }
}
