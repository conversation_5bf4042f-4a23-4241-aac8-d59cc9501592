﻿using Microsoft.AppCenter.Crashes;
using NCX.Logging.Interfaces;
using NCX.Logging.Models;
using System;
using System.Collections.Generic;
using System.Diagnostics;

namespace NCX.Logging.Commands
{
    public class DebugLogCommand : ILogCommand
    {
        public bool CanExecute(LogParameter parameter)
        {
            return true;
        }

        public void Execute(LogParameter parameter)
        {
            DateTime now = DateTime.UtcNow;

            try
            {
                Debug.WriteLine($"\n{now} {parameter.Source} : {parameter.MemberName} - {parameter.Message}\n");

                if (parameter.Data != null && parameter.Data.Count > 0)
                {
                    foreach (KeyValuePair<string, string> data in parameter.Data)
                    {
                        Debug.WriteLine($"\t{data.Key}: {data.Value}");
                    }

                    Debug.WriteLine("");
                }

                if (parameter.Exception != null)
                {
                    var stackTrace = parameter.Exception?.StackTrace?.Replace(" in ", "\n  in ");
                    //var stackTrace = parameter.Exception?.StackTrace?.Replace( "  at ", "\n\tat " ).Replace( " in ", "\n\tin " ).Replace( " in ", "\n\tin " );

                    Debug.WriteLine($"{parameter.Exception?.Message}\n");
                    Debug.WriteLine($"{stackTrace}\n");

                    foreach (var item in parameter.Exception.Data)
                    {
                        var kvp = (KeyValuePair<string, string>)item;

                        Debug.WriteLine($"{now}: {kvp.Key} = '{kvp.Value}");
                    }

                    if (parameter.Exception != parameter.Exception.GetBaseException())
                    {
                        Execute(new LogParameter
                        {
                            Source = parameter.Source,
                            Message = parameter.Message,
                            Exception = parameter.Exception.InnerException,
                            Type = parameter.Type,
                            Priority = parameter.Priority,
                        });
                    }

                }
            }
            catch (Exception ex)
            {
                Crashes.TrackError(ex);
                var stackTrace = ex.StackTrace.Replace(" in ", "\n  in ");

                Debug.WriteLine($"{now}: Exception Thrown in DebugLogCommand ");
                Debug.WriteLine($"{ex.Message}");
                Debug.WriteLine($"{stackTrace}");
            }
        }
    }
}
