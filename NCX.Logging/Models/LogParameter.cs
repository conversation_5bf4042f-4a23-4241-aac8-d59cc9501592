﻿using NCX.Logging.Enums;
using System;
using System.Collections.Generic;

namespace NCX.Logging.Models
{
    public class LogParameter
    {
        public LogType Type { get; set; } = LogType.Log;

        public LogPriority Priority { get; set; } = LogPriority.Low;

        public string Source { get; set; } = "UNKNOWN";

        public string Message { get; set; }

        public string MemberName { get; set; }

        public Dictionary<string, string> Data { get; set; }

        public Exception Exception { get; set; }

        public override string ToString()
        {
            return string.Format("{0} - {1} - {2}", Type.ToString(), Priority.ToString(), Message);
        }

        public LogParameter()
        {
        }

        public LogParameter(string message)
        {
        }

        public LogParameter(string message, Dictionary<string, string> data)
        {
        }

        public LogParameter(string message, Exception exception)
        {
        }
    }
}
