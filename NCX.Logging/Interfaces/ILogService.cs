﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace NCX.Logging.Interfaces
{
    public interface ILogService
    {
        void LogDebug(string source, string message, Exception exception = null, [CallerMemberName] string memberName = "");
        void LogDebug(string source, string message, Dictionary<string, string> data, Exception exception = null, [CallerMemberName] string memberName = "");

        void LogWarn(string source, string message, Exception exception = null, [CallerMemberName] string memberName = "");
        void LogWarn(string source, string message, Dictionary<string, string> data, Exception exception = null, [CallerMemberName] string memberName = "");

        void LogHigh(string source, string message, Exception exception = null, [CallerMemberName] string memberName = "");
        void LogHigh(string source, string message, Dictionary<string, string> data, Exception exception = null, [CallerMemberName] string memberName = "");

        void LogCritical(string source, string message, Exception exception = null, [CallerMemberName] string memberName = "");
        void LogCritical(string source, string message, Dictionary<string, string> data, Exception exception = null, [CallerMemberName] string memberName = "");

        void Track(string source, string message, Dictionary<string, string> data = null, [CallerMemberName] string memberName = "");
    }
}
