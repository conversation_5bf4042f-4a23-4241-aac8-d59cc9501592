﻿using NCX.Logging.Models;
using System;
using System.Collections.Generic;

namespace NCX.Logging.Interfaces
{
    public interface ILogProcessor
    {
        void RegisterCommand(ILogCommand command);
        void UnregisterCommand(ILogCommand command);
        void UnregisterCommand(Type type);

        List<ILogCommand> RegisteredCommands { get; }

        bool ProcessLogRequest(LogParameter parameter);

        Action<LogParameter> MissingCommandAction { get; set; }
        Action<LogParameter> DefaultAction { get; set; }
        Action<LogParameter> PreCommandAction { get; set; }
        Action<LogParameter> PostCommandAction { get; set; }
    }
}
