﻿using System.Runtime.CompilerServices;
using NCX.Mobile.Helpers;
using Prism.Mvvm;

namespace NCX.Mobile.Models
{
    public class AppSettings : BindableBase, IAppSettings
    {
        public AppSettings()
        {
#if DEBUG
            ApiHost = Constants.HostQA3;
#else
            ApiHost = Constants.HostProd;
#endif
        }

        private int version = 1;
        public int Version
        {
            get => version;
            set => SetProperty(ref version, value);
        }

        private string apiHost;
        public string ApiHost
        {
            get => apiHost;
            set => SetProperty(ref apiHost, value);
        }

        private int? lastLoggedInUserId;
        public int? LastLoggedInUserId
        {
            get => lastLoggedInUserId;
            set => SetProperty(ref lastLoggedInUserId, value);
        }

        private bool shouldNavigateToLoginOnNextAppResume;
        public bool ShouldNavigateToLoginOnNextAppResume
        {
            get => shouldNavigateToLoginOnNextAppResume;
            set => SetProperty(ref shouldNavigateToLoginOnNextAppResume, value);
        }

        private bool? sessionClosedBecauseExpired;
        public bool? SessionClosedBecauseExpired
        {
            get => sessionClosedBecauseExpired;
            set => SetProperty(ref sessionClosedBecauseExpired, value);
        }

        private bool shouldShowNewLoadsOnNextAppResume;
        public bool ShouldShowNewLoadsOnNextAppResume
        {
            get => shouldShowNewLoadsOnNextAppResume;
            set => SetProperty(ref shouldShowNewLoadsOnNextAppResume, value);
        }

        private bool shouldMergeHaulCache;
        public bool ShouldMergeHaulCache
        {
            get => shouldMergeHaulCache;
            set => SetProperty(ref shouldMergeHaulCache, value);
        }

        // Two letter ISO language name
        private string language = "EN";
        public string Language
        {
            get => language;
            set => SetProperty(ref language, value);
        }

        private bool? loadsNeedRefresh;
        public bool? LoadsNeedRefresh
        {
            get => loadsNeedRefresh;
            set => SetProperty(ref loadsNeedRefresh, value);
        }

        private string pushNotificationToken;
        public string PushNotificationToken
        {
            get => pushNotificationToken;
            set => SetProperty(ref pushNotificationToken, value);
        }
    }
}
