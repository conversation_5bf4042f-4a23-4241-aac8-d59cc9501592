﻿using JsonApiNet.Attributes;
using Prism.Mvvm;
using ReactiveUI;
using System;

namespace NCX.Mobile.Models
{
    [JsonApiResourceType("drivers")]
    public class User : BindableBase
    {
        private int _id;
        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private string _email;
        public string Email
        {
            get => _email;
            set => SetProperty(ref _email, value);
        }

        private string _firstName;
        public string FirstName
        {
            get => _firstName;
            set => SetProperty(ref _firstName, value, () => RaisePropertyChanged(nameof(Name)));
        }

        private string _lastName;
        public string LastName
        {
            get => _lastName;
            set => SetProperty(ref _lastName, value, () => RaisePropertyChanged(nameof(Name)));
        }

        public string Name => $"{FirstName} {LastName}".Trim();

        private string _avatarUri;
        [JsonApiAttribute("avatar")]
        public string AvatarUri
        {
            get => _avatarUri;
            set => SetProperty(ref _avatarUri, value);
        }

        private string _phoneNumber;
        public string PhoneNumber
        {
            get => _phoneNumber;
            set => SetProperty(ref _phoneNumber, value);
        }

        private string _country;
        public string Country
        {
            get => _country;
            set => SetProperty(ref _country, value);
        }

        private DateTime? _createdAt;
        public DateTime? CreatedAt
        {
            get => _createdAt;
            set => SetProperty(ref _createdAt, value);
        }

        private DateTime? _updateAt;
        public DateTime? UpdatedAt
        {
            get => _updateAt;
            set => SetProperty(ref _updateAt, value);
        }

        private string _driverLicenseState;
        public string DriverLicenseState
        {
            get => _driverLicenseState;
            set => SetProperty(ref _driverLicenseState, value);
        }

        private string _driverLicenseNumber;
        public string DriverLicenseNumber
        {
            get => _driverLicenseNumber;
            set => SetProperty(ref _driverLicenseNumber, value);
        }

        private DateTime? _driverLicenseExpirationDate;
        public DateTime? DriverLicenseExpirationDate
        {
            get => _driverLicenseExpirationDate;
            set => SetProperty(ref _driverLicenseExpirationDate, value);
        }

        private string _medicalCertificateNationalRegistrationNumber;
        public string MedicalCertificateNationalRegistrationNumber
        {
            get => _medicalCertificateNationalRegistrationNumber;
            set => SetProperty(ref _medicalCertificateNationalRegistrationNumber, value);
        }

        private DateTime? _medicalCertificateExpirationDate;
        public DateTime? MedicalCertificateExpirationDate
        {
            get => _medicalCertificateExpirationDate;
            set => SetProperty(ref _medicalCertificateExpirationDate, value);
        }

        private int? _loadsHauls;
        public int? LoadsHauled
        {
            get => _loadsHauls;
            set => SetProperty(ref _loadsHauls, value);
        }

        private bool? _personalUse;
        public bool? PersonalUse
        {
            get { return _personalUse; }
            set { SetProperty(ref _personalUse, value); }
        }

        private bool _eldExempt;
        public bool EldExempt
        {
            get { return _eldExempt; }
            set { SetProperty(ref _eldExempt, value); }
        }

        private string _communicationForm;
        public string CommunicationForm
        {
            get { return _communicationForm; }
            set { SetProperty(ref _communicationForm, value); }
        }

        private string _eldExemptReason;
        public string EldExemptReason
        {
            get { return _eldExemptReason; }
            set { SetProperty(ref _eldExemptReason, value); }
        }

        private bool _suspended;
        public bool Suspended
        {
            get { return _suspended; }
            set { SetProperty(ref _suspended, value); }
        }

        private bool _iftaEnabled;
        public bool IftaEnabled
        {
            get { return _iftaEnabled; }
            set { SetProperty(ref _iftaEnabled, value); }
        }

        private string _direction;
        public string Direction
        {
            get { return _direction; }
            set { SetProperty(ref _direction, value); }
        }

        //~User()
        //{
        //    _nameHelper.Dispose();
        //    _nameHelper = null;
        //}
    }
}
