﻿using System;
using Prism.Mvvm;

namespace NCX.Mobile.Models
{
    public class LocationOperatingHour : BindableBase
    {
        private string _id;
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private LocationOperatingHourDay _day;
        public LocationOperatingHourDay Day
        {
            get => _day;
            set => SetProperty(ref _day, value);
        }

        private DateTime _open;
        public DateTime Open
        {
            get => _open;
            set => SetProperty(ref _open, value);
        }

        private DateTime _close;
        public DateTime Close
        {
            get => _close;
            set => SetProperty(ref _close, value);
        }

        private bool _allDay;
        public bool AllDay
        {
            get => _allDay;
            set => SetProperty(ref _allDay, value);
        }

        private int _position;
        public int Position
        {
            get => _position;
            set => SetProperty(ref _position, value);
        }
    }
}
