﻿using System;
using Prism.Mvvm;
using ReactiveUI;

namespace NCX.Mobile.Models
{
    public class Organization : BindableBase
    {
        private int _id;
        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private string _name;
        public string Name
        {
            get { return _name; }
            set { SetProperty(ref _name, value); }
        }

        private string _mcNumber;
        public string McNumber
        {
            get { return _mcNumber; }
            set { SetProperty(ref _mcNumber, value); }
        }

        private string _dotNumber;
        public string DotNumber
        {
            get { return _dotNumber; }
            set { SetProperty(ref _dotNumber, value); }
        }

        private bool _verified;
        public bool Verified
        {
            get { return _verified; }
            set { SetProperty(ref _verified, value); }
        }

        private string _phoneNumber;
        public string PhoneNumber
        {
            get { return _phoneNumber; }
            set { SetProperty(ref _phoneNumber, value); }
        }

        private string _phoneExtension;
        public string PhoneExtension
        {
            get { return _phoneExtension; }
            set { SetProperty(ref _phoneExtension, value); }
        }

        private string _faxNumber;
        public string FaxNumber
        {
            get { return _faxNumber; }
            set { SetProperty(ref _faxNumber, value); }
        }

        private bool _deliversInUsa;
        public bool DeliversInUsa
        {
            get { return _deliversInUsa; }
            set { SetProperty(ref _deliversInUsa, value); }
        }

        private string _carrier;
        public string Carrier
        {
            get { return _carrier; }
            set { SetProperty(ref _carrier, value); }
        }

        private DateTime _createdAt;
        public DateTime CreatedAt
        {
            get { return _createdAt; }
            set { SetProperty(ref _createdAt, value); }
        }

        private DateTime _updatedAt;
        public DateTime UpdatedAt
        {
            get { return _updatedAt; }
            set { SetProperty(ref _updatedAt, value); }
        }

        private string _insuranceCompanyName;
        public string InsuranceCompanyName
        {
            get { return _insuranceCompanyName; }
            set { SetProperty(ref _insuranceCompanyName, value); }
        }

        private string _insurancePolicyNumber;
        public string InsurancePolicyNumber
        {
            get { return _insurancePolicyNumber; }
            set { SetProperty(ref _insurancePolicyNumber, value); }
        }

        private double? _insuranceAmountOfCoverage;
        public double? InsuranceAmountOfCoverage
        {
            get { return _insuranceAmountOfCoverage; }
            set { SetProperty(ref _insuranceAmountOfCoverage, value); }
        }

        private string _proofOfInsurance;
        public string ProofOfInsurance
        {
            get { return _proofOfInsurance; }
            set { SetProperty(ref _proofOfInsurance, value); }
        }

        private bool eldFeatures;
        public bool EldFeatures
        {
            get { return eldFeatures; }
            set { SetProperty(ref eldFeatures, value); }
        }

        private bool? _personalUse;
        public bool? PersonalUse
        {
            get { return _personalUse; }
            set { SetProperty(ref _personalUse, value); }
        }

        private bool _yardMove;
        public bool YardMove
        {
            get { return _yardMove; }
            set { SetProperty(ref _yardMove, value); }
        }

        private string _eldTimezone;
        public string EldTimezone
        {
            get { return _eldTimezone; }
            set { SetProperty(ref _eldTimezone, value); }
        }

        private string _eldStartTime;
        public string EldStartTime
        {
            get { return _eldStartTime; }
            set { SetProperty(ref _eldStartTime, value); }
        }

        private int _eldUtcOffset;
        public int EldUtcOffset
        {
            get { return _eldUtcOffset; }
            set { SetProperty(ref _eldUtcOffset, value); }
        }

        private int? _multidayDutyStatus;
        public int? MultidayDutyStatus
        {
            get { return _multidayDutyStatus; }
            set { SetProperty(ref _multidayDutyStatus, value); }
        }

        private string _routing;
        public string Routing
        {
            get { return _routing; }
            set { SetProperty(ref _routing, value); }
        }

        private bool _shipperEnabled;
        public bool ShipperEnabled
        {
            get { return _shipperEnabled; }
            set { SetProperty(ref _shipperEnabled, value); }
        }

        private bool _active;
        public bool Active
        {
            get { return _active; }
            set { SetProperty(ref _active, value); }
        }

        private bool _iftaReporting;
        public bool IftaReporting
        {
            get { return _iftaReporting; }
            set { SetProperty(ref _iftaReporting, value); }
        }

        private string _recurlyAccountId;
        public string RecurlyAccountId
        {
            get { return _recurlyAccountId; }
            set { SetProperty(ref _recurlyAccountId, value); }
        }
    }
}
