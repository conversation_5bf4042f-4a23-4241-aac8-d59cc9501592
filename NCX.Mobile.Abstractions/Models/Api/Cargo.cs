﻿using System;
using Prism.Mvvm;
using ReactiveUI;

namespace NCX.Mobile.Models
{
    [JsonApiNet.Attributes.JsonApiResourceType("load-cargos")]
    public class Cargo : BindableBase
    {
        private string _id;
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private string _unitType;
        public string UnitType
        {
            get => _unitType;
            set => SetProperty(ref _unitType, value);
        }

        private string _description;
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        private int _totalUnits;
        public int TotalUnits
        {
            get => _totalUnits;
            set => SetProperty(ref _totalUnits, value);
        }

        private decimal _totalWeight;
        public decimal TotalWeight
        {
            get => _totalWeight;
            set => SetProperty(ref _totalWeight, value);
        }

        private string _weightType;
        public string WeightType
        {
            get => _weightType;
            set => SetProperty(ref _weightType, value);
        }

        private int _totalPallets;
        public int TotalPallets
        {
            get => _totalPallets;
            set => SetProperty(ref _totalPallets, value);
        }

        private bool _isPalletized;
        public bool IsPalletized
        {
            get => _isPalletized;
            set => SetProperty(ref _isPalletized, value);
        }

        private DateTime _createdAt;
        public DateTime CreatedAt
        {
            get => _createdAt;
            set => SetProperty(ref _createdAt, value);
        }

        private DateTime _updatedAt;
        public DateTime UpdatedAt
        {
            get => _updatedAt;
            set => SetProperty(ref _updatedAt, value);
        }
    }
}
