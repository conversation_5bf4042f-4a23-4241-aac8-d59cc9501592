﻿using Refit;

namespace NCX.Mobile.Models
{
    public class UpdateUserPasswordRequest
    {
        [AliasAs("driver[current_password]")]
        public string CurrentPassword { get; set; }

        [AliasAs("driver[password]")]
        public string NewPassword { get; set; }

        [AliasAs("driver[password_confirmation]")]
        public string NewPasswordConfirmation { get; set; }

        public UpdateUserPasswordRequest(string currentPassword, string newPassword)
        {
            CurrentPassword = currentPassword;
            NewPassword = newPassword;
            NewPasswordConfirmation = newPassword;
        }
    }
}
