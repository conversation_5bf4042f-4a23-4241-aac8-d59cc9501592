﻿using System;
using System.Collections.Generic;
using Prism.Mvvm;

namespace NCX.Mobile.Models
{
    public class Haul : BindableBase
    {
        private int _id;
        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private HaulStatus _status;
        public HaulStatus Status
        {
            get => _status;
            set => SetProperty(ref _status, value, () => RaisePropertyChanged(nameof(IsActive)));
        }

        private DateTime _createdAt;
        public DateTime CreatedAt
        {
            get => _createdAt;
            set => SetProperty(ref _createdAt, value);
        }

        private DateTime _updatedAt;
        public DateTime UpdatedAt
        {
            get => _updatedAt;
            set => SetProperty(ref _updatedAt, value);
        }

        private string _equipmentType;
        public string EquipmentType
        {
            get => _equipmentType;
            set => SetProperty(ref _equipmentType, value);
        }

        private int _trailerLength;
        public int TrailerLength
        {
            get => _trailerLength;
            set => SetProperty(ref _trailerLength, value);
        }

        private string _requiredTemperature;
        public string RequiredTemperature
        {
            get => _requiredTemperature;
            set => SetProperty(ref _requiredTemperature, value);
        }

        private string _temperatureType;
        public string TemperatureType
        {
            get => _temperatureType;
            set => SetProperty(ref _temperatureType, value);
        }

        private IEnumerable<Load> _loads;
        public IEnumerable<Load> Loads
        {
            get => _loads;
            set => SetProperty(ref _loads, value);
        }

        private Organization _organization;
        public Organization Organization
        {
            get => _organization;
            set => SetProperty(ref _organization, value);
        }

        public bool IsActive => Status == HaulStatus.En_Route || Status == HaulStatus.Checked_In;
    }
}
