﻿using System;
using Prism.Mvvm;

namespace NCX.Mobile.Models
{
    public class LoadUpdate : BindableBase
    {
        public LoadUpdate(int id, LoadStatus status, DateTime updateAt)
        {
            Id = id;
            Status = status;
            UpdatedAt = updateAt;
        }

        private int _id;
        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private LoadStatus _status;
        public LoadStatus Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        private DateTime _updatedAt;
        public DateTime UpdatedAt
        {
            get => _updatedAt;
            set => SetProperty(ref _updatedAt, value);
        }

        public override string ToString()
        {
            return $"LoadId:{Id}, Status:{Status}";
        }
    }
}
