﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reactive.Linq;
using NCX.Mobile.Models.Api;
using Newtonsoft.Json;
using Prism.Mvvm;

namespace NCX.Mobile.Models
{
    public class Load : BindableBase
    {
        private int _id;
        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private string _eid;
        public string Eid
        {
            get => _eid;
            set => SetProperty(ref _eid, value);
        }

        private LoadStatus _status;
        public LoadStatus Status
        {
            get => _status;
            set => SetProperty(ref _status, value, OnStatusChanged);
        }

        private void OnStatusChanged()
        {
            RaisePropertyChanged(nameof(IsActive));
            RaisePropertyChanged(nameof(CanReportDelay));
        }

        private DateTime _createdAt;
        public DateTime CreatedAt
        {
            get => _createdAt;
            set => SetProperty(ref _createdAt, value);
        }

        private DateTime _updateAt;
        public DateTime UpdatedAt
        {
            get => _updateAt;
            set => SetProperty(ref _updateAt, value);
        }

        private string _notes;
        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        private IList<LoadOperation> _operations;
        public IList<LoadOperation> Operations
        {
            get => _operations;
            set => SetProperty(ref _operations, value, OnOperationsChanged);
        }

        private void OnOperationsChanged()
        {
            RaisePropertyChanged(nameof(Pickups));
            RaisePropertyChanged(nameof(Dropoffs));
        }

        private IList<LoadDelay> _delays;
        public IList<LoadDelay> Delays
        {
            get => _delays;
            set => SetProperty(ref _delays, value, OnDelaysChanged);
        }

        private PowerUnit _powerUnit;
        public PowerUnit PowerUnit
        {
            get => _powerUnit;
            set => SetProperty(ref _powerUnit, value);
        }

        private void OnDelaysChanged()
        {
            RaisePropertyChanged(nameof(LastReportedDelay));
            RaisePropertyChanged(nameof(CanReportDelay));
        }

        [JsonIgnore]
        public IEnumerable<LoadOperation> Pickups =>
            Operations.Where(o => o.SourceType == LoadOperationType.Pickup)
                      .OrderBy(o => o.ExpectedDate);

        [JsonIgnore]
        public IEnumerable<LoadOperation> Dropoffs =>
            Operations.Where(o => o.SourceType == LoadOperationType.Dropoff)
                      .OrderBy(o => o.ExpectedDate);

        [JsonIgnore]
        public bool IsActive => Status == LoadStatus.En_Route || Status == LoadStatus.Checked_In;

        [JsonIgnore]
        public LoadDelay LastReportedDelay => Delays?.LastOrDefault();

        [JsonIgnore]
        public bool CanReportDelay => IsActive && LastReportedDelay == null;

        public LoadOperation GetLoadOperation(int loadOperationId)
        {
            return Operations.Single(loadOp => loadOp.Id == loadOperationId);
        }

        public LoadOperation GetActiveLoadOperation()
        {
            return Operations.SingleOrDefault(loadOp => loadOp.IsActive);
        }

        public bool HasActiveOrPendingPickups()
        {
            return Pickups.Any(p => p.IsActiveOrPending);
        }

        public IEnumerable<LoadOperation> GetOrderedLoadOperations(LoadOperationType? type, IEnumerable<LoadOperationStatus> statuses)
        {
            IEnumerable<LoadOperation> loadOperations = Operations;

            if (type != null)
            {
                loadOperations = loadOperations.Where(l => l.SourceType == type);
            }

            if (statuses != null)
            {
                loadOperations = loadOperations.Where(l => statuses.Contains(l.Status));
            }

            loadOperations = loadOperations.OrderBy(o => o.ExpectedDate);

            return loadOperations.ToList();
        }

        public IEnumerable<LoadOperation> GetOrderedLoadOperations(LoadOperationType? type, LoadOperationStatus status)
        {
            return GetOrderedLoadOperations(type, new LoadOperationStatus[] { status });
        }

        //~Load()
        //{
        //    _canReportDelayHelper.Dispose();
        //    _canReportDelayHelper = null;

        //    _delayHelper.Dispose();
        //    _delayHelper = null;

        //    _dropoffsHelper.Dispose();
        //    _dropoffsHelper = null;

        //    _isActiveHelper.Dispose();
        //    _isActiveHelper = null;

        //    _pickupsHelper.Dispose();
        //    _pickupsHelper = null;
        //}
    }
}