﻿using JsonApiNet.Attributes;
using Prism.Mvvm;

namespace NCX.Mobile.Models
{
    public class LoadImage : BindableBase
    {
        private string _mimeType;
        [JsonApiAttribute("mime_type")]
        public string MimeType
        {
            get => _mimeType;
            set => SetProperty(ref _mimeType, value);
        }

        private string _data;
        public string Data
        {
            get => _data;
            set => SetProperty(ref _data, value);
        }
    }
}
