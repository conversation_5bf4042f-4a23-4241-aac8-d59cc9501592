﻿using Newtonsoft.Json;
using Prism.Mvvm;
using System;
using System.Reactive.Subjects;

namespace NCX.Mobile.Models.Api
{
    public class FuelStop : BindableBase
    {
        private int id;
        public int Id
        {
            get => id;
            set => SetProperty(ref id, value);
        }

        private int powerUnitId;
        public int PowerUnitId
        {
            get => powerUnitId;
            set => SetProperty(ref powerUnitId, value);
        }

        private double odometerReading;
        public double OdometerReading
        {
            get => odometerReading;
            set => SetProperty(ref odometerReading, value);
        }

        private FuelType fuelType;
        public FuelType FuelType
        {
            get => fuelType;
            set => SetProperty(ref fuelType, value);
        }

        private double quantityOfFuel;
        public double QuantityOfFuel
        {
            get => quantityOfFuel;
            set => SetProperty(ref quantityOfFuel, value, RaiseRefuelPriceChanged);
        }

        private double pricePerGallon;
        public double PricePerGallon
        {
            get => pricePerGallon;
            set => SetProperty(ref pricePerGallon, value, RaiseRefuelPriceChanged);
        }

        private void RaiseRefuelPriceChanged()
        {
            RaisePropertyChanged(nameof(RefuelPrice));
            observableRefuel.OnNext(RefuelPrice);
        }

        [JsonIgnore]
        public double RefuelPrice { get { return PricePerGallon * QuantityOfFuel; } }

        private readonly Subject<double> observableRefuel = new Subject<double>();
        [JsonIgnore]
        public IObservable<double> ObservableRefuelPrice => observableRefuel;

        private string receiptPhoto;
        public string ReceiptPhoto
        {
            get => receiptPhoto;
            set => SetProperty(ref receiptPhoto, value);
        }

        private int loadId;
        public int LoadId
        {
            get => loadId;
            set => SetProperty(ref loadId, value);
        }

        private int driverId;
        public int DriverId
        {
            get => driverId;
            set => SetProperty(ref driverId, value);
        }

        private string state;
        public string State
        {
            get => state;
            set => SetProperty(ref state, value);
        }

        private string country;
        public string Country
        {
            get => country;
            set => SetProperty(ref country, value);
        }

        private DateTime refueledAt;
        public DateTime RefueledAt
        {
            get => refueledAt;
            set => SetProperty(ref refueledAt, value);
        }

        private DateTime createdAt;
        public DateTime CreatedAt
        {
            get => createdAt;
            set => SetProperty(ref createdAt, value);
        }

        private DateTime updatedAt;
        public DateTime UpdatedAt
        {
            get => updatedAt;
            set => SetProperty(ref updatedAt, value);
        }
    }
}
