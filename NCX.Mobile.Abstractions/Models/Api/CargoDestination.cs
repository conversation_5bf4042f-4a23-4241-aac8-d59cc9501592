﻿using Prism.Mvvm;
using ReactiveUI;

namespace NCX.Mobile.Models
{
    [JsonApiNet.Attributes.JsonApiResourceType("load-cargo-destinations")]
    public class CargoDestination : BindableBase
    {
        private string _id;
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private int _totalUnits;
        public int TotalUnits
        {
            get => _totalUnits;
            set => SetProperty(ref _totalUnits, value);
        }

        private decimal _totalWeight;
        public decimal TotalWeight
        {
            get => _totalWeight;
            set => SetProperty(ref _totalWeight, value);
        }

        private Cargo _cargo;
        public Cargo Cargo
        {
            get => _cargo;
            set => SetProperty(ref _cargo, value);
        }
    }
}
