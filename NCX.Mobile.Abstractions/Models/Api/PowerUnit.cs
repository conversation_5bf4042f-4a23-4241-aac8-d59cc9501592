﻿using Prism.Mvvm;
using ReactiveUI;
using System;
using JsonApiNet.Attributes;

namespace NCX.Mobile.Models.Api
{
    public class PowerUnit : BindableBase
    {
        private int _id;
        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private string number;
        public string Number
        {
            get => number;
            set => SetProperty(ref number, value);
        }

        private string licensePlate;
        public string LicensePlate
        {
            get => licensePlate;
            set => SetProperty(ref licensePlate, value);
        }

        private string vehicleIdNumber;
        public string VehicleIdNumber
        {
            get => vehicleIdNumber;
            set => SetProperty(ref vehicleIdNumber, value);
        }

        private string nickname;
        public string Nickname
        {
            // Guards against Null Reference which will blow up the Picker
            get => nickname ?? number ?? "Unknown";
            set => SetProperty(ref nickname, value);
        }

        private double maxOdometerReading;
        public double? MaxOdometerReading
        {
            get => maxOdometerReading;
            set => SetProperty(ref maxOdometerReading, value ?? 0);
        }

        private DateTime? maxRefueledAt;
        public DateTime? MaxRefueledAt
        {
            get => maxRefueledAt;
            set => SetProperty(ref maxRefueledAt, value);
        }

        private Organization _organization;
        [JsonApiRelationship("organization")]
        public Organization Organization
        {
            get => _organization;
            set => SetProperty(ref _organization, value);
        }
    }
}
