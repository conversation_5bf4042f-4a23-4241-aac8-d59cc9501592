﻿using System;
using Prism.Mvvm;
using ReactiveUI;

namespace NCX.Mobile.Models
{
    public class LoadOperationUpdate : BindableBase
    {
        public LoadOperationUpdate(int id, LoadOperationStatus status, DateTime updateAt)
        {
            Id = id;
            Status = status;
            UpdatedAt = updateAt;
        }

        private int _id;
        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private LoadOperationStatus _status;
        public LoadOperationStatus Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        private DateTime _updatedAt;
        public DateTime UpdatedAt
        {
            get => _updatedAt;
            set => SetProperty(ref _updatedAt, value);
        }

        public override string ToString()
        {
            return $"LoadOperationId:{Id}, Status:{Status}, UpdatedAt: {UpdatedAt}";
        }
    }
}
