﻿using System;
using NCX.Mobile.Models.Api;
using ReactiveUI;

namespace NCX.Mobile.Models
{
    public class DriverLocationUpdate
    {
        public DriverLocationUpdate(
            int      driverId   , 
            double   latitude   , 
            double   longitutide, 
            DateTime locatedAt
            )
        {
            Id        = driverId   ;
            Latitude  = latitude   ;
            Longitude = longitutide;
            LocatedAt = locatedAt  ;
        }

        public int      Id        { get; }

        public double   Latitude  { get; }

        public double   Longitude { get; }

        public DateTime LocatedAt { get; }


        public override string ToString()
        {
            return $"Latitude:{Latitude}, Longitude:{Longitude}, LocatedAt: {LocatedAt}";
        }
    }
}
