﻿using System;
using System.Collections.Generic;
using System.Linq;
using JsonApiNet.Attributes;
using Prism.Mvvm;

namespace NCX.Mobile.Models
{
    public class Location : BindableBase
    {
        private int _id;
        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private string _companyName;
        [JsonApiAttribute("company-name")]
        public string CompanyName
        {
            get => _companyName;
            set => SetProperty(ref _companyName, value);
        }

        private string _contactName;
        [JsonApiAttribute("contact-name")]
        public string ContactName
        {
            get => _contactName;
            set => SetProperty(ref _contactName, value);
        }

        private string _contactNumber;
        [JsonApiAttribute("contact-number")]
        public string ContactNumber
        {
            get => _contactNumber;
            set => SetProperty(ref _contactNumber, value);
        }

        private decimal _miscFee;
        public decimal MiscFee
        {
            get => _miscFee;
            set => SetProperty(ref _miscFee, value);
        }

        private decimal _lateFee;
        public decimal LateFee
        {
            get => _lateFee;
            set => SetProperty(ref _lateFee, value);
        }

        private decimal _entranceFee;
        public decimal EntranceFee
        {
            get => _entranceFee;
            set => SetProperty(ref _entranceFee, value);
        }

        private decimal _unloadingFee;
        public decimal UnloadingFee
        {
            get => _unloadingFee;
            set => SetProperty(ref _unloadingFee, value);
        }

        private decimal _palletExchangeFee;
        public decimal PalletExchangeFee
        {
            get => _palletExchangeFee;
            set => SetProperty(ref _palletExchangeFee, value);
        }

        private string _instructions;
        public string Instructions
        {
            get => _instructions;
            set => SetProperty(ref _instructions, value);
        }

        private bool _appointmentRequried;
        public bool AppointmentRequired
        {
            get => _appointmentRequried;
            set => SetProperty(ref _appointmentRequried, value);
        }

        private string _appointmentContactName;
        public string AppointmentContactName
        {
            get => _appointmentContactName;
            set => SetProperty(ref _appointmentContactName, value);
        }

        private string _appointmentContactNumber;
        public string AppointmentContactNumber
        {
            get => _appointmentContactNumber;
            set => SetProperty(ref _appointmentContactNumber, value);
        }

        private bool _hasLiftingEquipment;
        public bool HasLiftingEquipment
        {
            get => _hasLiftingEquipment;
            set => SetProperty(ref _hasLiftingEquipment, value);
        }

        private bool _hasLoadingDock;
        public bool HasLoadingDock
        {
            get => _hasLoadingDock;
            set => SetProperty(ref _hasLoadingDock, value);
        }

        private DateTime _createdAt;
        public DateTime CreatedAt
        {
            get => _createdAt;
            set => SetProperty(ref _createdAt, value);
        }

        private DateTime _updatedAt;
        public DateTime UpdatedAt
        {
            get => _updatedAt;
            set => SetProperty(ref _updatedAt, value);
        }

        private IEnumerable<LocationOperatingHour> _operatingHours;
        public IEnumerable<LocationOperatingHour> OperatingHours
        {
            get => _operatingHours;
            set => SetProperty(ref _operatingHours, value, () => RaisePropertyChanged(nameof(OrderedOperatingHours)));
        }

        private OrganizationAddress _address;
        public OrganizationAddress Address
        {
            get => _address;
            set => SetProperty(ref _address, value);
        }

        public IEnumerable<LocationOperatingHour> OrderedOperatingHours
        {
            get { return OperatingHours?.OrderBy(h => h.Position); }
        }
    }
}
