﻿using ReactiveUI;

namespace NCX.Mobile.Models
{
    public class LoadDelay : ReactiveObject
    {
        private double? _latitude;
        public double? Latitude
        {
            get => _latitude;
            set => this.RaiseAndSetIfChanged(ref _latitude, value);
        }

        private double? _longitude;
        public double? Longitude
        {
            get => _longitude;
            set => this.RaiseAndSetIfChanged(ref _longitude, value);
        }

        private LoadDelayReason _reason;
        public LoadDelayReason Reason
        {
            get => _reason;
            set => this.RaiseAndSetIfChanged(ref _reason, value);
        }
    }
}
