﻿using System;
using NCX.Mobile.Models.Api;
using Prism.Mvvm;

namespace NCX.Mobile.Models
{
    public class LoadTrackingUpdate : BindableBase
    {
        private double _latitude;
        public double Latitude
        {
            get => _latitude;
            set => SetProperty(ref _latitude, value);
        }

        private double _longitude;
        public double Longitude
        {
            get => _longitude;
            set => SetProperty(ref _longitude, value);
        }

        private double _speed;
        public double Speed
        {
            get => _speed;
            set => SetProperty(ref _speed, value);
        }

        private double _accuracy;
        public double Accuracy
        {
            get => _accuracy;
            set => SetProperty(ref _accuracy, value);
        }

        private DateTime _createdAt;
        public DateTime CreatedAt
        {
            get => _createdAt;
            set => SetProperty(ref _createdAt, value);
        }

        private Organization _organization;
        public Organization Organization
        {
            get => _organization;
            set => SetProperty(ref _organization, value);
        }

        private Haul _haul;
        public Haul Haul
        {
            get => _haul;
            set => SetProperty(ref _haul, value);
        }

        private Load _load;
        public Load Load
        {
            get => _load;
            set => SetProperty(ref _load, value);
        }

        private LoadOperation _operation;
        public LoadOperation Operation
        {
            get => _operation;
            set => SetProperty(ref _operation, value);
        }

        public override string ToString()
        {
            return $"Latitude:{Latitude}, Longitude:{Longitude}, Speed:{Speed}, Accuracy:{Accuracy}, CreatedAt: {CreatedAt}, Organization: {Organization?.Id}, Haul: {Haul?.Id}, Load: {Load?.Id}, Operation: {Operation?.Id}";
        }
    }
}
