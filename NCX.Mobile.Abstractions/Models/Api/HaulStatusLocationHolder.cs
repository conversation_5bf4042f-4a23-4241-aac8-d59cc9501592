﻿using ReactiveUI;

namespace NCX.Mobile.Models
{
    public class HaulStatusLocationHolder : ReactiveObject
    {
        private string _id;
        public string Id
        {
            get => _id;
            set => this.RaiseAndSetIfChanged(ref _id, value);
        }

        private string _companyName;
        public string CompanyName
        {
            get => _companyName;
            set => this.RaiseAndSetIfChanged(ref _companyName, value);
        }

        private string _contactName;
        public string ContactName
        {
            get => _contactName;
            set => this.RaiseAndSetIfChanged(ref _contactName, value);
        }

        private string _contactNumber;
        public string ContactNumber
        {
            get => _contactNumber;
            set => this.RaiseAndSetIfChanged(ref _contactNumber, value);
        }

        private double _latitude;
        public double Latitude
        {
            get => _latitude;
            set => this.RaiseAndSetIfChanged(ref _latitude, value);
        }

        private double _longitude;
        public double Longitude
        {
            get => _longitude;
            set => this.RaiseAndSetIfChanged(ref _longitude, value);
        }

        private string _verifiedAddress;
        public string VerifiedAddress
        {
            get => _verifiedAddress;
            set => this.RaiseAndSetIfChanged(ref _verifiedAddress, value);
        }

        private string _type;
        public string Type
        {
            get => _type;
            set => this.RaiseAndSetIfChanged(ref _type, value);
        }

        private string _status;
        public string Status
        {
            get => _status;
            set => this.RaiseAndSetIfChanged(ref _status, value);
        }

        private bool _shouldEnable;
        public bool ShouldEnable
        {
            get => _shouldEnable;
            set => this.RaiseAndSetIfChanged(ref _shouldEnable, value);
        }
    }
}
