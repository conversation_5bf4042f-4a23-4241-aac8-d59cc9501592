﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Prism.Mvvm;

namespace NCX.Mobile.Models
{
    public class LoadOperation : BindableBase
    {
        private int _id;
        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private DateTime _expectedDate;
        public DateTime ExpectedDate
        {
            get => _expectedDate;
            set => SetProperty(ref _expectedDate, value);
        }

        private string _referenceNumber;
        public string ReferenceNumber
        {
            get => _referenceNumber;
            set => SetProperty(ref _referenceNumber, value);
        }

        private string _purchaseOrderNumber;
        public string PurchaseOrderNumber
        {
            get => _purchaseOrderNumber;
            set => SetProperty(ref _purchaseOrderNumber, value);
        }

        private LoadOperationStatus _status;
        public LoadOperationStatus Status
        {
            get => _status;
            set => SetProperty(ref _status, value, OnStatusChanged);
        }

        private void OnStatusChanged()
        {
            RaisePropertyChanged(nameof(IsActive));
            RaisePropertyChanged(nameof(IsActiveOrPending));
        }

        private LoadOperationType _sourceType;
        public LoadOperationType SourceType
        {
            get => _sourceType;
            set => SetProperty(ref _sourceType, value, () => RaisePropertyChanged(nameof(IsPickup)));
        }

        private DateTime _createdAt;
        public DateTime CreatedAt
        {
            get => _createdAt;
            set => SetProperty(ref _createdAt, value);
        }

        private DateTime _updatedAt;
        public DateTime UpdatedAt
        {
            get => _updatedAt;
            set => SetProperty(ref _updatedAt, value);
        }

        private Location _location;
        public Location Location
        {
            get => _location;
            set => SetProperty(ref _location, value);
        }

        private IEnumerable<Cargo> _cargos;
        public IEnumerable<Cargo> Cargos
        {
            get => _cargos;
            set => SetProperty(ref _cargos, value);
        }

        [JsonIgnore]
        public bool IsPickup => SourceType == LoadOperationType.Pickup;

        [JsonIgnore]
        public bool IsActive => Status == LoadOperationStatus.En_Route || Status == LoadOperationStatus.Checked_In;

        [JsonIgnore]
        public bool IsActiveOrPending => IsActive || Status == LoadOperationStatus.Pending;

        //~LoadOperation()
        //{
        //    _isActiveHelper.Dispose();
        //    _isActiveHelper = null;

        //    _isActiveOrPendingHelper.Dispose();
        //    _isActiveOrPendingHelper = null;

        //    _isPickupHelper.Dispose();
        //    _isPickupHelper = null;
        //}
    }
}
