﻿using Newtonsoft.Json;

namespace NCX.Mobile.Models
{
    public class UserSession
    {
        [JsonProperty("user")]
        public User User { get; set; }

        [JsonProperty("authToken")]
        public AuthToken AuthToken { get; set; }

        [JsonProperty("activeLoadOperationInfo")]
        public ActiveLoadInfo ActiveLoadOperationInfo { get; set; }

        public UserSession()
        {
        }

        public UserSession(User user, AuthToken authToken)
        {
            User = user;
            AuthToken = authToken;
        }
    }
}
