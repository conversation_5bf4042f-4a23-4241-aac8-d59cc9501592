﻿namespace NCX.Mobile.Models
{
    public class ActiveLoadInfo
    {
        public int HaulId { get; set; }
        public int LoadId { get; set; }
        public int? LoadOperationId { get; set; }
        public int? OrganizationId { get; set; }
        public int? PowerUnitId { get; set; }

        public ActiveLoadInfo()
        {
        }

        public ActiveLoadInfo(int haulId, int loadId, int? loadOperationId, int? organizationId, int? powerUnitId)
        {
            HaulId = haulId;
            LoadId = loadId;
            LoadOperationId = loadOperationId;
            OrganizationId = organizationId;
            PowerUnitId = powerUnitId;
        }

        public override bool Equals(object obj)
        {
            var ali = obj as ActiveLoadInfo;

            if (ali == null)
            {
                return false;
            }

            return ali.HaulId == HaulId &&
                   ali.LoadId == LoadId &&
                   ali.LoadOperationId == LoadOperationId &&
                   ali.OrganizationId == OrganizationId;
        }

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }

        public static bool Compare(ActiveLoadInfo o1, ActiveLoadInfo o2)
        {
            if (o1 == o2)
            {
                return true;
            }

            return o1 != null && o1.Equals(o2);
        }
    }
}
