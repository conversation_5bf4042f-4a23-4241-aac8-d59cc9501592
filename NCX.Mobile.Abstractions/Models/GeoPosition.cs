﻿using Shiny.Locations;

namespace NCX.Mobile.Models
{
    public class GeoPosition
    {
        public double Latitude { get; set; }

        public double Longitude { get; set; }

        // Meters / second
        public double? Speed { get; set; }

        public double Accuracy { get; set; }

        public GeoPosition()
        {
        }

        public GeoPosition(double latitude, double longitude, double? speed, double accuracy)
        {
            Latitude = latitude;
            Longitude = longitude;
            Speed = speed;
            Accuracy = accuracy;
        }

        public override string ToString()
        {
            return $"Latitude: {Latitude}, Longitude: {Longitude}, Speed: {Speed}, Accuracy: {Accuracy}";
        }

        public static GeoPosition FromReading(GpsReading reading) =>
            new GeoPosition(reading.Position.Latitude, reading.Position.Longitude, reading.Speed, reading.PositionAccuracy);
    }
}
