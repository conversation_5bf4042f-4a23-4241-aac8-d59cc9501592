﻿namespace NCX.Mobile.Models
{
    public interface IAppSettings
    {
        string ApiHost { get; set; }
        string Language { get; set; }
        int? LastLoggedInUserId { get; set; }
        bool? LoadsNeedRefresh { get; set; }
        string PushNotificationToken { get; set; }
        bool? SessionClosedBecauseExpired { get; set; }
        bool ShouldMergeHaulCache { get; set; }
        bool ShouldNavigateToLoginOnNextAppResume { get; set; }
        bool ShouldShowNewLoadsOnNextAppResume { get; set; }
        int Version { get; set; }
    }
}