﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <RootNamespace>NCX.Mobile</RootNamespace>
    <LangVersion>latest</LangVersion>

	<!-- Define XAML Namespace Mappings -->
	<XamlNamespaceMappings>
		xmlns:models="clr-namespace:NCX.Mobile.Models;assembly=NCX.Mobile"
		xmlns:properties="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile"
	  </XamlNamespaceMappings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Humanizer.Core" Version="2.14.1" />
	<PackageReference Include="Microsoft.Maui.Controls" Version="8.0.100" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Prism.Core" Version="9.0.537" />
    <PackageReference Include="Shiny.Locations" Version="3.3.4" />
    <PackageReference Include="Refit" Version="8.0.0" />
    <PackageReference Include="ReactiveUI" Version="20.1.63" />
    <PackageReference Include="Shiny.Notifications" Version="3.3.3" />
    <PackageReference Include="System.Threading.Tasks.Extensions" Version="4.6.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\JsonApiNETPCL\JsonApiNETPCL.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
    <EmbeddedResource Update="Properties\Resources.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Properties\Resources.resx">
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
  </ItemGroup>

</Project>
