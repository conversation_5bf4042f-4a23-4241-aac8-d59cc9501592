﻿using System.Threading.Tasks;
using Refit;
using NCX.Mobile.Models;
using System.Net.Http;
using System.Threading;

namespace NCX.Mobile.Services
{
    [Headers("Accept: application/json")]
    public interface INCXApi
    {
        HttpClient Client { get; }

        [Post("/drivers/sign_in")]
        Task<AuthToken> LoginAsync([Body(BodySerializationMethod.UrlEncoded)] LoginRequest loginRequest, [Header("X-Driver-DeviceOS")] string deviceOS, [Header("X-Driver-DeviceToken")] string deviceToken);

        [Delete("/drivers/sign_out")]
        Task LogoutAsync([Header("X-Driver-Email")] string username, [Header("X-Driver-Token")] string token);

        [Get("/drivers/{driverId}")]
        Task<string> GetDriverAsync(int driverId, [Header("X-Driver-Email")] string username, [Header("X-Driver-Token")] string token, CancellationToken cancellationToken);

        [Post("/drivers/password")]
        Task<string> ResetUserPasswordAsync([Body(BodySerializationMethod.UrlEncoded)] ResetUserPasswordRequest email);

        [Put("/drivers/update_password")]
        Task<string> UpdateDriverPasswordAsync([Body(BodySerializationMethod.UrlEncoded)] UpdateUserPasswordRequest info, [Header("X-Driver-Email")] string username, [Header("X-Driver-Token")] string token);

        [Get("/hauls?filter[driver_id]={driverId}&filter[status]=not_finished&page[number]=1&page[size]=50&include=loads.power-unit,loads,loads.operations,loads.operations.cargos,loads.operations.location,loads.operations.location.address,loads.operations,loads.images,loads.operations.location.operating-hours,loads.delays,organization")]
        Task<string> GetHaulsAsync(int driverId, [Header("X-Driver-Email")] string username, [Header("X-Driver-Token")] string token, CancellationToken cancellationToken);

        [Put("/load-operations/{loadOperationId}")]
        [Headers("Content-Type: application/vnd.api+json")]
        Task<string> UpdateLoadOperationAsync(int loadOperationId, [Header("X-Driver-Email")] string username, [Header("X-Driver-Token")] string token, [Body] string payload);

        [Put("/loads/{loadId}")]
        [Headers("Content-Type: application/vnd.api+json")]
        Task<string> UpdateLoadAsync(int loadId, [Header("X-Driver-Email")] string username, [Header("X-Driver-Token")] string token, [Body] string payload);

        [Put("/drivers/{driverId}")]
        [Headers("Content-Type: application/vnd.api+json")]
        Task<string> UpdateDriverLocationAsync(int driverId, [Header("X-Driver-Email")] string username, [Header("X-Driver-Token")] string token, [Body] string payload);

        [Post("/load-trackings")]
        [Headers("Content-Type: application/vnd.api+json")]
        Task<string> PostLoadTrackingAsync([Header("X-Driver-Email")] string username, [Header("X-Driver-Token")] string token, [Body] string payload);

        [Post("/load-operations/{loadOperationId}/images2")]
        [Headers("Content-Type: application/vnd.api+json")]
        Task<string> PostPODListAsync(int loadOperationId, [Header("X-Driver-Email")] string username, [Header("X-Driver-Token")] string token, [Body] string payload);

        [Post("/load-delays")]
        [Headers("Content-Type: application/vnd.api+json")]
        Task<string> PostLoadDelayAsync([Header("X-Driver-Email")] string username, [Header("X-Driver-Token")] string token, [Body] string payload);

        [Post("/drivers/{driverId}/update_device_token")]
        [Headers("Content-Type: application/vnd.api+json")]
        Task<string> UpdateUserDeviceTokenAsync(int driverId, [Header("X-Driver-Email")] string username, [Header("X-Driver-Token")] string token, [Header("X-Driver-DeviceToken")] string deviceToken, [Header("X-Driver-DeviceOS")] string deviceOS);

        [Post("/fuel-stops")]
        [Headers("Content-Type: application/vnd.api+json")]
        Task<string> CreateFuelStop([Header("X-Driver-Email")] string username, [Header("X-Driver-Token")] string token, [Body] string payload);

        [Get("/power-units?include=organization")]
        [Headers("Content-Type: application/vnd.api+json")]
        Task<string> GetPowerUnits([Header("X-Driver-Email")] string username, [Header("X-Driver-Token")] string token);
    }
}