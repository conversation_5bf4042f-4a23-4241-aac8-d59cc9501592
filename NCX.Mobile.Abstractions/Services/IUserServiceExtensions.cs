﻿using System.Threading.Tasks;

namespace NCX.Mobile.Services
{
    public static class IUserServiceExtensions
    {
        public static async Task<bool> HasActiveLoad(this IUserService userService)
        {
            var activeLoadOperationInfo = userService.ActiveLoadInfo;
            if (activeLoadOperationInfo != null && activeLoadOperationInfo.LoadOperationId != null)
            {
                if(!userService.IsActive)
                {
                    await userService.StartTravelAsync();
                }

                return true;
            }

            return false;
        }
    }
}
