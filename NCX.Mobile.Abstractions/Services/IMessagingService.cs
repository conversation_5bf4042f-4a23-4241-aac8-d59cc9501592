﻿namespace NCX.Mobile.Services
{
    // TODO: Deprecate in favor of Xamarin Essentials
    public interface IMessagingService
    {
        /// <summary>
        /// Gets a value indicating whether the device can make a phone call
        /// </summary>
        bool CanMakePhoneCall { get; }

        /// <summary>
        /// Make a phone call using the default dialer UI on the device.
        /// </summary>
        /// <param name="number">Number to phone</param>
        /// <param name="name">Optional name of the contact being phoned used for visual display
        /// on some platforms</param>
        void MakePhoneCall(string number, string name = null);

        bool CanSendEmail { get; }

        void StartComposeEmail(string to);
    }
}
