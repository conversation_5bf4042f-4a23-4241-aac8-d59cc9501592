﻿using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using Shiny.Locations;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace NCX.Mobile.Services
{
    public interface IUserService
    {
        bool IsLoggedIn { get; }

        bool IsActive { get; }

        int? PowerUnitId { get; set; }

        IObservable<bool> WhenIsActiveChanged();

        ActiveLoadInfo ActiveLoadInfo { get; }

        UserSession CurrentSession { get; }

        Task<bool> LoginAsync(string username, string password);

        Task LogoutAsync(bool reasonIsSessionExpired = false);

        Task StartGpsListener();

        Task StopGpsListener();

        IObservable<GpsReading> LastLocation { get; }

        Task<User> GetDriverProfileAsync(CancellationToken cancellationToken = default);

        Task<bool?> UpdateUserPasswordAsync(string currentPassword, string newPassword, ApiServiceExceptionHandler errorHandler);

        Task UpdateUserDeviceTokenAsync(string newDeviceToken);

        Task RefreshDriverHaulsFromOnlineAsync();

        Task StartTravelAsync();

        Task StopTravelAsync();
    }
}
