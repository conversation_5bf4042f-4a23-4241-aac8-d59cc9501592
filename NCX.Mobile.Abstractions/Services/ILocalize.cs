using NCX.Mobile.Models;
using System;
using System.Collections.Generic;
using System.Globalization;

namespace NCX.Mobile.Services
{
    public interface ILocalize
    {
        string CurrentLanguageCode { get; }

        void Initialize();

        void SetLanguage(string culture);

        IEnumerable<CultureInfo> GetCultures();

        IEnumerable<Language> GetAllSupportedLanguages(Func<CultureInfo, bool> predicate = null);
    }
}
