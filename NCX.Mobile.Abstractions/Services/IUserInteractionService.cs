﻿using Prism.Mvvm;
using Prism.Navigation;
using System;
using System.Threading.Tasks;

namespace NCX.Mobile.Services
{
    public interface IUserInteractionService
    {
        Task ShowAlertAsync(string message, string title, string OKButtonText = null);

        Task<bool> ShowConfirmationAsync(string message, string title, string OKButtonText = null, string CancelButtonText = null);

        Task<string> ShowActionSheetAsync(string title, string cancelButtonText, params string[] actions);

        Task<TResult> ShowPopupAsync<TViewModel, TResult>(INavigationParameters navParameters = null)
            where TViewModel : BindableBase, INavigationResultProducer<TResult>;

        void ShowLoading();

        void HideLoading();

        void NavigateTo(Uri uri);
    }
}
