﻿using NCX.Mobile.Models;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace NCX.Mobile.Services
{
    public interface IHaulService
    {
        Task<IEnumerable<Haul>> GetDriverHaulsAsync(bool fromCache, CancellationToken cancellationToken = default);
        Task UpdateLoadOperationStatusAsync(Haul haul, int loadId, int loadOperationId, LoadOperationStatus newLoadOperationStatus);
        Task ClearDataOnUserLogoutAsync();
        Task UpdateLoadToViewedStatusAsync(Haul haul);
        Task AddLoadDelayAsync(Haul haul, int loadId, LoadDelay loadDelay);
    }
}
