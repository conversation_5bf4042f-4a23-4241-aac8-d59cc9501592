﻿using System.Threading.Tasks;
using NCX.Mobile.Models;
using System.Collections.Generic;
using System.Threading;
using NCX.Mobile.Models.Api;

namespace NCX.Mobile.Services
{
    public interface INCXApiService
    {
        Task<AuthToken> LoginAsync(LoginRequest loginRequest, string deviceOS, string deviceToken);

        Task LogoutAsync(AuthToken auth);

        Task<User> GetDriverAsync(AuthToken auth, CancellationToken cancellationToken);

        Task ResetUserPasswordAsync(string email);

        Task UpdateUserPasswordAsync(string currentPassword, string newPassword, AuthToken auth);

        Task<IEnumerable<Haul>> GetHaulsAsync(AuthToken auth, CancellationToken cancellationToken);

        Task UpdateLoadOperationAsync(AuthToken auth, LoadOperationUpdate loadOperationUpdate);

        Task PostLoadTrackingAsync(AuthToken auth, LoadTrackingUpdate loadTracking);

        Task UpdateDriverLocationAsync(AuthToken auth, DriverLocationUpdate driverLocationUpdate);

        Task PostPODAsync(AuthToken auth, SendProofOfDeliveryInfo pod);

        Task UpdateLoadAsync(AuthToken authToken, LoadUpdate loadUpdate);

        Task PostLoadDelayAsync(AuthToken auth, SendLoadDelayInfo info);

        Task UpdateUserDeviceTokenAsync(AuthToken auth, string deviceToken, string deviceOS);

        Task<IEnumerable<PowerUnit>> GetPowerUnits(AuthToken auth);

        Task PostFuelStop(AuthToken auth, FuelStop fuelStop);
    }
}
