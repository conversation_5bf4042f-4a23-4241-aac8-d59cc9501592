﻿using NCX.Mobile.Models.Cache.PushOperations;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace NCX.Mobile.Services
{
    public interface IPushOperationCacheService
    {
        Task AddAsync(IPushOperation operation);

        Task DeleteAsync(IPushOperation operation);

        Task<IEnumerable<IPushOperation>> GetAllAsync();

        Task<IEnumerable<IPushOperation>> GetAllAsync(IEnumerable<PushOperationType> pushOperationTypeList);

        Task ClearAsync();
    }
}
