﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace NCX.Mobile.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("NCX.Mobile.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active Load.
        /// </summary>
        public static string ActiveLoadHeader {
            get {
                return ResourceManager.GetString("ActiveLoadHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Day.
        /// </summary>
        public static string AllDayHoursOfOperation {
            get {
                return ResourceManager.GetString("AllDayHoursOfOperation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to API endpoint host changed to.
        /// </summary>
        public static string APIHostChangedMessageText {
            get {
                return ResourceManager.GetString("APIHostChangedMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appt Contact Name.
        /// </summary>
        public static string ApptContactNameText {
            get {
                return ResourceManager.GetString("ApptContactNameText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appt Contact Number.
        /// </summary>
        public static string ApptContactNumberText {
            get {
                return ResourceManager.GetString("ApptContactNumberText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appt Required?.
        /// </summary>
        public static string ApptRequiredText {
            get {
                return ResourceManager.GetString("ApptRequiredText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A new application update is available.
        /// </summary>
        public static string AppUpdateAvailableMessage {
            get {
                return ResourceManager.GetString("AppUpdateAvailableMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application update.
        /// </summary>
        public static string AppUpdateAvailableTitle {
            get {
                return ResourceManager.GetString("AppUpdateAvailableTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update.
        /// </summary>
        public static string AppUpdateButtonText {
            get {
                return ResourceManager.GetString("AppUpdateButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not now.
        /// </summary>
        public static string AppUpdateNotNowButtonText {
            get {
                return ResourceManager.GetString("AppUpdateNotNowButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have been logged out.
        ///Check to make sure you were not logged in from another device..
        /// </summary>
        public static string AutomaticLogoutMessageText {
            get {
                return ResourceManager.GetString("AutomaticLogoutMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login required.
        /// </summary>
        public static string AutomaticLogoutTitleText {
            get {
                return ResourceManager.GetString("AutomaticLogoutTitleText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Back to Login.
        /// </summary>
        public static string BackToLoginText {
            get {
                return ResourceManager.GetString("BackToLoginText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string CancelButtonText {
            get {
                return ResourceManager.GetString("CancelButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Canceled.
        /// </summary>
        public static string CanceledText {
            get {
                return ResourceManager.GetString("CanceledText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can only update the status of an active pickup or dropoff..
        /// </summary>
        public static string CanUpdateActivePickupOrDropOffMessageText {
            get {
                return ResourceManager.GetString("CanUpdateActivePickupOrDropOffMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Another pickup or dropoff is active.
        /// </summary>
        public static string CanUpdateActivePickupOrDropOffTitleText {
            get {
                return ResourceManager.GetString("CanUpdateActivePickupOrDropOffTitleText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Take a photo of the Proof of Delivery (POD) or select an existing photo from the library. Be sure to clearly capture the signature of the proof of delivery in the photo.
        ///
        ///If the Proof of Delivery contains multiple pages, take one photo at a time..
        /// </summary>
        public static string CapturePODInfoText {
            get {
                return ResourceManager.GetString("CapturePODInfoText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cargo Destination Details.
        /// </summary>
        public static string CargoDestinationDetailsText {
            get {
                return ResourceManager.GetString("CargoDestinationDetailsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cargo Details.
        /// </summary>
        public static string CargoDetailsText {
            get {
                return ResourceManager.GetString("CargoDetailsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} of {1}.
        /// </summary>
        public static string CargoItemHeaderFormatText {
            get {
                return ResourceManager.GetString("CargoItemHeaderFormatText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bag.
        /// </summary>
        public static string CargoUnitBag {
            get {
                return ResourceManager.GetString("CargoUnitBag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bin (Cardboard).
        /// </summary>
        public static string CargoUnitBinCardboard {
            get {
                return ResourceManager.GetString("CargoUnitBinCardboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bin (Plastic).
        /// </summary>
        public static string CargoUnitBinPlastic {
            get {
                return ResourceManager.GetString("CargoUnitBinPlastic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Box.
        /// </summary>
        public static string CargoUnitBox {
            get {
                return ResourceManager.GetString("CargoUnitBox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk.
        /// </summary>
        public static string CargoUnitBulk {
            get {
                return ResourceManager.GetString("CargoUnitBulk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Carton.
        /// </summary>
        public static string CargoUnitCarton {
            get {
                return ResourceManager.GetString("CargoUnitCarton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Container.
        /// </summary>
        public static string CargoUnitContainer {
            get {
                return ResourceManager.GetString("CargoUnitContainer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Crate.
        /// </summary>
        public static string CargoUnitCrate {
            get {
                return ResourceManager.GetString("CargoUnitCrate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RPC.
        /// </summary>
        public static string CargoUnitRpc {
            get {
                return ResourceManager.GetString("CargoUnitRpc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tote.
        /// </summary>
        public static string CargoUnitTote {
            get {
                return ResourceManager.GetString("CargoUnitTote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change language.
        /// </summary>
        public static string ChangeLanguageText {
            get {
                return ResourceManager.GetString("ChangeLanguageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change password.
        /// </summary>
        public static string ChangePasswordBtnText {
            get {
                return ResourceManager.GetString("ChangePasswordBtnText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Checked In.
        /// </summary>
        public static string CheckedInText {
            get {
                return ResourceManager.GetString("CheckedInText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        public static string CloseButtonText {
            get {
                return ResourceManager.GetString("CloseButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        public static string CloseText {
            get {
                return ResourceManager.GetString("CloseText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company.
        /// </summary>
        public static string Company {
            get {
                return ResourceManager.GetString("Company", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completed.
        /// </summary>
        public static string CompletedText {
            get {
                return ResourceManager.GetString("CompletedText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You must complete all pickups before drop-offs..
        /// </summary>
        public static string CompletePickupFirstMessageText {
            get {
                return ResourceManager.GetString("CompletePickupFirstMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmation.
        /// </summary>
        public static string ConfirmationText {
            get {
                return ResourceManager.GetString("ConfirmationText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm your new password.
        /// </summary>
        public static string ConfirmYourNewPassword {
            get {
                return ResourceManager.GetString("ConfirmYourNewPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NCX is having trouble connecting to the server. Try again later..
        /// </summary>
        public static string ConnectionErrorMessageText {
            get {
                return ResourceManager.GetString("ConnectionErrorMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Name.
        /// </summary>
        public static string ContactNameText {
            get {
                return ResourceManager.GetString("ContactNameText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Number.
        /// </summary>
        public static string ContactNumberText {
            get {
                return ResourceManager.GetString("ContactNumberText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send us an email.
        /// </summary>
        public static string ContactUsByEmail {
            get {
                return ResourceManager.GetString("ContactUsByEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call us.
        /// </summary>
        public static string ContactUsByPhone {
            get {
                return ResourceManager.GetString("ContactUsByPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copyright © National Carrier Exchange
        ///All Rights Reserved.
        /// </summary>
        public static string CopyrightText {
            get {
                return ResourceManager.GetString("CopyrightText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current location.
        /// </summary>
        public static string CurrentLocation {
            get {
                return ResourceManager.GetString("CurrentLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dashboard.
        /// </summary>
        public static string Dashboard {
            get {
                return ResourceManager.GetString("Dashboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your internet connection appears to be offline.
        /// </summary>
        public static string DataConnectivityIssue {
            get {
                return ResourceManager.GetString("DataConnectivityIssue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minor Accident.
        /// </summary>
        public static string DelayReasonAccidentText {
            get {
                return ResourceManager.GetString("DelayReasonAccidentText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bad Weather.
        /// </summary>
        public static string DelayReasonBadWeatherText {
            get {
                return ResourceManager.GetString("DelayReasonBadWeatherText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minor Breakdown.
        /// </summary>
        public static string DelayReasonBreakdownText {
            get {
                return ResourceManager.GetString("DelayReasonBreakdownText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Flat Tire.
        /// </summary>
        public static string DelayReasonFlatTireText {
            get {
                return ResourceManager.GetString("DelayReasonFlatTireText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Heavy Traffic.
        /// </summary>
        public static string DelayReasonHeavyTrafficText {
            get {
                return ResourceManager.GetString("DelayReasonHeavyTrafficText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Road Closure.
        /// </summary>
        public static string DelayReasonRoadClosureText {
            get {
                return ResourceManager.GetString("DelayReasonRoadClosureText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleted.
        /// </summary>
        public static string DeletedText {
            get {
                return ResourceManager.GetString("DeletedText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Number.
        /// </summary>
        public static string DeliverNumberText {
            get {
                return ResourceManager.GetString("DeliverNumberText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Details.
        /// </summary>
        public static string DetailsText {
            get {
                return ResourceManager.GetString("DetailsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Created.
        /// </summary>
        public static string DriverDateCreated {
            get {
                return ResourceManager.GetString("DriverDateCreated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to State.
        /// </summary>
        public static string DriverLicenseState {
            get {
                return ResourceManager.GetString("DriverLicenseState", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your Profile.
        /// </summary>
        public static string DriverProfileTitle {
            get {
                return ResourceManager.GetString("DriverProfileTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Dropoffs.
        /// </summary>
        public static string DropoffCountPlural {
            get {
                return ResourceManager.GetString("DropoffCountPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Dropoff.
        /// </summary>
        public static string DropoffCountSingular {
            get {
                return ResourceManager.GetString("DropoffCountSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dropoff Location.
        /// </summary>
        public static string DropOffLocationText {
            get {
                return ResourceManager.GetString("DropOffLocationText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email address or Phone number.
        /// </summary>
        public static string EmailText {
            get {
                return ResourceManager.GetString("EmailText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to En Route.
        /// </summary>
        public static string EnRouteText {
            get {
                return ResourceManager.GetString("EnRouteText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entrance Fees.
        /// </summary>
        public static string EntranceFeesText {
            get {
                return ResourceManager.GetString("EntranceFeesText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Air-Ride Van.
        /// </summary>
        public static string EquipmentAirRideVan {
            get {
                return ResourceManager.GetString("EquipmentAirRideVan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto Carrier.
        /// </summary>
        public static string EquipmentAutoCarrier {
            get {
                return ResourceManager.GetString("EquipmentAutoCarrier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beverage Trailer.
        /// </summary>
        public static string EquipmentBeverageTrailer {
            get {
                return ResourceManager.GetString("EquipmentBeverageTrailer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Boat Trailer (Double).
        /// </summary>
        public static string EquipmentBoatTrailerDouble {
            get {
                return ResourceManager.GetString("EquipmentBoatTrailerDouble", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Boat Trailer (Single).
        /// </summary>
        public static string EquipmentBoatTrailerSingle {
            get {
                return ResourceManager.GetString("EquipmentBoatTrailerSingle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom Dump Trailer.
        /// </summary>
        public static string EquipmentBottomDumpTrailer {
            get {
                return ResourceManager.GetString("EquipmentBottomDumpTrailer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to B-Train Combo.
        /// </summary>
        public static string EquipmentBTrainCombo {
            get {
                return ResourceManager.GetString("EquipmentBTrainCombo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Conestoga Trailer.
        /// </summary>
        public static string EquipmentConestogaTrailer {
            get {
                return ResourceManager.GetString("EquipmentConestogaTrailer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Double Drop.
        /// </summary>
        public static string EquipmentDoubleDrop {
            get {
                return ResourceManager.GetString("EquipmentDoubleDrop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Double Drop Extendable.
        /// </summary>
        public static string EquipmentDoubleDropExtendable {
            get {
                return ResourceManager.GetString("EquipmentDoubleDropExtendable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Double Drop Side Kit.
        /// </summary>
        public static string EquipmentDoubleDropSideKit {
            get {
                return ResourceManager.GetString("EquipmentDoubleDropSideKit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Double Drop Stretch.
        /// </summary>
        public static string EquipmentDoubleDropStretch {
            get {
                return ResourceManager.GetString("EquipmentDoubleDropStretch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dry Van.
        /// </summary>
        public static string EquipmentDryVan {
            get {
                return ResourceManager.GetString("EquipmentDryVan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dump Trailer.
        /// </summary>
        public static string EquipmentDumpTrailer {
            get {
                return ResourceManager.GetString("EquipmentDumpTrailer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End Dump Trailer.
        /// </summary>
        public static string EquipmentEndDumpTrailer {
            get {
                return ResourceManager.GetString("EquipmentEndDumpTrailer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Flatbed.
        /// </summary>
        public static string EquipmentFlatbed {
            get {
                return ResourceManager.GetString("EquipmentFlatbed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Flatbed Side Kit.
        /// </summary>
        public static string EquipmentFlatbedSideKit {
            get {
                return ResourceManager.GetString("EquipmentFlatbedSideKit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Flatbed Stretch.
        /// </summary>
        public static string EquipmentFlatbedStretch {
            get {
                return ResourceManager.GetString("EquipmentFlatbedStretch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Flatbed with Sides.
        /// </summary>
        public static string EquipmentFlatbedwithSides {
            get {
                return ResourceManager.GetString("EquipmentFlatbedwithSides", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Flatbed with Tarps.
        /// </summary>
        public static string EquipmentFlatbedwithTarps {
            get {
                return ResourceManager.GetString("EquipmentFlatbedwithTarps", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Frac Tank (Round Bottom).
        /// </summary>
        public static string EquipmentFracTankRoundBottom {
            get {
                return ResourceManager.GetString("EquipmentFracTankRoundBottom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Frac Tank (Square).
        /// </summary>
        public static string EquipmentFracTankSquare {
            get {
                return ResourceManager.GetString("EquipmentFracTankSquare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fuel Tank (Double).
        /// </summary>
        public static string EquipmentFuelTankDouble {
            get {
                return ResourceManager.GetString("EquipmentFuelTankDouble", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fuel Tank (Single).
        /// </summary>
        public static string EquipmentFuelTankSingle {
            get {
                return ResourceManager.GetString("EquipmentFuelTankSingle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Half-Round End Dump Trailer.
        /// </summary>
        public static string EquipmentHalfRoundEndDumpTrailer {
            get {
                return ResourceManager.GetString("EquipmentHalfRoundEndDumpTrailer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hopper (Double).
        /// </summary>
        public static string EquipmentHopperDouble {
            get {
                return ResourceManager.GetString("EquipmentHopperDouble", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hopper (Grain, Double).
        /// </summary>
        public static string EquipmentHopperGrainDouble {
            get {
                return ResourceManager.GetString("EquipmentHopperGrainDouble", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hopper (Grain, Single).
        /// </summary>
        public static string EquipmentHopperGrainSingle {
            get {
                return ResourceManager.GetString("EquipmentHopperGrainSingle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hopper (Single).
        /// </summary>
        public static string EquipmentHopperSingle {
            get {
                return ResourceManager.GetString("EquipmentHopperSingle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Intermodal 20&apos; Container.
        /// </summary>
        public static string EquipmentIntermodal20Container {
            get {
                return ResourceManager.GetString("EquipmentIntermodal20Container", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Intermodal 40&apos; Container.
        /// </summary>
        public static string EquipmentIntermodal40Container {
            get {
                return ResourceManager.GetString("EquipmentIntermodal40Container", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Live Floor.
        /// </summary>
        public static string EquipmentLiveFloor {
            get {
                return ResourceManager.GetString("EquipmentLiveFloor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Livestock Trailer (Cattle).
        /// </summary>
        public static string EquipmentLivestockTrailerCattle {
            get {
                return ResourceManager.GetString("EquipmentLivestockTrailerCattle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Livestock Trailer (Chickens).
        /// </summary>
        public static string EquipmentLivestockTrailerChickens {
            get {
                return ResourceManager.GetString("EquipmentLivestockTrailerChickens", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Livestock Trailer (Hogs).
        /// </summary>
        public static string EquipmentLivestockTrailerHogs {
            get {
                return ResourceManager.GetString("EquipmentLivestockTrailerHogs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Logger Trailer.
        /// </summary>
        public static string EquipmentLoggerTrailer {
            get {
                return ResourceManager.GetString("EquipmentLoggerTrailer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lowboy.
        /// </summary>
        public static string EquipmentLowboy {
            get {
                return ResourceManager.GetString("EquipmentLowboy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maxi-Cube.
        /// </summary>
        public static string EquipmentMaxiCube {
            get {
                return ResourceManager.GetString("EquipmentMaxiCube", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pneumatic.
        /// </summary>
        public static string EquipmentPneumatic {
            get {
                return ResourceManager.GetString("EquipmentPneumatic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Power Unit Only.
        /// </summary>
        public static string EquipmentPowerUnitOnly {
            get {
                return ResourceManager.GetString("EquipmentPowerUnitOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refrigerated Van.
        /// </summary>
        public static string EquipmentRefrigeratedVan {
            get {
                return ResourceManager.GetString("EquipmentRefrigeratedVan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Removable Gooseneck (RGN).
        /// </summary>
        public static string EquipmentRemovableGooseneckRGN {
            get {
                return ResourceManager.GetString("EquipmentRemovableGooseneckRGN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saddlemount.
        /// </summary>
        public static string EquipmentSaddlemount {
            get {
                return ResourceManager.GetString("EquipmentSaddlemount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Single Drop.
        /// </summary>
        public static string EquipmentSingleDrop {
            get {
                return ResourceManager.GetString("EquipmentSingleDrop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Single Drop Extendable.
        /// </summary>
        public static string EquipmentSingleDropExtendable {
            get {
                return ResourceManager.GetString("EquipmentSingleDropExtendable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Single Drop Side Kit.
        /// </summary>
        public static string EquipmentSingleDropSideKit {
            get {
                return ResourceManager.GetString("EquipmentSingleDropSideKit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Single Drop Stretch.
        /// </summary>
        public static string EquipmentSingleDropStretch {
            get {
                return ResourceManager.GetString("EquipmentSingleDropStretch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Specialized Trailer.
        /// </summary>
        public static string EquipmentSpecializedTrailer {
            get {
                return ResourceManager.GetString("EquipmentSpecializedTrailer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tanker (Chemical).
        /// </summary>
        public static string EquipmentTankerChemical {
            get {
                return ResourceManager.GetString("EquipmentTankerChemical", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tanker (Food-Grade).
        /// </summary>
        public static string EquipmentTankerFoodGrade {
            get {
                return ResourceManager.GetString("EquipmentTankerFoodGrade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tanker (Petroleum).
        /// </summary>
        public static string EquipmentTankerPetroleum {
            get {
                return ResourceManager.GetString("EquipmentTankerPetroleum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tanker (Vacuum).
        /// </summary>
        public static string EquipmentTankerVacuum {
            get {
                return ResourceManager.GetString("EquipmentTankerVacuum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tanker (Water).
        /// </summary>
        public static string EquipmentTankerWater {
            get {
                return ResourceManager.GetString("EquipmentTankerWater", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Equipment Type.
        /// </summary>
        public static string EquipmentTypeText {
            get {
                return ResourceManager.GetString("EquipmentTypeText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Van with Curtains.
        /// </summary>
        public static string EquipmentVanwithCurtains {
            get {
                return ResourceManager.GetString("EquipmentVanwithCurtains", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ventilated Van.
        /// </summary>
        public static string EquipmentVentilatedVan {
            get {
                return ResourceManager.GetString("EquipmentVentilatedVan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        public static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expected.
        /// </summary>
        public static string ExpectedDateText {
            get {
                return ResourceManager.GetString("ExpectedDateText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FINAL DROPOFF.
        /// </summary>
        public static string FinalDropOff {
            get {
                return ResourceManager.GetString("FinalDropOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FIRST PICKUP.
        /// </summary>
        public static string FirstPickup {
            get {
                return ResourceManager.GetString("FirstPickup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forgot password?.
        /// </summary>
        public static string ForgotPasswordText {
            get {
                return ResourceManager.GetString("ForgotPasswordText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fuel type.
        /// </summary>
        public static string FuelType {
            get {
                return ResourceManager.GetString("FuelType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GO.
        /// </summary>
        public static string GoText {
            get {
                return ResourceManager.GetString("GoText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Has Lifting Equipment.
        /// </summary>
        public static string HasLiftingEquipmentText {
            get {
                return ResourceManager.GetString("HasLiftingEquipmentText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Has Loading Dock.
        /// </summary>
        public static string HasLoadingDockText {
            get {
                return ResourceManager.GetString("HasLoadingDockText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Help.
        /// </summary>
        public static string Help {
            get {
                return ResourceManager.GetString("Help", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Help FAQ.
        /// </summary>
        public static string HelpFAQ {
            get {
                return ResourceManager.GetString("HelpFAQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hide passwords.
        /// </summary>
        public static string HidePasswordsButtonText {
            get {
                return ResourceManager.GetString("HidePasswordsButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hide password.
        /// </summary>
        public static string HidePasswordText {
            get {
                return ResourceManager.GetString("HidePasswordText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hours of Operation.
        /// </summary>
        public static string HoursOfOperationText {
            get {
                return ResourceManager.GetString("HoursOfOperationText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ID.
        /// </summary>
        public static string IDText {
            get {
                return ResourceManager.GetString("IDText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Information.
        /// </summary>
        public static string InforamationTitleText {
            get {
                return ResourceManager.GetString("InforamationTitleText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instructions.
        /// </summary>
        public static string InstructionsText {
            get {
                return ResourceManager.GetString("InstructionsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Could not contact the user login endpoint.
        ///Make sure you are connected to internet..
        /// </summary>
        public static string InternetNotAvailableMessageText {
            get {
                return ResourceManager.GetString("InternetNotAvailableMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid data was returned from the backend.
        ///Reason: {0}
        ///
        ///Application cannot continue..
        /// </summary>
        public static string InvalidDataFromBackendMessageText {
            get {
                return ResourceManager.GetString("InvalidDataFromBackendMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Consistency error.
        /// </summary>
        public static string InvalidDataFromBackendTitleText {
            get {
                return ResourceManager.GetString("InvalidDataFromBackendTitleText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Description.
        /// </summary>
        public static string ItemDescriptionText {
            get {
                return ResourceManager.GetString("ItemDescriptionText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change the application language to {0}?.
        /// </summary>
        public static string LanguageChangeConfirmation {
            get {
                return ResourceManager.GetString("LanguageChangeConfirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You reported a &apos;{0}&apos; delay on this load..
        /// </summary>
        public static string LastReportedDelayText {
            get {
                return ResourceManager.GetString("LastReportedDelayText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LEGAL.
        /// </summary>
        public static string Legal {
            get {
                return ResourceManager.GetString("Legal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LICENSE.
        /// </summary>
        public static string License {
            get {
                return ResourceManager.GetString("License", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expiration Date.
        /// </summary>
        public static string LicenseExpirationDate {
            get {
                return ResourceManager.GetString("LicenseExpirationDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to License Number.
        /// </summary>
        public static string LicenseNumber {
            get {
                return ResourceManager.GetString("LicenseNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Load Details.
        /// </summary>
        public static string LoadDetails {
            get {
                return ResourceManager.GetString("LoadDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dropoff Details.
        /// </summary>
        public static string LoadDropoffDetailsText {
            get {
                return ResourceManager.GetString("LoadDropoffDetailsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LOAD ID.
        /// </summary>
        public static string LoadId {
            get {
                return ResourceManager.GetString("LoadId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Load Info.
        /// </summary>
        public static string LoadInfo {
            get {
                return ResourceManager.GetString("LoadInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NCX.
        /// </summary>
        public static string LoadListPageHeader {
            get {
                return ResourceManager.GetString("LoadListPageHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pickup Details.
        /// </summary>
        public static string LoadPickupDetailsText {
            get {
                return ResourceManager.GetString("LoadPickupDetailsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LOADS.
        /// </summary>
        public static string Loads {
            get {
                return ResourceManager.GetString("Loads", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location of refuel.
        /// </summary>
        public static string LocationOfRefuel {
            get {
                return ResourceManager.GetString("LocationOfRefuel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Without accessing your location, the dispatcher is not able to know your location.
        ///
        ///Please enable the permission in
        ///Settings &gt; Permissions 
        /// and check &apos;Location&apos; permission..
        /// </summary>
        public static string LocationPermissionDeniedEnableSettingsMessageText {
            get {
                return ResourceManager.GetString("LocationPermissionDeniedEnableSettingsMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Without accessing your location, the dispatcher is not able to know your location.
        ///The application requires you to allow the location permission..
        /// </summary>
        public static string LocationPermissionDeniedMessageText {
            get {
                return ResourceManager.GetString("LocationPermissionDeniedMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NCX Application is Running.
        /// </summary>
        public static string LocationTrackingNotificationText {
            get {
                return ResourceManager.GetString("LocationTrackingNotificationText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SIGN IN.
        /// </summary>
        public static string LoginText {
            get {
                return ResourceManager.GetString("LoginText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hello, {0}.
        /// </summary>
        public static string MainMenuHeaderFormat {
            get {
                return ResourceManager.GetString("MainMenuHeaderFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The URL is malformed.
        ///Make sure the URL is in the form &apos;https://server&apos;..
        /// </summary>
        public static string MalformedUrlMessageText {
            get {
                return ResourceManager.GetString("MalformedUrlMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Url malformed.
        /// </summary>
        public static string MalformedUrlTitleText {
            get {
                return ResourceManager.GetString("MalformedUrlTitleText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MEDICAL CERTIFICATE.
        /// </summary>
        public static string MedicalCertificate {
            get {
                return ResourceManager.GetString("MedicalCertificate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expiration Date.
        /// </summary>
        public static string MedicalCertificateExpirationDate {
            get {
                return ResourceManager.GetString("MedicalCertificateExpirationDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate Number.
        /// </summary>
        public static string MedicalCertificateNationalRegistrationNumber {
            get {
                return ResourceManager.GetString("MedicalCertificateNationalRegistrationNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Misc Fees.
        /// </summary>
        public static string MiscFeesText {
            get {
                return ResourceManager.GetString("MiscFeesText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mobile Phone.
        /// </summary>
        public static string MobilePhone {
            get {
                return ResourceManager.GetString("MobilePhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You must first select the Power Unit you are using before you can go on duty..
        /// </summary>
        public static string MustSelectPowerUnitToGoActiveMessage {
            get {
                return ResourceManager.GetString("MustSelectPowerUnitToGoActiveMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Load.
        /// </summary>
        public static string NewLoadPageTitle {
            get {
                return ResourceManager.GetString("NewLoadPageTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have a new load.
        /// </summary>
        public static string NewLoadTitleText {
            get {
                return ResourceManager.GetString("NewLoadTitleText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next.
        /// </summary>
        public static string Next {
            get {
                return ResourceManager.GetString("Next", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To use this mobile app, your dispatcher needs to invite you as a driver to their organization from the NCX website.
        /// </summary>
        public static string NoAccountInfo {
            get {
                return ResourceManager.GetString("NoAccountInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No account? Tap here.
        /// </summary>
        public static string NoAccountLink {
            get {
                return ResourceManager.GetString("NoAccountLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have no loads assigned.
        /// </summary>
        public static string NoLoadsAssigned {
            get {
                return ResourceManager.GetString("NoLoadsAssigned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to start an application to show you directions to the location.
        ///Make sure you have an application installed which can give map directions..
        /// </summary>
        public static string NoMapAppForDirectionsMessage {
            get {
                return ResourceManager.GetString("NoMapAppForDirectionsMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can only update the status of an active load..
        /// </summary>
        public static string NotActiveLoadMessageText {
            get {
                return ResourceManager.GetString("NotActiveLoadMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Another load is active.
        /// </summary>
        public static string NotActiveLoadTitleText {
            get {
                return ResourceManager.GetString("NotActiveLoadTitleText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        public static string NotesText {
            get {
                return ResourceManager.GetString("NotesText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        public static string NoText {
            get {
                return ResourceManager.GetString("NoText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notifications.
        /// </summary>
        public static string Notifications {
            get {
                return ResourceManager.GetString("Notifications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Loads Hauled.
        /// </summary>
        public static string NumberLoadsHauled {
            get {
                return ResourceManager.GetString("NumberLoadsHauled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of pallets.
        /// </summary>
        public static string NumberOfPalletsText {
            get {
                return ResourceManager.GetString("NumberOfPalletsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Odometer reading (miles).
        /// </summary>
        public static string OdometerReading {
            get {
                return ResourceManager.GetString("OdometerReading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OK.
        /// </summary>
        public static string OKButtonText {
            get {
                return ResourceManager.GetString("OKButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pallet Exchange Fee.
        /// </summary>
        public static string PalletExchangeFeeText {
            get {
                return ResourceManager.GetString("PalletExchangeFeeText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You password was changed successfully..
        /// </summary>
        public static string PasswordChangedSuccessfullyMessage {
            get {
                return ResourceManager.GetString("PasswordChangedSuccessfullyMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password.
        /// </summary>
        public static string PasswordText {
            get {
                return ResourceManager.GetString("PasswordText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pending Load.
        /// </summary>
        public static string PendingLoadHeader {
            get {
                return ResourceManager.GetString("PendingLoadHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pending Loads.
        /// </summary>
        public static string PendingLoadsHeader {
            get {
                return ResourceManager.GetString("PendingLoadsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pending.
        /// </summary>
        public static string PendingText {
            get {
                return ResourceManager.GetString("PendingText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Permission denied.
        /// </summary>
        public static string PermissionDeniedText {
            get {
                return ResourceManager.GetString("PermissionDeniedText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PHOTO LIBRARY.
        /// </summary>
        public static string PhotoLibraryText {
            get {
                return ResourceManager.GetString("PhotoLibraryText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Photo {0} of {1}.
        /// </summary>
        public static string PhotoViewerStatusText {
            get {
                return ResourceManager.GetString("PhotoViewerStatusText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} PickUps.
        /// </summary>
        public static string PickUpCountPlural {
            get {
                return ResourceManager.GetString("PickUpCountPlural", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} PickUp.
        /// </summary>
        public static string PickUpCountSingular {
            get {
                return ResourceManager.GetString("PickUpCountSingular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pickup Location.
        /// </summary>
        public static string PickupLocationText {
            get {
                return ResourceManager.GetString("PickupLocationText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pickup Number.
        /// </summary>
        public static string PickupNumberText {
            get {
                return ResourceManager.GetString("PickupNumberText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Without accessing your camera, you cannot take a photo for proof of delivery.
        ///
        ///Please enable the permission in
        ///Settings &gt; Permissions 
        /// and check &apos;Camera&apos; permission..
        /// </summary>
        public static string PODCameraPermissionDeniedEnableSettingsMessageText {
            get {
                return ResourceManager.GetString("PODCameraPermissionDeniedEnableSettingsMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Without accessing your camera, you cannot take a photo for proof of delivery..
        /// </summary>
        public static string PODCameraPermissionDeniedMessageText {
            get {
                return ResourceManager.GetString("PODCameraPermissionDeniedMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Without accessing your storage, you cannot send a photo for proof of delivery.
        ///
        ///Please enable the permission in
        ///Settings &gt; Permissions 
        /// and check &apos;Storage&apos; permission..
        /// </summary>
        public static string PODStoragePermissionDeniedEnableSettingsMessageText {
            get {
                return ResourceManager.GetString("PODStoragePermissionDeniedEnableSettingsMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Without accessing your storage, you cannot send a photo for proof of delivery..
        /// </summary>
        public static string PODStoragePermissionDeniedMessageText {
            get {
                return ResourceManager.GetString("PODStoragePermissionDeniedMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price per gallon ($).
        /// </summary>
        public static string PricePerGallon {
            get {
                return ResourceManager.GetString("PricePerGallon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Privacy Policy.
        /// </summary>
        public static string PrivacyPolicyLabel {
            get {
                return ResourceManager.GetString("PrivacyPolicyLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Proof of Delivery Image.
        /// </summary>
        public static string ProofOfDeliveryText {
            get {
                return ResourceManager.GetString("ProofOfDeliveryText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order Number.
        /// </summary>
        public static string PurchaseOrderNumberText {
            get {
                return ResourceManager.GetString("PurchaseOrderNumberText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity of fuel (gallons).
        /// </summary>
        public static string QuantityOfFuel {
            get {
                return ResourceManager.GetString("QuantityOfFuel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Record a Fuel Stop.
        /// </summary>
        public static string RecordFuelStop {
            get {
                return ResourceManager.GetString("RecordFuelStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh.
        /// </summary>
        public static string RefreshBtnText {
            get {
                return ResourceManager.GetString("RefreshBtnText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to remove this photo from the list?.
        /// </summary>
        public static string RemovePhotoConfirmationText {
            get {
                return ResourceManager.GetString("RemovePhotoConfirmationText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to REPORT A DELAY.
        /// </summary>
        public static string ReportDelayButton {
            get {
                return ResourceManager.GetString("ReportDelayButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report &apos;{0}&apos; for this load?.
        /// </summary>
        public static string ReportDelayConfirmationText {
            get {
                return ResourceManager.GetString("ReportDelayConfirmationText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report a delay.
        /// </summary>
        public static string ReportDelayHeader {
            get {
                return ResourceManager.GetString("ReportDelayHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required Temperature.
        /// </summary>
        public static string RequiredTemperatureText {
            get {
                return ResourceManager.GetString("RequiredTemperatureText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make sure the email or phone number is correct..
        /// </summary>
        public static string ResetPasswordErrorMessageText {
            get {
                return ResourceManager.GetString("ResetPasswordErrorMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error resetting your password.
        /// </summary>
        public static string ResetPasswordErrorMessageTitle {
            get {
                return ResourceManager.GetString("ResetPasswordErrorMessageTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your Password reset request has been submitted. Please check your email or phone SMS for for instructions to reset your password..
        /// </summary>
        public static string ResetPasswordSuccessfulMessageText {
            get {
                return ResourceManager.GetString("ResetPasswordSuccessfulMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password reset successful.
        /// </summary>
        public static string ResetPasswordSuccessfulTitleText {
            get {
                return ResourceManager.GetString("ResetPasswordSuccessfulTitleText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset My Password.
        /// </summary>
        public static string ResetPasswordText {
            get {
                return ResourceManager.GetString("ResetPasswordText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Retry.
        /// </summary>
        public static string RetryText {
            get {
                return ResourceManager.GetString("RetryText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Schedule.
        /// </summary>
        public static string Schedule {
            get {
                return ResourceManager.GetString("Schedule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Secret Code.
        /// </summary>
        public static string SecretCodeText {
            get {
                return ResourceManager.GetString("SecretCodeText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Language.
        /// </summary>
        public static string SelectLanguageText {
            get {
                return ResourceManager.GetString("SelectLanguageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select an en route destination.
        /// </summary>
        public static string SelectLocationEnRouteHeader {
            get {
                return ResourceManager.GetString("SelectLocationEnRouteHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select location to update.
        /// </summary>
        public static string SelectLocationHeader {
            get {
                return ResourceManager.GetString("SelectLocationHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Drop-off.
        /// </summary>
        public static string SelectLocationToUpdateDropoffHeader {
            get {
                return ResourceManager.GetString("SelectLocationToUpdateDropoffHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pickup.
        /// </summary>
        public static string SelectLocationToUpdatePickupHeader {
            get {
                return ResourceManager.GetString("SelectLocationToUpdatePickupHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send proof of delivery?.
        /// </summary>
        public static string SendPODConfirmationMessage {
            get {
                return ResourceManager.GetString("SendPODConfirmationMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you need to take another photo select the &quot;Take Another Photo&quot; button, and when you&apos;re done tap the &quot;Send&quot; button to complete the load..
        /// </summary>
        public static string SendPODInfoText {
            get {
                return ResourceManager.GetString("SendPODInfoText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Proof of Delivery.
        /// </summary>
        public static string SendPODText {
            get {
                return ResourceManager.GetString("SendPODText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send.
        /// </summary>
        public static string SendText {
            get {
                return ResourceManager.GetString("SendText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Could not contact server.
        /// </summary>
        public static string ServerErrorTitleText {
            get {
                return ResourceManager.GetString("ServerErrorTitleText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings.
        /// </summary>
        public static string Settings {
            get {
                return ResourceManager.GetString("Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show application settings.
        /// </summary>
        public static string ShowAppSettingsText {
            get {
                return ResourceManager.GetString("ShowAppSettingsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SHOW DETAILS.
        /// </summary>
        public static string ShowDetails {
            get {
                return ResourceManager.GetString("ShowDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show passwords.
        /// </summary>
        public static string ShowPasswordsButtonText {
            get {
                return ResourceManager.GetString("ShowPasswordsButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show password.
        /// </summary>
        public static string ShowPasswordText {
            get {
                return ResourceManager.GetString("ShowPasswordText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pressing sign in confirms your acceptance of the terms and conditions for using the NCX platform..
        /// </summary>
        public static string SignInAcceptTermsLabel {
            get {
                return ResourceManager.GetString("SignInAcceptTermsLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sign out.
        /// </summary>
        public static string SignOut {
            get {
                return ResourceManager.GetString("SignOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to START TRAVEL.
        /// </summary>
        public static string StartTravel {
            get {
                return ResourceManager.GetString("StartTravel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        public static string StatusText {
            get {
                return ResourceManager.GetString("StatusText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stops.
        /// </summary>
        public static string StopsText {
            get {
                return ResourceManager.GetString("StopsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to STOP TRAVEL.
        /// </summary>
        public static string StopTravel {
            get {
                return ResourceManager.GetString("StopTravel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SUBMIT.
        /// </summary>
        public static string Submit {
            get {
                return ResourceManager.GetString("Submit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Success.
        /// </summary>
        public static string SuccessTitleText {
            get {
                return ResourceManager.GetString("SuccessTitleText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TAKE ANOTHER PHOTO.
        /// </summary>
        public static string TakeAnotherPhotoText {
            get {
                return ResourceManager.GetString("TakeAnotherPhotoText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TAKE PHOTO OF RECEIPT.
        /// </summary>
        public static string TakePhotoOfReceiptText {
            get {
                return ResourceManager.GetString("TakePhotoOfReceiptText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Take a photo of the receipt or select an existing photo from your library..
        /// </summary>
        public static string TakePhotoOrSelectExistingText {
            get {
                return ResourceManager.GetString("TakePhotoOrSelectExistingText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TAKE PHOTO.
        /// </summary>
        public static string TakePhotoText {
            get {
                return ResourceManager.GetString("TakePhotoText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (tap for directions).
        /// </summary>
        public static string TapForDirections {
            get {
                return ResourceManager.GetString("TapForDirections", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (tap to call).
        /// </summary>
        public static string TapToCall {
            get {
                return ResourceManager.GetString("TapToCall", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terms of Service.
        /// </summary>
        public static string TermsOfServiceLabel {
            get {
                return ResourceManager.GetString("TermsOfServiceLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total.
        /// </summary>
        public static string Total {
            get {
                return ResourceManager.GetString("Total", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trailer Length.
        /// </summary>
        public static string TrailerLengthText {
            get {
                return ResourceManager.GetString("TrailerLengthText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tutorial.
        /// </summary>
        public static string TutorialBtnText {
            get {
                return ResourceManager.GetString("TutorialBtnText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tutorial.
        /// </summary>
        public static string TutorialPageTitle {
            get {
                return ResourceManager.GetString("TutorialPageTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unit Count.
        /// </summary>
        public static string UnitCountText {
            get {
                return ResourceManager.GetString("UnitCountText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unit Type.
        /// </summary>
        public static string UnitTypeText {
            get {
                return ResourceManager.GetString("UnitTypeText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unit Weight.
        /// </summary>
        public static string UnitWeightText {
            get {
                return ResourceManager.GetString("UnitWeightText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unloading Fees.
        /// </summary>
        public static string UnloadingFeesText {
            get {
                return ResourceManager.GetString("UnloadingFeesText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change status to {0}?.
        /// </summary>
        public static string UpdateLoadOperationStatusMessageFormatText {
            get {
                return ResourceManager.GetString("UpdateLoadOperationStatusMessageFormatText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make sure the existing password is correct..
        /// </summary>
        public static string UpdatePasswordErrorMessage {
            get {
                return ResourceManager.GetString("UpdatePasswordErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error changing your password.
        /// </summary>
        public static string UpdatePasswordErrorTitle {
            get {
                return ResourceManager.GetString("UpdatePasswordErrorTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The new password must match and the new password confirmation.
        /// </summary>
        public static string UpdatePasswordNewPasswordDiffersMessage {
            get {
                return ResourceManager.GetString("UpdatePasswordNewPasswordDiffersMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change your password.
        /// </summary>
        public static string UpdatePasswordPageTitle {
            get {
                return ResourceManager.GetString("UpdatePasswordPageTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You password must have a minimum of 8 characters.
        /// </summary>
        public static string UpdatePasswordTooSmallErrorMessage {
            get {
                return ResourceManager.GetString("UpdatePasswordTooSmallErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UPDATE STATUS.
        /// </summary>
        public static string UpdateStatusText {
            get {
                return ResourceManager.GetString("UpdateStatusText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UPDATE THIS LOCATION.
        /// </summary>
        public static string UpdateThisLocationButton {
            get {
                return ResourceManager.GetString("UpdateThisLocationButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Check your email/phone number and password.
        /// </summary>
        public static string UserLoginErrorMessageText {
            get {
                return ResourceManager.GetString("UserLoginErrorMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login failed.
        /// </summary>
        public static string UserLoginErrorTitleText {
            get {
                return ResourceManager.GetString("UserLoginErrorTitleText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning, you are logging out of the application. 
        ///    All of your loads data will be lost. 
        ///    You will not be able to retrieve this information unless you have internet connection when you log back in?.
        /// </summary>
        public static string UserLogoutMessageText {
            get {
                return ResourceManager.GetString("UserLogoutMessageText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email or Phone number.
        /// </summary>
        public static string UsernameText {
            get {
                return ResourceManager.GetString("UsernameText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change your password.
        /// </summary>
        public static string UserProfileUpdatePasswordButton {
            get {
                return ResourceManager.GetString("UserProfileUpdatePasswordButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vehicle.
        /// </summary>
        public static string Vehicle {
            get {
                return ResourceManager.GetString("Vehicle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        public static string YesText {
            get {
                return ResourceManager.GetString("YesText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your current password.
        /// </summary>
        public static string YourCurrentPassword {
            get {
                return ResourceManager.GetString("YourCurrentPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your new password.
        /// </summary>
        public static string YourNewPassword {
            get {
                return ResourceManager.GetString("YourNewPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your profile.
        /// </summary>
        public static string YourProfile {
            get {
                return ResourceManager.GetString("YourProfile", resourceCulture);
            }
        }
    }
}
