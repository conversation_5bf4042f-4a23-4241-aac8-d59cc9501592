﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AutomaticLogoutMessageText" xml:space="preserve">
    <value>Has sido desconectado.
Asegúrese de que no haya iniciado la sesión desde otro dispositivo.</value>
  </data>
  <data name="AutomaticLogoutTitleText" xml:space="preserve">
    <value>Necesario inciar sesion</value>
  </data>
  <data name="CapturePODInfoText" xml:space="preserve">
    <value>Tome una foto de la prueba de entrega (POD) o seleccione una foto existente de la galería de fotos. Asegúrese de capturar claramente la firma de la prueba de entrega en la foto. 

si la prueba de entrega contiene varias páginas, tome una foto a la vez.</value>
  </data>
  <data name="CloseText" xml:space="preserve">
    <value>Cerra</value>
  </data>
  <data name="ConfirmationText" xml:space="preserve">
    <value>Confirmación</value>
  </data>
  <data name="CopyrightText" xml:space="preserve">
    <value>Derechos de Autor © National Carrier Exchange
Todos los derechos reservados</value>
  </data>
  <data name="DetailsText" xml:space="preserve">
    <value>Detalles</value>
  </data>
  <data name="ExpectedDateText" xml:space="preserve">
    <value>Esperado</value>
  </data>
  <data name="DelayReasonAccidentText" xml:space="preserve">
    <value>Accidente menor</value>
  </data>
  <data name="DelayReasonBadWeatherText" xml:space="preserve">
    <value>Mal clima</value>
  </data>
  <data name="DelayReasonBreakdownText" xml:space="preserve">
    <value>Desglose menor</value>
  </data>
  <data name="DelayReasonFlatTireText" xml:space="preserve">
    <value>Neumático plano</value>
  </data>
  <data name="DelayReasonHeavyTrafficText" xml:space="preserve">
    <value>Tráfico pesado</value>
  </data>
  <data name="DelayReasonRoadClosureText" xml:space="preserve">
    <value>Cierre de carreteras</value>
  </data>
  <data name="ForgotPasswordText" xml:space="preserve">
    <value>¿Olvidó su contraseña?</value>
  </data>
  <data name="HidePasswordText" xml:space="preserve">
    <value>Ocultar</value>
  </data>
  <data name="LastReportedDelayText" xml:space="preserve">
    <value>Ha reportado un ' {0} ' retraso en esta carga.</value>
  </data>
  <data name="LoginText" xml:space="preserve">
    <value>SESIÓN</value>
  </data>
  <data name="PasswordText" xml:space="preserve">
    <value>Contraseña</value>
  </data>
  <data name="PhotoLibraryText" xml:space="preserve">
    <value>Galería de Fotos</value>
  </data>
  <data name="PhotoViewerStatusText" xml:space="preserve">
    <value>Foto {0} de {1}</value>
  </data>
  <data name="RemovePhotoConfirmationText" xml:space="preserve">
    <value>¿está seguro que quieres quitar esta foto de la lista?</value>
  </data>
  <data name="SendPODInfoText" xml:space="preserve">
    <value>Si necesita tomar otra foto Seleccione el botón "tomar otra foto", y cuando haya terminado seleccione el botón "enviar" para completar la carga.</value>
  </data>
  <data name="SendPODText" xml:space="preserve">
    <value>Prueba de Entrega</value>
  </data>
  <data name="SendText" xml:space="preserve">
    <value>Enviar</value>
  </data>
  <data name="ReportDelayConfirmationText" xml:space="preserve">
    <value>¿Informe ' {0} ' para esta carga?</value>
  </data>
  <data name="ReportDelayHeader" xml:space="preserve">
    <value>Retraso de informe</value>
  </data>
  <data name="ShowPasswordText" xml:space="preserve">
    <value>Mostrar</value>
  </data>
  <data name="TakeAnotherPhotoText" xml:space="preserve">
    <value>Toma otra foto</value>
  </data>
  <data name="TakePhotoText" xml:space="preserve">
    <value>Toma una foto</value>
  </data>
  <data name="UpdateLoadOperationStatusMessageFormatText" xml:space="preserve">
    <value>¿cambia el estado a {0}?</value>
  </data>
  <data name="UpdateStatusText" xml:space="preserve">
    <value>Actualizar</value>
  </data>
  <data name="UserLoginErrorMessageText" xml:space="preserve">
    <value>Comprueba tu nombre de usuario y contraseña</value>
  </data>
  <data name="UserLoginErrorTitleText" xml:space="preserve">
    <value>Error de inicio de sesión</value>
  </data>
  <data name="UserLogoutMessageText" xml:space="preserve">
    <value>¿está seguro que quieres continuar con la sesión de cierre?</value>
  </data>
  <data name="UsernameText" xml:space="preserve">
    <value>Nombre de Usuario</value>
  </data>
  <data name="LocationPermissionDeniedMessageText" xml:space="preserve">
    <value>Sin acceso a su ubicación, el despachador no va saber tu localización. 
La aplicación requiere permiso de ubicación.</value>
  </data>
  <data name="PermissionDeniedText" xml:space="preserve">
    <value>Permiso denegado</value>
  </data>
  <data name="LocationPermissionDeniedEnableSettingsMessageText" xml:space="preserve">
    <value>Sin acceso a su ubicación, el despachador no va saber tu localización.
 
Por favor activa el permiso en
Ajustes &gt; permisos 
 y comprueba el permiso de ' ubicación '.</value>
  </data>
  <data name="ShowAppSettingsText" xml:space="preserve">
    <value>Mostrar configuración de la aplicación</value>
  </data>
  <data name="PODCameraPermissionDeniedMessageText" xml:space="preserve">
    <value>Sin tener acceso a su cámara, usted no puede tomar una foto para la prueba de entrega.</value>
  </data>
  <data name="PODCameraPermissionDeniedEnableSettingsMessageText" xml:space="preserve">
    <value>Sin tener acceso a su cámara, usted no puede tomar una foto para la prueba de entrega. 

Por favor activa el permiso en
Ajustes &gt; permisos 
 y comprueba el permiso de ' cámara '.</value>
  </data>
  <data name="RetryText" xml:space="preserve">
    <value>Reintentar</value>
  </data>
  <data name="PODStoragePermissionDeniedMessageText" xml:space="preserve">
    <value>Sin tener acceso a su almacenamiento, no puede enviar una foto para la prueba de entrega.</value>
  </data>
  <data name="PODStoragePermissionDeniedEnableSettingsMessageText" xml:space="preserve">
    <value>Sin tener acceso a su almacenamiento, no puede enviar una foto para prueba de entrega. 

Por favor activa el permiso en
Ajustes &gt; permisos 
 y comprueba el permiso de ' almacenamiento '.</value>
  </data>
  <data name="ResetPasswordText" xml:space="preserve">
    <value>Restablecer mi contraseña</value>
  </data>
  <data name="ResetPasswordSuccessfulTitleText" xml:space="preserve">
    <value>Restablecimiento de la contraseña</value>
  </data>
  <data name="ResetPasswordSuccessfulMessageText" xml:space="preserve">
    <value>Su solicitud de restablecimiento de contraseña ha sido enviada. Por favor revise su correo electrónico o teléfono SMS para obtener instrucciones para restablecer su contraseña.</value>
  </data>
  <data name="ServerErrorTitleText" xml:space="preserve">
    <value>No se pudo contactar con el servidor</value>
  </data>
  <data name="EmailText" xml:space="preserve">
    <value>Dirección de correo electrónico</value>
  </data>
  <data name="SelectLanguageText" xml:space="preserve">
    <value>Selecciona idioma</value>
  </data>
  <data name="LanguageChangeConfirmation" xml:space="preserve">
    <value>¿cambia la idioma de la aplicación a {0}?</value>
  </data>
  <data name="LoadListPageHeader" xml:space="preserve">
    <value>NCX</value>
  </data>
  <data name="NoLoadsAssigned" xml:space="preserve">
    <value>No tiene ninguna carga asignada</value>
  </data>
  <data name="SignOut" xml:space="preserve">
    <value>Desconectar</value>
  </data>
  <data name="FirstPickup" xml:space="preserve">
    <value>Primera recogida</value>
  </data>
  <data name="FinalDropOff" xml:space="preserve">
    <value> Entrega final</value>
  </data>
  <data name="LoadId" xml:space="preserve">
    <value>Identificación de carga</value>
  </data>
  <data name="PickUpCountSingular" xml:space="preserve">
    <value>{0} PickUp</value>
  </data>
  <data name="PickUpCountPlural" xml:space="preserve">
    <value>{0} Recogidas</value>
  </data>
  <data name="DropoffCountSingular" xml:space="preserve">
    <value>{0} Dropoff</value>
  </data>
  <data name="DropoffCountPlural" xml:space="preserve">
    <value>{0} Entregas</value>
  </data>
  <data name="LoadInfo" xml:space="preserve">
    <value>Información de la carga</value>
  </data>
  <data name="Schedule" xml:space="preserve">
    <value>Horario</value>
  </data>
  <data name="LoadDetails" xml:space="preserve">
    <value>Detalles de la carga</value>
  </data>
  <data name="IDText" xml:space="preserve">
    <value>Identificación</value>
  </data>
  <data name="StatusText" xml:space="preserve">
    <value>Estado</value>
  </data>
  <data name="EquipmentTypeText" xml:space="preserve">
    <value>Tipo de equipo</value>
  </data>
  <data name="TrailerLengthText" xml:space="preserve">
    <value>Longitud del acoplado</value>
  </data>
  <data name="RequiredTemperatureText" xml:space="preserve">
    <value>Temperatura requerida</value>
  </data>
  <data name="StopsText" xml:space="preserve">
    <value>Paradas</value>
  </data>
  <data name="NotesText" xml:space="preserve">
    <value>Notas</value>
  </data>
  <data name="PickupNumberText" xml:space="preserve">
    <value>Número de recogida</value>
  </data>
  <data name="DeliverNumberText" xml:space="preserve">
    <value>Número de entrega</value>
  </data>
  <data name="PurchaseOrderNumberText" xml:space="preserve">
    <value>Número de pedido de compra</value>
  </data>
  <data name="ContactNameText" xml:space="preserve">
    <value>Nombre de contacto</value>
  </data>
  <data name="ContactNumberText" xml:space="preserve">
    <value>Número de contacto</value>
  </data>
  <data name="ApptContactNameText" xml:space="preserve">
    <value>Nombre de contacto de cita</value>
  </data>
  <data name="ApptContactNumberText" xml:space="preserve">
    <value>Número de contacto del citado</value>
  </data>
  <data name="ApptRequiredText" xml:space="preserve">
    <value>¿cita requerida?</value>
  </data>
  <data name="MiscFeesText" xml:space="preserve">
    <value>Cuotas misceláneas</value>
  </data>
  <data name="EntranceFeesText" xml:space="preserve">
    <value>Tarifas de entrada</value>
  </data>
  <data name="UnloadingFeesText" xml:space="preserve">
    <value>Gastos de descarga</value>
  </data>
  <data name="PalletExchangeFeeText" xml:space="preserve">
    <value>Tasa de cambio de la tarima</value>
  </data>
  <data name="InstructionsText" xml:space="preserve">
    <value>Instrucciones</value>
  </data>
  <data name="HasLiftingEquipmentText" xml:space="preserve">
    <value>Tiene equipo de elevación</value>
  </data>
  <data name="HasLoadingDockText" xml:space="preserve">
    <value>Tiene muelle de carga</value>
  </data>
  <data name="HoursOfOperationText" xml:space="preserve">
    <value>Horas de operación</value>
  </data>
  <data name="NotActiveLoadMessageText" xml:space="preserve">
    <value>Sólo se puede actualizar el estado de una carga activa.</value>
  </data>
  <data name="NotActiveLoadTitleText" xml:space="preserve">
    <value>Otra carga está activa</value>
  </data>
  <data name="CanUpdateActivePickupOrDropOffMessageText" xml:space="preserve">
    <value>Sólo puede actualizar el estado de una activación o dejar activa.</value>
  </data>
  <data name="CanUpdateActivePickupOrDropOffTitleText" xml:space="preserve">
    <value>Otra recogida o entrega está activa</value>
  </data>
  <data name="CompletePickupFirstMessageText" xml:space="preserve">
    <value>Debe completar todas las recogidas antes de las entregas.</value>
  </data>
  <data name="InforamationTitleText" xml:space="preserve">
    <value>Información</value>
  </data>
  <data name="PickupLocationText" xml:space="preserve">
    <value>Localización de la recogida</value>
  </data>
  <data name="DropOffLocationText" xml:space="preserve">
    <value>Localización de la entrega</value>
  </data>
  <data name="LoadPickupDetailsText" xml:space="preserve">
    <value>Detalles de la recogida</value>
  </data>
  <data name="LoadDropoffDetailsText" xml:space="preserve">
    <value>Detalles de la entrega</value>
  </data>
  <data name="ItemDescriptionText" xml:space="preserve">
    <value>Descripción del artículo</value>
  </data>
  <data name="UnitTypeText" xml:space="preserve">
    <value>Tipo de unidad</value>
  </data>
  <data name="UnitCountText" xml:space="preserve">
    <value>Conteo de unidades</value>
  </data>
  <data name="UnitWeightText" xml:space="preserve">
    <value>Peso unitario</value>
  </data>
  <data name="NumberOfPalletsText" xml:space="preserve">
    <value>Número de tarimas</value>
  </data>
  <data name="CargoDetailsText" xml:space="preserve">
    <value>Detalles de carga</value>
  </data>
  <data name="CargoDestinationDetailsText" xml:space="preserve">
    <value>Detalles de destino de carga</value>
  </data>
  <data name="CargoItemHeaderFormatText" xml:space="preserve">
    <value>{0} de {1}</value>
  </data>
  <data name="ConnectionErrorMessageText" xml:space="preserve">
    <value>NCX tiene problemas conectando al servidor. Inténtalo más tarde.</value>
  </data>
  <data name="InternetNotAvailableMessageText" xml:space="preserve">
    <value>No se pudo contactar con el punto de acceso del usuario. 
Asegurarse de que está conectado a Internet.</value>
  </data>
  <data name="APIHostChangedMessageText" xml:space="preserve">
    <value>El anfitrión de extremo API cambió a</value>
  </data>
  <data name="SuccessTitleText" xml:space="preserve">
    <value>Éxito</value>
  </data>
  <data name="MalformedUrlMessageText" xml:space="preserve">
    <value>La URL está malformada. 
Asegurase que la URL está en la forma ' https://Server '.</value>
  </data>
  <data name="MalformedUrlTitleText" xml:space="preserve">
    <value>URL malformada</value>
  </data>
  <data name="InvalidDataFromBackendMessageText" xml:space="preserve">
    <value>Los datos inválidos se devolvieron desde el backend. 
Causa: {0}

Application no puede continuar.</value>
  </data>
  <data name="InvalidDataFromBackendTitleText" xml:space="preserve">
    <value>Error de consistencia</value>
  </data>
  <data name="SecretCodeText" xml:space="preserve">
    <value>Código secreto</value>
  </data>
  <data name="GoText" xml:space="preserve">
    <value>Ir</value>
  </data>
  <data name="ProofOfDeliveryText" xml:space="preserve">
    <value>Imagen de la prueba de entrega</value>
  </data>
  <data name="ChangeLanguageText" xml:space="preserve">
    <value>Cambiar idioma</value>
  </data>
  <data name="CheckedInText" xml:space="preserve">
    <value>Registrado</value>
  </data>
  <data name="EnRouteText" xml:space="preserve">
    <value>En ruta</value>
  </data>
  <data name="PendingText" xml:space="preserve">
    <value>Pendiente</value>
  </data>
  <data name="BackToLoginText" xml:space="preserve">
    <value>Volver al inicio de sesión</value>
  </data>
  <data name="CompletedText" xml:space="preserve">
    <value>Completado</value>
  </data>
  <data name="CanceledText" xml:space="preserve">
    <value>Cancelado</value>
  </data>
  <data name="DeletedText" xml:space="preserve">
    <value>Eliminados</value>
  </data>
  <data name="NewLoadTitleText" xml:space="preserve">
    <value>Usted tiene una nueva carga</value>
  </data>
  <data name="LocationTrackingNotificationText" xml:space="preserve">
    <value>La aplicación NCX se está ejecutando</value>
  </data>
  <data name="CancelButtonText" xml:space="preserve">
    <value>Cancela</value>
  </data>
  <data name="OKButtonText" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="CloseButtonText" xml:space="preserve">
    <value>Cierra</value>
  </data>
  <data name="YesText" xml:space="preserve">
    <value>Si</value>
  </data>
  <data name="NoText" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="SendPODConfirmationMessage" xml:space="preserve">
    <value>¿enviar la prueba de entrega?</value>
  </data>
  <data name="AllDayHoursOfOperation" xml:space="preserve">
    <value>Todo el día</value>
  </data>
  <data name="TapToCall" xml:space="preserve">
    <value>(toque para llamar)</value>
  </data>
  <data name="TapForDirections" xml:space="preserve">
    <value>(toque para direcciones)</value>
  </data>
  <data name="EquipmentRefrigeratedVan" xml:space="preserve">
    <value>Furgoneta refrigerada</value>
  </data>
  <data name="CargoUnitBag" xml:space="preserve">
    <value>Bolsa</value>
  </data>
  <data name="NoMapAppForDirectionsMessage" xml:space="preserve">
    <value>No se puede iniciar una aplicación para mostrar las direcciones de la ubicación.
 Asegúrese de que tiene una aplicación instalada que puede dar direcciones de mapa.</value>
  </data>
  <data name="DriverDateCreated" xml:space="preserve">
    <value>Fecha de creación</value>
  </data>
  <data name="DriverLicenseState" xml:space="preserve">
    <value>Estado de licencia de chofer</value>
  </data>
  <data name="LicenseExpirationDate" xml:space="preserve">
    <value>Fecha de vencimiento de la licencia</value>
  </data>
  <data name="LicenseNumber" xml:space="preserve">
    <value>Número de licencia</value>
  </data>
  <data name="MedicalCertificateExpirationDate" xml:space="preserve">
    <value>Fecha de vencimiento del certificado médico</value>
  </data>
  <data name="MedicalCertificateNationalRegistrationNumber" xml:space="preserve">
    <value>Certificado médico número de registro nacional</value>
  </data>
  <data name="MobilePhone" xml:space="preserve">
    <value>Teléfono móvil</value>
  </data>
  <data name="NumberLoadsHauled" xml:space="preserve">
    <value>Número de cargas transportadas</value>
  </data>
  <data name="DriverProfileTitle" xml:space="preserve">
    <value>Tu perfil</value>
  </data>
  <data name="YourProfile" xml:space="preserve">
    <value>Tu perfil</value>
  </data>
  <data name="UpdatePasswordNewPasswordDiffersMessage" xml:space="preserve">
    <value>La nueva contraseña debe coincidir y la nueva confirmación de contraseña</value>
  </data>
  <data name="UpdatePasswordErrorTitle" xml:space="preserve">
    <value>Error al cambiar la contraseña</value>
  </data>
  <data name="YourCurrentPassword" xml:space="preserve">
    <value>Su contraseña actual</value>
  </data>
  <data name="YourNewPassword" xml:space="preserve">
    <value>Su nueva contraseña</value>
  </data>
  <data name="ConfirmYourNewPassword" xml:space="preserve">
    <value>Confirme su nueva contraseña</value>
  </data>
  <data name="UpdatePasswordPageTitle" xml:space="preserve">
    <value>Cambiar su contraseña</value>
  </data>
  <data name="ChangePasswordBtnText" xml:space="preserve">
    <value>Cambiar contraseña</value>
  </data>
  <data name="UserProfileUpdatePasswordButton" xml:space="preserve">
    <value>Cambiar su contraseña</value>
  </data>
  <data name="ShowPasswordsButtonText" xml:space="preserve">
    <value>Mostrar contraseñas</value>
  </data>
  <data name="HidePasswordsButtonText" xml:space="preserve">
    <value>Ocultar contraseñas</value>
  </data>
  <data name="UpdatePasswordErrorMessage" xml:space="preserve">
    <value>Asegúrese de que la contraseña existente esta correcta.</value>
  </data>
  <data name="PasswordChangedSuccessfullyMessage" xml:space="preserve">
    <value>La contraseña se modificó correctamente.</value>
  </data>
  <data name="UpdatePasswordTooSmallErrorMessage" xml:space="preserve">
    <value>La contraseña debe tener un mínimo de 8 caracteres</value>
  </data>
  <data name="ResetPasswordErrorMessageText" xml:space="preserve">
    <value>Asegúrese de que la contraseña existente esta correcta.</value>
  </data>
  <data name="ResetPasswordErrorMessageTitle" xml:space="preserve">
    <value>Error al reconfigurar la contraseña</value>
  </data>
  <data name="EquipmentDryVan" xml:space="preserve">
    <value>Camión con contenedor</value>
  </data>
  <data name="EquipmentVentilatedVan" xml:space="preserve">
    <value>Camión con ventilación</value>
  </data>
  <data name="EquipmentAirRideVan" xml:space="preserve">
    <value>Camión con suspención de aire</value>
  </data>
  <data name="EquipmentVanwithCurtains" xml:space="preserve">
    <value>Camión con cortinas</value>
  </data>
  <data name="EquipmentFlatbed" xml:space="preserve">
    <value>Camión con plataforma</value>
  </data>
  <data name="EquipmentFlatbedwithSides" xml:space="preserve">
    <value>Camión con plataforma y lados</value>
  </data>
  <data name="EquipmentFlatbedwithTarps" xml:space="preserve">
    <value>Camión con plataforma y correas</value>
  </data>
  <data name="EquipmentFlatbedStretch" xml:space="preserve">
    <value>Camión con plataforma extendible</value>
  </data>
  <data name="EquipmentFlatbedSideKit" xml:space="preserve">
    <value>Kit lateral plano</value>
  </data>
  <data name="EquipmentAutoCarrier" xml:space="preserve">
    <value>Camión de carros</value>
  </data>
  <data name="EquipmentBeverageTrailer" xml:space="preserve">
    <value>Contenedor de bebidas</value>
  </data>
  <data name="EquipmentIntermodal20Container" xml:space="preserve">
    <value>Contenedor multimodal 20'</value>
  </data>
  <data name="EquipmentIntermodal40Container" xml:space="preserve">
    <value>Contenedor multimodal 40'</value>
  </data>
  <data name="EquipmentLivestockTrailerCattle" xml:space="preserve">
    <value>Remolque de animales (Ganado)</value>
  </data>
  <data name="EquipmentLivestockTrailerHogs" xml:space="preserve">
    <value>Remolque de animales (Cerdos)</value>
  </data>
  <data name="EquipmentLivestockTrailerChickens" xml:space="preserve">
    <value>Remolque de animales (Gallinas)</value>
  </data>
  <data name="EquipmentLoggerTrailer" xml:space="preserve">
    <value>Remolque de troncos de arboles</value>
  </data>
  <data name="EquipmentBoatTrailerSingle" xml:space="preserve">
    <value>Remolque de Barcos (Individual)</value>
  </data>
  <data name="EquipmentBoatTrailerDouble" xml:space="preserve">
    <value>Remolque de Barcos (Doble)</value>
  </data>
  <data name="EquipmentRemovableGooseneckRGN" xml:space="preserve">
    <value>Removable Gooseneck (RGN)</value>
  </data>
  <data name="EquipmentConestogaTrailer" xml:space="preserve">
    <value>Conestoga Trailer</value>
  </data>
  <data name="EquipmentDoubleDrop" xml:space="preserve">
    <value>Double Drop</value>
  </data>
  <data name="EquipmentDoubleDropStretch" xml:space="preserve">
    <value>Double Drop Stretch</value>
  </data>
  <data name="EquipmentDoubleDropExtendable" xml:space="preserve">
    <value>Double Drop Extendable</value>
  </data>
  <data name="EquipmentDoubleDropSideKit" xml:space="preserve">
    <value>Double Drop Side Kit</value>
  </data>
  <data name="EquipmentBTrainCombo" xml:space="preserve">
    <value>B-Train Combo</value>
  </data>
  <data name="EquipmentSingleDrop" xml:space="preserve">
    <value>Single Drop</value>
  </data>
  <data name="EquipmentSingleDropStretch" xml:space="preserve">
    <value>Single Drop Stretch</value>
  </data>
  <data name="EquipmentSingleDropExtendable" xml:space="preserve">
    <value>Single Drop Extendable</value>
  </data>
  <data name="EquipmentSingleDropSideKit" xml:space="preserve">
    <value>Single Drop Side Kit</value>
  </data>
  <data name="EquipmentDumpTrailer" xml:space="preserve">
    <value>Remolque volcado</value>
  </data>
  <data name="EquipmentEndDumpTrailer" xml:space="preserve">
    <value>End Dump Trailer</value>
  </data>
  <data name="EquipmentHalfRoundEndDumpTrailer" xml:space="preserve">
    <value>Half-Round End Dump Trailer</value>
  </data>
  <data name="EquipmentBottomDumpTrailer" xml:space="preserve">
    <value>Bottom Dump Trailer</value>
  </data>
  <data name="EquipmentFuelTankSingle" xml:space="preserve">
    <value>Tanque de Gasolina (Individual)</value>
  </data>
  <data name="EquipmentFuelTankDouble" xml:space="preserve">
    <value>Tanque de Gasolina (Doble)</value>
  </data>
  <data name="EquipmentHopperGrainSingle" xml:space="preserve">
    <value>Tolva (grano, solo)</value>
  </data>
  <data name="EquipmentHopperGrainDouble" xml:space="preserve">
    <value>Tolva (grano, doble)</value>
  </data>
  <data name="EquipmentHopperSingle" xml:space="preserve">
    <value>Granos (Individual)</value>
  </data>
  <data name="EquipmentHopperDouble" xml:space="preserve">
    <value>Granos (Doble)</value>
  </data>
  <data name="EquipmentLiveFloor" xml:space="preserve">
    <value>Piso vivo</value>
  </data>
  <data name="EquipmentSaddlemount" xml:space="preserve">
    <value>Saddlemount</value>
  </data>
  <data name="EquipmentPneumatic" xml:space="preserve">
    <value>Neumático</value>
  </data>
  <data name="EquipmentLowboy" xml:space="preserve">
    <value>Lowboy</value>
  </data>
  <data name="EquipmentMaxiCube" xml:space="preserve">
    <value>Maxi-Cube</value>
  </data>
  <data name="EquipmentPowerUnitOnly" xml:space="preserve">
    <value>Sólo unidad de potencia</value>
  </data>
  <data name="EquipmentFracTankSquare" xml:space="preserve">
    <value>Frac Tank (Square)</value>
  </data>
  <data name="EquipmentFracTankRoundBottom" xml:space="preserve">
    <value>Frac Tank (Round Bottom)</value>
  </data>
  <data name="EquipmentTankerFoodGrade" xml:space="preserve">
    <value>Tanque (Comida)</value>
  </data>
  <data name="EquipmentTankerWater" xml:space="preserve">
    <value>Tanque (Agua)</value>
  </data>
  <data name="EquipmentTankerVacuum" xml:space="preserve">
    <value>Tanque (Vacuum)</value>
  </data>
  <data name="EquipmentTankerPetroleum" xml:space="preserve">
    <value>Tanque (Petroleo)</value>
  </data>
  <data name="EquipmentTankerChemical" xml:space="preserve">
    <value>Tanque (Quimicos)</value>
  </data>
  <data name="EquipmentSpecializedTrailer" xml:space="preserve">
    <value>Remolque especializado</value>
  </data>
  <data name="CargoUnitBinCardboard" xml:space="preserve">
    <value>Contenedor (Cartón)</value>
  </data>
  <data name="CargoUnitBinPlastic" xml:space="preserve">
    <value>Contenedor (Plastico)</value>
  </data>
  <data name="CargoUnitBox" xml:space="preserve">
    <value>Caja</value>
  </data>
  <data name="CargoUnitBulk" xml:space="preserve">
    <value>Bulto</value>
  </data>
  <data name="CargoUnitCarton" xml:space="preserve">
    <value>Caja de Cartón</value>
  </data>
  <data name="CargoUnitCrate" xml:space="preserve">
    <value>Cajon</value>
  </data>
  <data name="CargoUnitContainer" xml:space="preserve">
    <value>Contenedor</value>
  </data>
  <data name="CargoUnitRpc" xml:space="preserve">
    <value>RPC</value>
  </data>
  <data name="CargoUnitTote" xml:space="preserve">
    <value>Totalizador</value>
  </data>
  <data name="NoAccountInfo" xml:space="preserve">
    <value>Para utilizar esta aplicación móvil, su despachador necesita invitarle como chofer a su organización desde el sitio web de NCX</value>
  </data>
  <data name="NoAccountLink" xml:space="preserve">
    <value>¿sin cuenta? Toque aquí</value>
  </data>
  <data name="TutorialBtnText" xml:space="preserve">
    <value>Tutorial</value>
  </data>
  <data name="ActiveLoadHeader" xml:space="preserve">
    <value>Carga Activa</value>
  </data>
  <data name="PendingLoadsHeader" xml:space="preserve">
    <value>Cargas Pendientes</value>
  </data>
  <data name="UpdateThisLocationButton" xml:space="preserve">
    <value>ACTUALIZA ESTA UBICACIÓN</value>
  </data>
  <data name="PendingLoadHeader" xml:space="preserve">
    <value>Carga Pendiente</value>
  </data>
  <data name="SelectLocationToUpdateDropoffHeader" xml:space="preserve">
    <value>Entrega</value>
  </data>
  <data name="SelectLocationToUpdatePickupHeader" xml:space="preserve">
    <value>Recogida</value>
  </data>
  <data name="ReportDelayButton" xml:space="preserve">
    <value>INFORMAR UN RETRASO</value>
  </data>
  <data name="SelectLocationHeader" xml:space="preserve">
    <value>Selecione la ubicación</value>
  </data>
  <data name="SelectLocationEnRouteHeader" xml:space="preserve">
    <value>Selecione un destino enruta</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Compañía</value>
  </data>
  <data name="PrivacyPolicyLabel" xml:space="preserve">
    <value>Política de privacidad</value>
  </data>
  <data name="SignInAcceptTermsLabel" xml:space="preserve">
    <value>Al presionar el inicio de sesión, confirma su aceptación de los términos y condiciones de uso de la plataforma NCX</value>
  </data>
  <data name="TermsOfServiceLabel" xml:space="preserve">
    <value>Términos de servicio</value>
  </data>
  <data name="MainMenuHeaderFormat" xml:space="preserve">
    <value>Hola, {0}</value>
  </data>
  <data name="License" xml:space="preserve">
    <value>LICENSIA</value>
  </data>
  <data name="MedicalCertificate" xml:space="preserve">
    <value>CERTIFICADO MÉDICO</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Ajustas</value>
  </data>
  <data name="HelpFAQ" xml:space="preserve">
    <value>Preguntas Frecuentes</value>
  </data>
  <data name="Help" xml:space="preserve">
    <value>Ayuda</value>
  </data>
  <data name="Legal" xml:space="preserve">
    <value>LEGAL</value>
  </data>
  <data name="ContactUsByEmail" xml:space="preserve">
    <value>Envianos un correo electrónico</value>
  </data>
  <data name="ContactUsByPhone" xml:space="preserve">
    <value>Llámenos</value>
  </data>
  <data name="AppUpdateAvailableMessage" xml:space="preserve">
    <value>Una nueva actualización de la aplicación está disponible</value>
  </data>
  <data name="AppUpdateAvailableTitle" xml:space="preserve">
    <value>Actualización de la aplicación</value>
  </data>
  <data name="AppUpdateButtonText" xml:space="preserve">
    <value>Actualizar</value>
  </data>
  <data name="AppUpdateNotNowButtonText" xml:space="preserve">
    <value>Ahora no</value>
  </data>
  <data name="DataConnectivityIssue" xml:space="preserve">
    <value>Tu conexión de Internet aparece que esta fuera de línea</value>
  </data>
  <data name="TutorialPageTitle" xml:space="preserve">
    <value>Tutorial</value>
  </data>
  <data name="ShowDetails" xml:space="preserve">
    <value>VE DETALLES</value>
  </data>
  <data name="RefreshBtnText" xml:space="preserve">
    <value>Recargar</value>
  </data>
  <data name="Loads" xml:space="preserve">
    <value>CARGAS</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Tablero</value>
  </data>
  <data name="Notifications" xml:space="preserve">
    <value>Notificaciones</value>
  </data>
  <data name="RecordFuelStop" xml:space="preserve">
    <value>Grabar una parada de combustible</value>
  </data>
  <data name="NewLoadPageTitle" xml:space="preserve">
    <value>Nueva Carga</value>
  </data>
</root>