<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AutomaticLogoutMessageText" xml:space="preserve">
    <value>You have been logged out.
Check to make sure you were not logged in from another device.</value>
  </data>
  <data name="AutomaticLogoutTitleText" xml:space="preserve">
    <value>Login required</value>
  </data>
  <data name="CapturePODInfoText" xml:space="preserve">
    <value>Take a photo of the Proof of Delivery (POD) or select an existing photo from the library. Be sure to clearly capture the signature of the proof of delivery in the photo.

If the Proof of Delivery contains multiple pages, take one photo at a time.</value>
  </data>
  <data name="CloseText" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="ConfirmationText" xml:space="preserve">
    <value>Confirmation</value>
  </data>
  <data name="CopyrightText" xml:space="preserve">
    <value>Copyright © National Carrier Exchange
All Rights Reserved</value>
  </data>
  <data name="DetailsText" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="ExpectedDateText" xml:space="preserve">
    <value>Expected</value>
  </data>
  <data name="DelayReasonAccidentText" xml:space="preserve">
    <value>Minor Accident</value>
  </data>
  <data name="DelayReasonBadWeatherText" xml:space="preserve">
    <value>Bad Weather</value>
  </data>
  <data name="DelayReasonBreakdownText" xml:space="preserve">
    <value>Minor Breakdown</value>
  </data>
  <data name="DelayReasonFlatTireText" xml:space="preserve">
    <value>Flat Tire</value>
  </data>
  <data name="DelayReasonHeavyTrafficText" xml:space="preserve">
    <value>Heavy Traffic</value>
  </data>
  <data name="DelayReasonRoadClosureText" xml:space="preserve">
    <value>Road Closure</value>
  </data>
  <data name="ForgotPasswordText" xml:space="preserve">
    <value>Forgot password?</value>
  </data>
  <data name="HidePasswordText" xml:space="preserve">
    <value>Hide password</value>
  </data>
  <data name="LastReportedDelayText" xml:space="preserve">
    <value>You reported a '{0}' delay on this load.</value>
  </data>
  <data name="LoginText" xml:space="preserve">
    <value>SIGN IN</value>
  </data>
  <data name="PasswordText" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="PhotoLibraryText" xml:space="preserve">
    <value>PHOTO LIBRARY</value>
  </data>
  <data name="PhotoViewerStatusText" xml:space="preserve">
    <value>Photo {0} of {1}</value>
  </data>
  <data name="RemovePhotoConfirmationText" xml:space="preserve">
    <value>Are you sure you want to remove this photo from the list?</value>
  </data>
  <data name="SendPODInfoText" xml:space="preserve">
    <value>If you need to take another photo select the "Take Another Photo" button, and when you're done tap the "Send" button to complete the load.</value>
  </data>
  <data name="SendPODText" xml:space="preserve">
    <value>Proof of Delivery</value>
  </data>
  <data name="SendText" xml:space="preserve">
    <value>Send</value>
  </data>
  <data name="ReportDelayConfirmationText" xml:space="preserve">
    <value>Report '{0}' for this load?</value>
  </data>
  <data name="ReportDelayHeader" xml:space="preserve">
    <value>Report a delay</value>
  </data>
  <data name="ShowPasswordText" xml:space="preserve">
    <value>Show password</value>
  </data>
  <data name="TakeAnotherPhotoText" xml:space="preserve">
    <value>TAKE ANOTHER PHOTO</value>
  </data>
  <data name="TakePhotoText" xml:space="preserve">
    <value>TAKE PHOTO</value>
  </data>
  <data name="UpdateLoadOperationStatusMessageFormatText" xml:space="preserve">
    <value>Change status to {0}?</value>
  </data>
  <data name="UpdateStatusText" xml:space="preserve">
    <value>UPDATE STATUS</value>
  </data>
  <data name="UserLoginErrorMessageText" xml:space="preserve">
    <value>Check your email/phone number and password</value>
  </data>
  <data name="UserLoginErrorTitleText" xml:space="preserve">
    <value>Login failed</value>
  </data>
  <data name="UserLogoutMessageText" xml:space="preserve">
    <value>Warning, you are logging out of the application. 
    All of your loads data will be lost. 
    You will not be able to retrieve this information unless you have internet connection when you log back in?</value>
  </data>
  <data name="UsernameText" xml:space="preserve">
    <value>Email or Phone number</value>
  </data>
  <data name="LocationPermissionDeniedMessageText" xml:space="preserve">
    <value>Without accessing your location, the dispatcher is not able to know your location.
The application requires you to allow the location permission.</value>
  </data>
  <data name="PermissionDeniedText" xml:space="preserve">
    <value>Permission denied</value>
  </data>
  <data name="LocationPermissionDeniedEnableSettingsMessageText" xml:space="preserve">
    <value>Without accessing your location, the dispatcher is not able to know your location.

Please enable the permission in
Settings &gt; Permissions 
 and check 'Location' permission.</value>
  </data>
  <data name="ShowAppSettingsText" xml:space="preserve">
    <value>Show application settings</value>
  </data>
  <data name="PODCameraPermissionDeniedMessageText" xml:space="preserve">
    <value>Without accessing your camera, you cannot take a photo for proof of delivery.</value>
  </data>
  <data name="PODCameraPermissionDeniedEnableSettingsMessageText" xml:space="preserve">
    <value>Without accessing your camera, you cannot take a photo for proof of delivery.

Please enable the permission in
Settings &gt; Permissions 
 and check 'Camera' permission.</value>
  </data>
  <data name="RetryText" xml:space="preserve">
    <value>Retry</value>
  </data>
  <data name="PODStoragePermissionDeniedMessageText" xml:space="preserve">
    <value>Without accessing your storage, you cannot send a photo for proof of delivery.</value>
  </data>
  <data name="PODStoragePermissionDeniedEnableSettingsMessageText" xml:space="preserve">
    <value>Without accessing your storage, you cannot send a photo for proof of delivery.

Please enable the permission in
Settings &gt; Permissions 
 and check 'Storage' permission.</value>
  </data>
  <data name="ResetPasswordText" xml:space="preserve">
    <value>Reset My Password</value>
  </data>
  <data name="ResetPasswordSuccessfulTitleText" xml:space="preserve">
    <value>Password reset successful</value>
  </data>
  <data name="ResetPasswordSuccessfulMessageText" xml:space="preserve">
    <value>Your Password reset request has been submitted. Please check your email or phone SMS for for instructions to reset your password.</value>
  </data>
  <data name="ServerErrorTitleText" xml:space="preserve">
    <value>Could not contact server</value>
  </data>
  <data name="EmailText" xml:space="preserve">
    <value>Email address or Phone number</value>
  </data>
  <data name="SelectLanguageText" xml:space="preserve">
    <value>Select Language</value>
  </data>
  <data name="LanguageChangeConfirmation" xml:space="preserve">
    <value>Change the application language to {0}?</value>
  </data>
  <data name="LoadListPageHeader" xml:space="preserve">
    <value>NCX</value>
  </data>
  <data name="NoLoadsAssigned" xml:space="preserve">
    <value>You have no loads assigned</value>
  </data>
  <data name="SignOut" xml:space="preserve">
    <value>Sign out</value>
  </data>
  <data name="FirstPickup" xml:space="preserve">
    <value>FIRST PICKUP</value>
  </data>
  <data name="FinalDropOff" xml:space="preserve">
    <value>FINAL DROPOFF</value>
  </data>
  <data name="LoadId" xml:space="preserve">
    <value>LOAD ID</value>
  </data>
  <data name="PickUpCountSingular" xml:space="preserve">
    <value>{0} PickUp</value>
  </data>
  <data name="PickUpCountPlural" xml:space="preserve">
    <value>{0} PickUps</value>
  </data>
  <data name="DropoffCountSingular" xml:space="preserve">
    <value>{0} Dropoff</value>
  </data>
  <data name="DropoffCountPlural" xml:space="preserve">
    <value>{0} Dropoffs</value>
  </data>
  <data name="LoadInfo" xml:space="preserve">
    <value>Load Info</value>
  </data>
  <data name="Schedule" xml:space="preserve">
    <value>Schedule</value>
  </data>
  <data name="LoadDetails" xml:space="preserve">
    <value>Load Details</value>
  </data>
  <data name="IDText" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="StatusText" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="EquipmentTypeText" xml:space="preserve">
    <value>Equipment Type</value>
  </data>
  <data name="TrailerLengthText" xml:space="preserve">
    <value>Trailer Length</value>
  </data>
  <data name="RequiredTemperatureText" xml:space="preserve">
    <value>Required Temperature</value>
  </data>
  <data name="StopsText" xml:space="preserve">
    <value>Stops</value>
  </data>
  <data name="NotesText" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="PickupNumberText" xml:space="preserve">
    <value>Pickup Number</value>
  </data>
  <data name="DeliverNumberText" xml:space="preserve">
    <value>Delivery Number</value>
  </data>
  <data name="PurchaseOrderNumberText" xml:space="preserve">
    <value>Purchase Order Number</value>
  </data>
  <data name="ContactNameText" xml:space="preserve">
    <value>Contact Name</value>
  </data>
  <data name="ContactNumberText" xml:space="preserve">
    <value>Contact Number</value>
  </data>
  <data name="ApptContactNameText" xml:space="preserve">
    <value>Appt Contact Name</value>
  </data>
  <data name="ApptContactNumberText" xml:space="preserve">
    <value>Appt Contact Number</value>
  </data>
  <data name="ApptRequiredText" xml:space="preserve">
    <value>Appt Required?</value>
  </data>
  <data name="MiscFeesText" xml:space="preserve">
    <value>Misc Fees</value>
  </data>
  <data name="EntranceFeesText" xml:space="preserve">
    <value>Entrance Fees</value>
  </data>
  <data name="UnloadingFeesText" xml:space="preserve">
    <value>Unloading Fees</value>
  </data>
  <data name="PalletExchangeFeeText" xml:space="preserve">
    <value>Pallet Exchange Fee</value>
  </data>
  <data name="InstructionsText" xml:space="preserve">
    <value>Instructions</value>
  </data>
  <data name="HasLiftingEquipmentText" xml:space="preserve">
    <value>Has Lifting Equipment</value>
  </data>
  <data name="HasLoadingDockText" xml:space="preserve">
    <value>Has Loading Dock</value>
  </data>
  <data name="HoursOfOperationText" xml:space="preserve">
    <value>Hours of Operation</value>
  </data>
  <data name="NotActiveLoadMessageText" xml:space="preserve">
    <value>You can only update the status of an active load.</value>
  </data>
  <data name="NotActiveLoadTitleText" xml:space="preserve">
    <value>Another load is active</value>
  </data>
  <data name="CanUpdateActivePickupOrDropOffMessageText" xml:space="preserve">
    <value>You can only update the status of an active pickup or dropoff.</value>
  </data>
  <data name="CanUpdateActivePickupOrDropOffTitleText" xml:space="preserve">
    <value>Another pickup or dropoff is active</value>
  </data>
  <data name="CompletePickupFirstMessageText" xml:space="preserve">
    <value>You must complete all pickups before drop-offs.</value>
  </data>
  <data name="InforamationTitleText" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="PickupLocationText" xml:space="preserve">
    <value>Pickup Location</value>
  </data>
  <data name="DropOffLocationText" xml:space="preserve">
    <value>Dropoff Location</value>
  </data>
  <data name="LoadPickupDetailsText" xml:space="preserve">
    <value>Pickup Details</value>
  </data>
  <data name="LoadDropoffDetailsText" xml:space="preserve">
    <value>Dropoff Details</value>
  </data>
  <data name="ItemDescriptionText" xml:space="preserve">
    <value>Item Description</value>
  </data>
  <data name="UnitTypeText" xml:space="preserve">
    <value>Unit Type</value>
  </data>
  <data name="UnitCountText" xml:space="preserve">
    <value>Unit Count</value>
  </data>
  <data name="UnitWeightText" xml:space="preserve">
    <value>Unit Weight</value>
  </data>
  <data name="NumberOfPalletsText" xml:space="preserve">
    <value>Number of pallets</value>
  </data>
  <data name="CargoDetailsText" xml:space="preserve">
    <value>Cargo Details</value>
  </data>
  <data name="CargoDestinationDetailsText" xml:space="preserve">
    <value>Cargo Destination Details</value>
  </data>
  <data name="CargoItemHeaderFormatText" xml:space="preserve">
    <value>{0} of {1}</value>
  </data>
  <data name="ConnectionErrorMessageText" xml:space="preserve">
    <value>NCX is having trouble connecting to the server. Try again later.</value>
  </data>
  <data name="InternetNotAvailableMessageText" xml:space="preserve">
    <value>Could not contact the user login endpoint.
Make sure you are connected to internet.</value>
  </data>
  <data name="APIHostChangedMessageText" xml:space="preserve">
    <value>API endpoint host changed to</value>
  </data>
  <data name="SuccessTitleText" xml:space="preserve">
    <value>Success</value>
  </data>
  <data name="MalformedUrlMessageText" xml:space="preserve">
    <value>The URL is malformed.
Make sure the URL is in the form 'https://server'.</value>
  </data>
  <data name="MalformedUrlTitleText" xml:space="preserve">
    <value>Url malformed</value>
  </data>
  <data name="InvalidDataFromBackendMessageText" xml:space="preserve">
    <value>Invalid data was returned from the backend.
Reason: {0}

Application cannot continue.</value>
  </data>
  <data name="InvalidDataFromBackendTitleText" xml:space="preserve">
    <value>Consistency error</value>
  </data>
  <data name="SecretCodeText" xml:space="preserve">
    <value>Secret Code</value>
  </data>
  <data name="GoText" xml:space="preserve">
    <value>GO</value>
  </data>
  <data name="ProofOfDeliveryText" xml:space="preserve">
    <value>Proof of Delivery Image</value>
  </data>
  <data name="ChangeLanguageText" xml:space="preserve">
    <value>Change language</value>
  </data>
  <data name="CheckedInText" xml:space="preserve">
    <value>Checked In</value>
  </data>
  <data name="EnRouteText" xml:space="preserve">
    <value>En Route</value>
  </data>
  <data name="PendingText" xml:space="preserve">
    <value>Pending</value>
  </data>
  <data name="BackToLoginText" xml:space="preserve">
    <value>Back to Login</value>
  </data>
  <data name="CompletedText" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="CanceledText" xml:space="preserve">
    <value>Canceled</value>
  </data>
  <data name="DeletedText" xml:space="preserve">
    <value>Deleted</value>
  </data>
  <data name="NewLoadTitleText" xml:space="preserve">
    <value>You have a new load</value>
  </data>
  <data name="LocationTrackingNotificationText" xml:space="preserve">
    <value>NCX Application is Running</value>
  </data>
  <data name="CancelButtonText" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="OKButtonText" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="CloseButtonText" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="YesText" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="NoText" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="SendPODConfirmationMessage" xml:space="preserve">
    <value>Send proof of delivery?</value>
  </data>
  <data name="AllDayHoursOfOperation" xml:space="preserve">
    <value>All Day</value>
  </data>
  <data name="TapToCall" xml:space="preserve">
    <value>(tap to call)</value>
  </data>
  <data name="TapForDirections" xml:space="preserve">
    <value>(tap for directions)</value>
  </data>
  <data name="EquipmentRefrigeratedVan" xml:space="preserve">
    <value>Refrigerated Van</value>
  </data>
  <data name="CargoUnitBag" xml:space="preserve">
    <value>Bag</value>
  </data>
  <data name="NoMapAppForDirectionsMessage" xml:space="preserve">
    <value>Unable to start an application to show you directions to the location.
Make sure you have an application installed which can give map directions.</value>
  </data>
  <data name="DriverDateCreated" xml:space="preserve">
    <value>Date Created</value>
  </data>
  <data name="DriverLicenseState" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="LicenseExpirationDate" xml:space="preserve">
    <value>Expiration Date</value>
  </data>
  <data name="LicenseNumber" xml:space="preserve">
    <value>License Number</value>
  </data>
  <data name="MedicalCertificateExpirationDate" xml:space="preserve">
    <value>Expiration Date</value>
  </data>
  <data name="MedicalCertificateNationalRegistrationNumber" xml:space="preserve">
    <value>Certificate Number</value>
  </data>
  <data name="MobilePhone" xml:space="preserve">
    <value>Mobile Phone</value>
  </data>
  <data name="NumberLoadsHauled" xml:space="preserve">
    <value>Number of Loads Hauled</value>
  </data>
  <data name="DriverProfileTitle" xml:space="preserve">
    <value>Your Profile</value>
  </data>
  <data name="YourProfile" xml:space="preserve">
    <value>Your profile</value>
  </data>
  <data name="UpdatePasswordNewPasswordDiffersMessage" xml:space="preserve">
    <value>The new password must match and the new password confirmation</value>
  </data>
  <data name="UpdatePasswordErrorTitle" xml:space="preserve">
    <value>Error changing your password</value>
  </data>
  <data name="YourCurrentPassword" xml:space="preserve">
    <value>Your current password</value>
  </data>
  <data name="YourNewPassword" xml:space="preserve">
    <value>Your new password</value>
  </data>
  <data name="ConfirmYourNewPassword" xml:space="preserve">
    <value>Confirm your new password</value>
  </data>
  <data name="UpdatePasswordPageTitle" xml:space="preserve">
    <value>Change your password</value>
  </data>
  <data name="ChangePasswordBtnText" xml:space="preserve">
    <value>Change password</value>
  </data>
  <data name="UserProfileUpdatePasswordButton" xml:space="preserve">
    <value>Change your password</value>
  </data>
  <data name="ShowPasswordsButtonText" xml:space="preserve">
    <value>Show passwords</value>
  </data>
  <data name="HidePasswordsButtonText" xml:space="preserve">
    <value>Hide passwords</value>
  </data>
  <data name="UpdatePasswordErrorMessage" xml:space="preserve">
    <value>Make sure the existing password is correct.</value>
  </data>
  <data name="PasswordChangedSuccessfullyMessage" xml:space="preserve">
    <value>You password was changed successfully.</value>
  </data>
  <data name="UpdatePasswordTooSmallErrorMessage" xml:space="preserve">
    <value>You password must have a minimum of 8 characters</value>
  </data>
  <data name="ResetPasswordErrorMessageText" xml:space="preserve">
    <value>Make sure the email or phone number is correct.</value>
  </data>
  <data name="ResetPasswordErrorMessageTitle" xml:space="preserve">
    <value>Error resetting your password</value>
  </data>
  <data name="EquipmentDryVan" xml:space="preserve">
    <value>Dry Van</value>
  </data>
  <data name="EquipmentVentilatedVan" xml:space="preserve">
    <value>Ventilated Van</value>
  </data>
  <data name="EquipmentAirRideVan" xml:space="preserve">
    <value>Air-Ride Van</value>
  </data>
  <data name="EquipmentVanwithCurtains" xml:space="preserve">
    <value>Van with Curtains</value>
  </data>
  <data name="EquipmentFlatbed" xml:space="preserve">
    <value>Flatbed</value>
  </data>
  <data name="EquipmentFlatbedwithSides" xml:space="preserve">
    <value>Flatbed with Sides</value>
  </data>
  <data name="EquipmentFlatbedwithTarps" xml:space="preserve">
    <value>Flatbed with Tarps</value>
  </data>
  <data name="EquipmentFlatbedStretch" xml:space="preserve">
    <value>Flatbed Stretch</value>
  </data>
  <data name="EquipmentFlatbedSideKit" xml:space="preserve">
    <value>Flatbed Side Kit</value>
  </data>
  <data name="EquipmentAutoCarrier" xml:space="preserve">
    <value>Auto Carrier</value>
  </data>
  <data name="EquipmentBeverageTrailer" xml:space="preserve">
    <value>Beverage Trailer</value>
  </data>
  <data name="EquipmentIntermodal20Container" xml:space="preserve">
    <value>Intermodal 20' Container</value>
  </data>
  <data name="EquipmentIntermodal40Container" xml:space="preserve">
    <value>Intermodal 40' Container</value>
  </data>
  <data name="EquipmentLivestockTrailerCattle" xml:space="preserve">
    <value>Livestock Trailer (Cattle)</value>
  </data>
  <data name="EquipmentLivestockTrailerHogs" xml:space="preserve">
    <value>Livestock Trailer (Hogs)</value>
  </data>
  <data name="EquipmentLivestockTrailerChickens" xml:space="preserve">
    <value>Livestock Trailer (Chickens)</value>
  </data>
  <data name="EquipmentLoggerTrailer" xml:space="preserve">
    <value>Logger Trailer</value>
  </data>
  <data name="EquipmentBoatTrailerSingle" xml:space="preserve">
    <value>Boat Trailer (Single)</value>
  </data>
  <data name="EquipmentBoatTrailerDouble" xml:space="preserve">
    <value>Boat Trailer (Double)</value>
  </data>
  <data name="EquipmentRemovableGooseneckRGN" xml:space="preserve">
    <value>Removable Gooseneck (RGN)</value>
  </data>
  <data name="EquipmentConestogaTrailer" xml:space="preserve">
    <value>Conestoga Trailer</value>
  </data>
  <data name="EquipmentDoubleDrop" xml:space="preserve">
    <value>Double Drop</value>
  </data>
  <data name="EquipmentDoubleDropStretch" xml:space="preserve">
    <value>Double Drop Stretch</value>
  </data>
  <data name="EquipmentDoubleDropExtendable" xml:space="preserve">
    <value>Double Drop Extendable</value>
  </data>
  <data name="EquipmentDoubleDropSideKit" xml:space="preserve">
    <value>Double Drop Side Kit</value>
  </data>
  <data name="EquipmentBTrainCombo" xml:space="preserve">
    <value>B-Train Combo</value>
  </data>
  <data name="EquipmentSingleDrop" xml:space="preserve">
    <value>Single Drop</value>
  </data>
  <data name="EquipmentSingleDropStretch" xml:space="preserve">
    <value>Single Drop Stretch</value>
  </data>
  <data name="EquipmentSingleDropExtendable" xml:space="preserve">
    <value>Single Drop Extendable</value>
  </data>
  <data name="EquipmentSingleDropSideKit" xml:space="preserve">
    <value>Single Drop Side Kit</value>
  </data>
  <data name="EquipmentDumpTrailer" xml:space="preserve">
    <value>Dump Trailer</value>
  </data>
  <data name="EquipmentEndDumpTrailer" xml:space="preserve">
    <value>End Dump Trailer</value>
  </data>
  <data name="EquipmentHalfRoundEndDumpTrailer" xml:space="preserve">
    <value>Half-Round End Dump Trailer</value>
  </data>
  <data name="EquipmentBottomDumpTrailer" xml:space="preserve">
    <value>Bottom Dump Trailer</value>
  </data>
  <data name="EquipmentFuelTankSingle" xml:space="preserve">
    <value>Fuel Tank (Single)</value>
  </data>
  <data name="EquipmentFuelTankDouble" xml:space="preserve">
    <value>Fuel Tank (Double)</value>
  </data>
  <data name="EquipmentHopperGrainSingle" xml:space="preserve">
    <value>Hopper (Grain, Single)</value>
  </data>
  <data name="EquipmentHopperGrainDouble" xml:space="preserve">
    <value>Hopper (Grain, Double)</value>
  </data>
  <data name="EquipmentHopperSingle" xml:space="preserve">
    <value>Hopper (Single)</value>
  </data>
  <data name="EquipmentHopperDouble" xml:space="preserve">
    <value>Hopper (Double)</value>
  </data>
  <data name="EquipmentLiveFloor" xml:space="preserve">
    <value>Live Floor</value>
  </data>
  <data name="EquipmentSaddlemount" xml:space="preserve">
    <value>Saddlemount</value>
  </data>
  <data name="EquipmentPneumatic" xml:space="preserve">
    <value>Pneumatic</value>
  </data>
  <data name="EquipmentLowboy" xml:space="preserve">
    <value>Lowboy</value>
  </data>
  <data name="EquipmentMaxiCube" xml:space="preserve">
    <value>Maxi-Cube</value>
  </data>
  <data name="EquipmentPowerUnitOnly" xml:space="preserve">
    <value>Power Unit Only</value>
  </data>
  <data name="EquipmentFracTankSquare" xml:space="preserve">
    <value>Frac Tank (Square)</value>
  </data>
  <data name="EquipmentFracTankRoundBottom" xml:space="preserve">
    <value>Frac Tank (Round Bottom)</value>
  </data>
  <data name="EquipmentTankerFoodGrade" xml:space="preserve">
    <value>Tanker (Food-Grade)</value>
  </data>
  <data name="EquipmentTankerWater" xml:space="preserve">
    <value>Tanker (Water)</value>
  </data>
  <data name="EquipmentTankerVacuum" xml:space="preserve">
    <value>Tanker (Vacuum)</value>
  </data>
  <data name="EquipmentTankerPetroleum" xml:space="preserve">
    <value>Tanker (Petroleum)</value>
  </data>
  <data name="EquipmentTankerChemical" xml:space="preserve">
    <value>Tanker (Chemical)</value>
  </data>
  <data name="EquipmentSpecializedTrailer" xml:space="preserve">
    <value>Specialized Trailer</value>
  </data>
  <data name="CargoUnitBinCardboard" xml:space="preserve">
    <value>Bin (Cardboard)</value>
  </data>
  <data name="CargoUnitBinPlastic" xml:space="preserve">
    <value>Bin (Plastic)</value>
  </data>
  <data name="CargoUnitBox" xml:space="preserve">
    <value>Box</value>
  </data>
  <data name="CargoUnitBulk" xml:space="preserve">
    <value>Bulk</value>
  </data>
  <data name="CargoUnitCarton" xml:space="preserve">
    <value>Carton</value>
  </data>
  <data name="CargoUnitCrate" xml:space="preserve">
    <value>Crate</value>
  </data>
  <data name="CargoUnitContainer" xml:space="preserve">
    <value>Container</value>
  </data>
  <data name="CargoUnitRpc" xml:space="preserve">
    <value>RPC</value>
  </data>
  <data name="CargoUnitTote" xml:space="preserve">
    <value>Tote</value>
  </data>
  <data name="NoAccountInfo" xml:space="preserve">
    <value>To use this mobile app, your dispatcher needs to invite you as a driver to their organization from the NCX website</value>
  </data>
  <data name="NoAccountLink" xml:space="preserve">
    <value>No account? Tap here</value>
  </data>
  <data name="TutorialBtnText" xml:space="preserve">
    <value>Tutorial</value>
  </data>
  <data name="ActiveLoadHeader" xml:space="preserve">
    <value>Active Load</value>
  </data>
  <data name="PendingLoadsHeader" xml:space="preserve">
    <value>Pending Loads</value>
  </data>
  <data name="UpdateThisLocationButton" xml:space="preserve">
    <value>UPDATE THIS LOCATION</value>
  </data>
  <data name="PendingLoadHeader" xml:space="preserve">
    <value>Pending Load</value>
  </data>
  <data name="SelectLocationToUpdateDropoffHeader" xml:space="preserve">
    <value>Drop-off</value>
  </data>
  <data name="SelectLocationToUpdatePickupHeader" xml:space="preserve">
    <value>Pickup</value>
  </data>
  <data name="ReportDelayButton" xml:space="preserve">
    <value>REPORT A DELAY</value>
  </data>
  <data name="SelectLocationHeader" xml:space="preserve">
    <value>Select location to update</value>
  </data>
  <data name="SelectLocationEnRouteHeader" xml:space="preserve">
    <value>Select an en route destination</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="PrivacyPolicyLabel" xml:space="preserve">
    <value>Privacy Policy</value>
  </data>
  <data name="SignInAcceptTermsLabel" xml:space="preserve">
    <value>Pressing sign in confirms your acceptance of the terms and conditions for using the NCX platform.</value>
  </data>
  <data name="TermsOfServiceLabel" xml:space="preserve">
    <value>Terms of Service</value>
  </data>
  <data name="MainMenuHeaderFormat" xml:space="preserve">
    <value>Hello, {0}</value>
  </data>
  <data name="License" xml:space="preserve">
    <value>LICENSE</value>
  </data>
  <data name="MedicalCertificate" xml:space="preserve">
    <value>MEDICAL CERTIFICATE</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="HelpFAQ" xml:space="preserve">
    <value>Help FAQ</value>
  </data>
  <data name="Help" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="Legal" xml:space="preserve">
    <value>LEGAL</value>
  </data>
  <data name="ContactUsByEmail" xml:space="preserve">
    <value>Send us an email</value>
  </data>
  <data name="ContactUsByPhone" xml:space="preserve">
    <value>Call us</value>
  </data>
  <data name="AppUpdateAvailableMessage" xml:space="preserve">
    <value>A new application update is available</value>
  </data>
  <data name="AppUpdateAvailableTitle" xml:space="preserve">
    <value>Application update</value>
  </data>
  <data name="AppUpdateButtonText" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="AppUpdateNotNowButtonText" xml:space="preserve">
    <value>Not now</value>
  </data>
  <data name="DataConnectivityIssue" xml:space="preserve">
    <value>Your internet connection appears to be offline</value>
  </data>
  <data name="TutorialPageTitle" xml:space="preserve">
    <value>Tutorial</value>
  </data>
  <data name="ShowDetails" xml:space="preserve">
    <value>SHOW DETAILS</value>
  </data>
  <data name="RefreshBtnText" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="Loads" xml:space="preserve">
    <value>LOADS</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="Notifications" xml:space="preserve">
    <value>Notifications</value>
  </data>
  <data name="RecordFuelStop" xml:space="preserve">
    <value>Record a Fuel Stop</value>
  </data>
  <data name="CurrentLocation" xml:space="preserve">
    <value>Current location</value>
  </data>
  <data name="FuelType" xml:space="preserve">
    <value>Fuel type</value>
  </data>
  <data name="LocationOfRefuel" xml:space="preserve">
    <value>Location of refuel</value>
  </data>
  <data name="OdometerReading" xml:space="preserve">
    <value>Odometer reading (miles)</value>
  </data>
  <data name="PricePerGallon" xml:space="preserve">
    <value>Price per gallon ($)</value>
  </data>
  <data name="QuantityOfFuel" xml:space="preserve">
    <value>Quantity of fuel (gallons)</value>
  </data>
  <data name="Vehicle" xml:space="preserve">
    <value>Vehicle</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="StartTravel" xml:space="preserve">
    <value>START TRAVEL</value>
  </data>
  <data name="StopTravel" xml:space="preserve">
    <value>STOP TRAVEL</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="Submit" xml:space="preserve">
    <value>SUBMIT</value>
  </data>
  <data name="TakePhotoOfReceiptText" xml:space="preserve">
    <value>TAKE PHOTO OF RECEIPT</value>
  </data>
  <data name="TakePhotoOrSelectExistingText" xml:space="preserve">
    <value>Take a photo of the receipt or select an existing photo from your library.</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="MustSelectPowerUnitToGoActiveMessage" xml:space="preserve">
    <value>You must first select the Power Unit you are using before you can go on duty.</value>
  </data>
  <data name="NewLoadPageTitle" xml:space="preserve">
    <value>New Load</value>
  </data>
</root>