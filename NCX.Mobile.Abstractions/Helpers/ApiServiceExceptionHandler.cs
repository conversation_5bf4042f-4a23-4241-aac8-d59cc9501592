﻿using System;
using System.Threading.Tasks;

namespace NCX.Mobile.Helpers
{
    // Differentiates between:
    //   - Success
    //   - Could not reach the server
    //   - Server replied with HTTP error
    //   - <PERSON> replied with Unauthorized Http Error
    public abstract class ApiServiceExceptionHandler
    {
        public void HandleException(Exception ex)
        {
            if (ex is ApiUnauthorizedException)
            {
                OnApiUnauthorizedException();
            }
            else if (ex is ApiErrorException)
            {
                var apiErrorException = ex as ApiErrorException;

                if (apiErrorException.HttpStatusCode != null)
                {
                    OnApiError(apiErrorException);
                }
                else
                {
                    OnApiConnectionError(apiErrorException);
                }
            }
            //else if (ex is TaskCanceledException)
            //{
            //    OnApiCancelled((TaskCanceledException)ex);
            //}
            else
            {
                throw ex;
            }
        }

        protected abstract void OnApiError(ApiErrorException ex);

        protected abstract void OnApiUnauthorizedException();

        protected abstract void OnApiConnectionError(ApiErrorException ex);

        //protected abstract void OnApiCancelled(TaskCanceledException ex);
    }
}
