﻿using System;
using System.Net;

namespace NCX.Mobile.Helpers
{
    public class ApiErrorException : Exception
    {
        public HttpStatusCode? HttpStatusCode { get; }

        public string ReasonPhrase { get; }

        public ApiErrorException(Exception innerException, HttpStatusCode? httpStatusCode = null, string reasonPhrase = null) : base("API call failed", innerException)
        {
            HttpStatusCode = httpStatusCode;
            ReasonPhrase = reasonPhrase;
        }
    }
}
