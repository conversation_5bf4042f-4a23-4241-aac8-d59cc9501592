﻿using System.Collections.Generic;
using System.Collections.ObjectModel;

namespace NCX.Mobile.Helpers
{
    // Transforms a hierachy of items into a flat sequence, by expanding \ collapsing node items
    public class FlatHierachyItemsDataSource<T> : ObservableCollection<T> where T : IEnumerable<T>
    {
        readonly IEnumerable<T> _items;

        public FlatHierachyItemsDataSource(IEnumerable<T> items)
        {
            _items = items;

            foreach (T item in items)
            {
                Add(item);
            }
        }

        public void Expand(T item)
        {
            int itemIndex = IndexOf(item);

            foreach (T childItem in item)
            {
                Insert(itemIndex + 1, childItem);
            }
        }

        public void Collapse(T item)
        {
            foreach (T childItem in item)
            {
                Remove(childItem);
            }
        }
    }
}
