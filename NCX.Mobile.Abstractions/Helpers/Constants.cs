﻿namespace NCX.Mobile.Helpers
{
    public static class Constants
    {
        public const string Log_Tag = "NCX";

        public const string Host = "nationalcarrierexchange.com";
        public const string HostDev = "https://dev." + Host;
        public const string HostQA3 = "https://qa3." + Host;
        public const string HostQA4 = "https://qa4." + Host;
        public const string HostProd = "https://app." + Host;

        // The minimum distance (measured in meters) a device must move horizontally before an update event is generated.
        public const double LocationUpdateDisplacement = 500 * 0.3048; // 500 feet
        public const double LocationUpdateDisplacement_ActiveLoad = 500 * 0.3048; // 500 feet
        public const double LocationUpdateDisplacement_ActiveLoad_SlowSpeed = 100 * 0.3048; // 100 feet

        // Interval for active location updates, in milliseconds. 
        public const double LocationUpdateInterval = 10 * 1000; // 10 secs
        public const double LocationUpdateInterval_ActiveLoad = 3 * 1000; // 3 secs
        public const double LocationUpdateInterval_ActiveLoad_SlowSpeed = 2.5 * 1000; // 2.5 secs

        // Meters / second
        public const double LocationUpdateInterval_ActiveLoad_SlowSpeed_Threshold = 17.7778; // 64 km / h

        public const string PODDirectory = "PODs";
        public const int PODCompressionQuality = 70;
        public const int PODMaxSize = 1024;

        public const string TermsOfServiceWebpageUri = "https://www.nationalcarrierexchange.com/terms.html";
        public const string PrivacyWebpageUri = "https://www.nationalcarrierexchange.com/privacy.html";

        public const string DeepLinkScheme = "ncx";
        public const string DeepLinkHostLogin = "login";

#if DEBUG
        public const string AppUpdateManifestUri = null;
#else 
        public const string AppUpdateManifestUri = "http://updates.nationalcarrierexchange.com/mobileapp_updates.txt";
#endif

        public const string AppStoreUriAndroid = "https://play.google.com/store/apps/details?id=com.ncx.mobile";
        public const string AppStoreUriIOS = "https://itunes.apple.com/us/app/national-carrier-exchange-ncx/id956684956?mt=8";

        public const string TermsOfServiceUrl = "https://www.nationalcarrierexchange.com/terms.html";
        public const string PrivacyUrl = "https://www.nationalcarrierexchange.com/privacy.html";
        public const string HelpFAQUrl = "https://www.nationalcarrierexchange.com/faq.html";
        public const string TutorialFormatUrl = "https://www.nationalcarrierexchange.com/tutorials/mv2/{0}";

        public const string SupportEmail = "<EMAIL>";
        public const string SupportPhone = "+12132328687";
    }
}