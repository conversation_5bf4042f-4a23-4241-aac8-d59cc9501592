<Project>
  <PropertyGroup>
    <!-- Define global conditional compilation symbols -->
    <DefineConstants Condition="$(TargetFramework.Contains('-android'))">$(DefineConstants);ANDROID</DefineConstants>
    <DefineConstants Condition="$(TargetFramework.Contains('-ios'))">$(DefineConstants);IOS</DefineConstants>
    <DefineConstants Condition="$(TargetFramework.Contains('-maccatalyst'))">$(DefineConstants);MACCATALYST</DefineConstants>
    <DefineConstants Condition="$(TargetFramework.Contains('-windows'))">$(DefineConstants);WINDOWS</DefineConstants>
  </PropertyGroup>
</Project>