﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	<TargetFrameworks>net8.0;net8.0-ios;net8.0-maccatalyst</TargetFrameworks>
	<ImplicitUsings>enable</ImplicitUsings>
	<Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="Humanizer.Core" Version="2.14.1" />
    <PackageReference Include="Microsoft.AppCenter.Crashes" Version="5.0.6" />
    <PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="8.0.100" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Prism.Core" Version="9.0.537" />
    <PackageReference Include="Prism.DryIoc.Maui" Version="9.0.537" />
    <PackageReference Include="Refit" Version="8.0.0" />
    <PackageReference Include="Shiny.Locations" Version="3.3.4" />
    <PackageReference Include="System.Threading.Tasks.Extensions" Version="4.6.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NCX.Mobile.Abstractions\NCX.Mobile.Abstractions.csproj" />
    <ProjectReference Include="..\NCX.Mobile.Forms\NCX.Mobile.Forms.csproj" />
  </ItemGroup>

</Project>
