﻿using NCX.Mobile.IFTA.ViewModels;
using NCX.Mobile.IFTA.Views;
using Prism.Ioc;
using Prism.Modularity;

namespace NCX.Mobile.IFTA
{
    public class IFTAModule : IModule
    {
        public void OnInitialized(IContainerProvider containerProvider)
        {
            // Initialization logic if necessary
        }

        public void RegisterTypes(IContainerRegistry containerRegistry)
        {
            containerRegistry.RegisterForNavigation<RecordFuelStopPage, RecordFuelStopPageViewModel>();
            containerRegistry.RegisterForNavigation<RecordFuelReceiptPage, RecordFuelReceiptPageViewModel>();
        }
    }
}
