﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://schemas.microsoft.com/dotnet/2021/maui/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ncx="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             xmlns:behaviors="clr-namespace:NCX.Mobile.Behaviors;assembly=NCX.Mobile.Forms"
             xmlns:prism="http://prismlibrary.com"
             mc:Ignorable="d"
             x:Name="page"
             x:Class="NCX.Mobile.IFTA.Views.RecordFuelStopPage"
             prism:ViewModelLocator.AutowireViewModel="Automatic"
             Title="{x:Static ncx:Resources.RecordFuelStop}">
    <ContentPage.Resources>
        <ResourceDictionary>
            <Style TargetType="Label">
                <Setter Property="FontAttributes" Value="Bold" />
            </Style>
        </ResourceDictionary>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid Padding="15">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Label Text="{x:Static ncx:Resources.LocationOfRefuel}"
                   FontAttributes="Bold"
                   d:Text="Location of refuel"/>

            <Label Text="{Binding FuelStop.State}"
                   d:Text="California"
                   TextColor="Lime"
                   FontSize="Medium"
                   FontAttributes="None"
                   Grid.Row="1" />

            <Label Text="{x:Static ncx:Resources.CurrentLocation}"
                   d:Text="Current location"
                   FontAttributes="Italic"
                   TextColor="{StaticResource LightTextColor}"
                   Margin="0,0,0,7"
                   Grid.Row="2" />

            <Label Text="{x:Static ncx:Resources.Vehicle}"
                   d:Text="Vehicle"
                   Grid.Row="3" />

            <Picker Title="Select power unit"
                    ItemsSource="{Binding PowerUnits}"
                    ItemDisplayBinding="{Binding Nickname}"
                    Grid.Row="4">
                <Picker.Behaviors>
                    <behaviors:SelectedPowerUnitBehavior FuelStop="{Binding FuelStop}" />
                </Picker.Behaviors>
            </Picker>

            <Label Text="{x:Static ncx:Resources.OdometerReading}"
                   d:Text="Odometer Reading (miles)"
                   Grid.Row="5" />
            <Entry Text="{Binding FuelStop.OdometerReading}"
                   Placeholder="40000"
                   Keyboard="Numeric"
                   Grid.Row="6" />

            <Label Text="{x:Static ncx:Resources.FuelType}"
                   d:Text="Fuel Type"
                   Grid.Row="7" />
            <Picker Title="{x:Static ncx:Resources.FuelType}"
                    d:Title="Fuel Type"
                    ItemsSource="{Binding FuelTypes}"
                    ItemDisplayBinding="{Binding ., Converter={StaticResource HumanizerConverter}}"
                    SelectedItem="{Binding FuelStop.FuelType}"
                    Grid.Row="8" />

            <Label Text="{x:Static ncx:Resources.QuantityOfFuel}"
                   d:Text="Quantity of fuel (gallons)"
                   Grid.Row="9" />
            <Entry Text="{Binding FuelStop.QuantityOfFuel}"
                   Keyboard="Numeric"
                   Placeholder="35.2"
                   Grid.Row="10" />

            <Label Text="{x:Static ncx:Resources.PricePerGallon}"
                   d:Text="Price per gallon ($)"
                   Grid.Row="11" />
            <Entry Text="{Binding FuelStop.PricePerGallon}"
                   Keyboard="Numeric"
                   Placeholder="3.83"
                   Grid.Row="12" />

            <Label Text="{x:Static ncx:Resources.Total}"
                   d:Text="Total"
                   Grid.Row="13" />
            <Label Text="{Binding FuelStop.RefuelPrice, StringFormat='{0:C}'}"
                   TextColor="{StaticResource StatusCompletedColor}"
                   FontSize="Medium"
                   Margin="5,0,0,0"
                   Grid.Row="14" />

            <Button Text="{x:Static ncx:Resources.Next}"
                    d:Text="Next"
                    Command="{Binding NextCommand}"
                    Style="{StaticResource BtnFormStyle}"
                    VerticalOptions="End"
                    Margin="20,0,20,20"
                    Grid.Row="15" />
        </Grid>
    </ContentPage.Content>
</ContentPage>
