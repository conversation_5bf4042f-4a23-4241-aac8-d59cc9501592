﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://schemas.microsoft.com/dotnet/2021/maui/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ncx="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             mc:Ignorable="d"
             x:Name="page"
             x:Class="NCX.Mobile.IFTA.Views.RecordFuelReceiptPage">
    <ContentPage.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Label Text="{x:Static ncx:Resources.TakePhotoOrSelectExistingText}"
                   Margin="20,10"
                   FontSize="Medium"
                   Grid.Row="0"/>

            <Button Text="{x:Static ncx:Resources.TakePhotoOfReceiptText}"
                    Style="{StaticResource BtnSecondaryFormStyle}"
                    Command="{Binding TakePhotoCommand}"
                    Margin="40,5"
                    IsVisible="{Binding NeedsPhoto}"
                    Grid.Row="1"/>

            <Button Text="{x:Static ncx:Resources.PhotoLibraryText}"
                    Style="{StaticResource BtnSecondaryFormStyle}"
                    Command="{Binding PickPhotoCommand}"
                    Margin="40,5"
                    IsVisible="{Binding NeedsPhoto}"
                    Grid.Row="2"/>

            <Image Source="{Binding FuelStop.ReceiptPhoto, Converter={StaticResource ImageConverter}}"
                   IsVisible="{Binding NeedsPhoto, Converter={StaticResource ObjectToBoolInverseConverter}}"
                   WidthRequest="{Binding Width, Source={x:Reference page}}"
                   Margin="20"
                   Aspect="AspectFit"
                   Grid.Row="3" />

            <Button Text="{x:Static ncx:Resources.Submit}"
                    Style="{StaticResource BtnFormStyle}"
                    VerticalOptions="End"
                    Margin="40,20"
                    Command="{Binding SumbitReceiptCommand}"
                    x:Name="submitButton"
                    Grid.Row="4">
                <Button.Triggers>
                    <DataTrigger TargetType="Button"
                                 Binding="{Binding IsEnabled, Source={x:Reference submitButton}}"
                                 Value="False">
                        <Setter Property="BackgroundColor" Value="{StaticResource BtnDisabledColor}" />
                    </DataTrigger>
                </Button.Triggers>
            </Button>
        </Grid>
    </ContentPage.Content>
</ContentPage>
