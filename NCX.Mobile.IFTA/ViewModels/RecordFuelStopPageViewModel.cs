﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.AppCenter.Crashes;
using Microsoft.Maui.Devices.Sensors;
using NCX.Mobile.Models.Api;
using NCX.Mobile.Services;
using Prism.AppModel;
using Prism.Navigation;
using System;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Reactive.Linq;
using System.Threading.Tasks;

namespace NCX.Mobile.IFTA.ViewModels
{
    public class RecordFuelStopPageViewModel : ObservableObject, IPageLifecycleAware, IDestructible
    {
        private readonly INavigationService _navigationService;
        private readonly IUserService _userService;
        private readonly IUserInteractionService _userInteractionService;
        private readonly INCXApiService _apiService;

        public RecordFuelStopPageViewModel(INavigationService navigationService,
                                           IUserService userService,
                                           IUserInteractionService userInteractionService,
                                           INCXApiService apiService)
        {
            _apiService = apiService;
            _navigationService = navigationService;
            _userService = userService;
            _userInteractionService = userInteractionService;

            PowerUnits = new ObservableCollection<PowerUnit>();
            FuelStop = new FuelStop();

            NextCommand = new AsyncRelayCommand(OnNextCommandExecuted, CanNavigate);
        }

        public ObservableCollection<PowerUnit> PowerUnits { get; }

        public IEnumerable<FuelType> FuelTypes => new[] { FuelType.Diesel, FuelType.Ethanol, FuelType.MotorFuelGasoline };

        private FuelStop fuelStop;
        public FuelStop FuelStop
        {
            get => fuelStop;
            set => SetProperty(ref fuelStop, value);
        }

        public IAsyncRelayCommand NextCommand { get; }

        private bool CanNavigate()
        {
            return FuelStop != null &&
                   FuelStop.RefuelPrice > 0 &&
                   FuelStop.OdometerReading > 0 &&
                   FuelStop.PowerUnitId > 0;
        }

        async void IPageLifecycleAware.OnAppearing()
        {
            _userInteractionService.ShowLoading();

            try
            {
                var gps = await _userService.LastLocation;

                var locations = await Geocoding.Default.GetPlacemarksAsync(gps.Position.Latitude, gps.Position.Longitude);
                var location = locations.FirstOrDefault();

                if (location != null)
                {
                    FuelStop.Country = location.CountryName;
                    FuelStop.State = location.AdminArea;
                }

                var units = await _apiService.GetPowerUnits(_userService.CurrentSession.AuthToken);

                PowerUnits.Clear();
                foreach (var unit in units)
                {
                    PowerUnits.Add(unit);
                }
            }
            catch (Exception ex)
            {
                // Handle exception as needed
            }
            finally
            {
                _userInteractionService.HideLoading();
            }
        }

        void IPageLifecycleAware.OnDisappearing() { }

        private async Task OnNextCommandExecuted()
        {
            _userInteractionService.ShowLoading();
            try
            {
                var activeLoad = _userService.ActiveLoadInfo;
                var user = await _userService.GetDriverProfileAsync();
                FuelStop.DriverId = user.Id;
                FuelStop.LoadId = activeLoad?.LoadId ?? 0;

                var navigationParams = new NavigationParameters { { "fuelStop", FuelStop } };
                await _navigationService.NavigateAsync("RecordFuelReceiptPage", navigationParams);
            }
            catch (Exception ex)
            {
                Crashes.TrackError(ex);
                Console.WriteLine(ex);
            }
            finally
            {
                _userInteractionService.HideLoading();
            }
        }

        public void Destroy()
        {
            // IAsyncRelayCommand from CommunityToolkit does not require disposal.
            // No explicit cleanup needed for NextCommand.
        }
    }
}
