﻿using System.Reactive;
using System.Reactive.Linq;
using System.Threading.Tasks;
using NCX.Mobile.Models.Api;
using NCX.Mobile.Services;
using Prism.Navigation;
using ReactiveUI;

namespace NCX.Mobile.IFTA.ViewModels
{
    public class RecordFuelReceiptPageViewModel : ReactiveObject, IDestructible
    {
        private IDocumentScanner _documentScanner { get; }
        private INCXApiService _apiService { get; }
        private IUserInteractionService _interactionService { get; }
        private IUserService _userService { get; }

        public RecordFuelReceiptPageViewModel(IDocumentScanner documentScanner, INCXApiService apiService, IUserInteractionService interactionService, IUserService userService)
        {
            _apiService = apiService;
            _documentScanner = documentScanner;
            _interactionService = interactionService;
            _userService = userService;

            TakePhotoCommand = ReactiveCommand.CreateFromTask(OnTakePhotoCommandExecuted);
            PickPhotoCommand = ReactiveCommand.CreateFromTask(OnPickPhotoCommandExecuted);
            SumbitReceiptCommand = ReactiveCommand.CreateFromTask(OnSumbitReceiptCommandExecuted, this.WhenAnyValue(x => x.NeedsPhoto).Select(x => !x));
        }

        private FuelStop _fuelStop;

        public FuelStop FuelStop
        {
            get => _fuelStop;
            set => this.RaiseAndSetIfChanged(ref _fuelStop, value);
        }

        private bool _needsPhoto = true;
        public bool NeedsPhoto
        {
            get => _needsPhoto;
            set => this.RaiseAndSetIfChanged(ref _needsPhoto, value);
        }

        public ReactiveCommand<Unit, Unit> TakePhotoCommand { get; set; }

        public ReactiveCommand<Unit, Unit> PickPhotoCommand { get; set; }

        public ReactiveCommand<Unit, Unit> SumbitReceiptCommand { get; set; }

        private async Task OnTakePhotoCommandExecuted()
        {
            FuelStop.ReceiptPhoto = await _documentScanner.ScanDocumentAsync();
            NeedsPhoto = string.IsNullOrEmpty(FuelStop.ReceiptPhoto);
        }

        private async Task OnPickPhotoCommandExecuted()
        {
            FuelStop.ReceiptPhoto = await _documentScanner.PickDocumentAsync();
            NeedsPhoto = string.IsNullOrEmpty(FuelStop.ReceiptPhoto);
        }

        private async Task OnSumbitReceiptCommandExecuted()
        {
            try
            {
                _interactionService.ShowLoading();
                await _apiService.PostFuelStop(_userService.CurrentSession.AuthToken, FuelStop);
            }
            catch (System.Exception ex)
            {
                await _interactionService.ShowAlertAsync("Error", ex.Message);
            }
            finally
            {
                _interactionService.HideLoading();
            }
        }

        public void Destroy()
        {
            TakePhotoCommand.Dispose();
            TakePhotoCommand = null;

            PickPhotoCommand.Dispose();
            PickPhotoCommand = null;

            SumbitReceiptCommand.Dispose();
            SumbitReceiptCommand = null;
        }
    }
}
