﻿using NCX.Logging.Interfaces;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using NCX.Mobile.Models.Cache.PushOperations;
using Shiny.Jobs;
using System;
using System.Threading.Tasks;

namespace NCX.Mobile.Services.Impl
{
    public class PushOperationService : IPushOperationService
    {
        private readonly ILogService _logger;
        private readonly IPushOperationCacheService _pushOperationCacheService;
        private readonly IPushOperationProcessorService _pushOperationProcessorService;
        private readonly IJobManager _jobManager;

        public PushOperationService(
            ILogService logger,
            IJobManager jobManager,
            IPushOperationCacheService pushOperationCacheService,
            IPushOperationProcessorService pushOperationProcessorService)
        {
            _logger = logger;
            _jobManager = jobManager;
            _pushOperationCacheService = pushOperationCacheService;
            _pushOperationProcessorService = pushOperationProcessorService;
        }

        public async Task AddAndRunAsync(IPushOperation operation)
        {
            try
            {
                // Add Operation to the Cache
                await _pushOperationCacheService.AddAsync(operation);
                _logger.LogDebug("PushOperationService", "Added push operation to cache.");

                // Check for Operations in the Cache and Push to the API
                await _pushOperationProcessorService.StartAsync();
                _logger.LogDebug("PushOperationService", "Started push operation processor service.");

                var forceJob = new JobInfo("forceSync", typeof(ApiCacheSyncJob))
                {
                    BatteryNotLow = false,
                    DeviceCharging = false,
                    RequiredInternetAccess = InternetAccess.Any
                };

                // Schedule the job using the correct method
                _jobManager.Register(forceJob);
                _logger.LogDebug("PushOperationService", "Scheduled ApiCacheSyncJob.");

                // Run the job using the correct method
                await _jobManager.Run("forceSync");
                _logger.LogDebug("PushOperationService", "Executed ApiCacheSyncJob.");
            }
            catch (Exception ex)
            {
                _logger.LogHigh("PushOperationService", "Error in AddAndRunAsync.", ex);
                throw;
            }
        }

        public async Task ClearAsync()
        {
            try
            {
                await _pushOperationCacheService.ClearAsync();
                _logger.LogDebug("PushOperationService", "Cleared push operation cache.");
            }
            catch (Exception ex)
            {
                _logger.LogHigh("PushOperationService", "Error in ClearAsync.", ex);
                throw;
            }
        }
    }
}
