﻿using NCX.Logging.Interfaces;
using NCX.Mobile.Data;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models.Cache.PushOperations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace NCX.Mobile.Services.Impl
{
    class PushOperationCacheService : IPushOperationCacheService
    {
        readonly ILogService _logger;
        readonly IPushOperationCacheFactory _pushOperationCacheFactory;

        public PushOperationCacheService(ILogService logger, IPushOperationCacheFactory pushOperationCacheFactory)
        {
            _logger = logger;
            _pushOperationCacheFactory = pushOperationCacheFactory;
        }

        public async Task AddAsync(IPushOperation operation)
        {
            _logger.LogDebug(Constants.Log_Tag, $"Adding operation Type={operation.PushOperationType}");

            // Add operation to db
            await _pushOperationCacheFactory.Create(operation.PushOperationType).AddAsync(operation);

            _logger.LogWarn(Constants.Log_Tag, $"Operation added Id={operation.Id} Type={operation.PushOperationType}");
        }

        public Task<IEnumerable<IPushOperation>> GetAllAsync()
        {
            return GetAllAsync(Enum.GetValues(typeof(PushOperationType)).Cast<PushOperationType>());
        }

        public async Task<IEnumerable<IPushOperation>> GetAllAsync(IEnumerable<PushOperationType> pushOperationTypeList)
        {
            var operationsToReturn = new List<IPushOperation>();

            foreach (PushOperationType opType in pushOperationTypeList)
            {
                operationsToReturn.AddRange(await _pushOperationCacheFactory.Create(opType).GetAllAsync());
            }

            return operationsToReturn.OrderBy(o => o.CreatedOn).ToList();
        }

        public async Task ClearAsync()
        {
            foreach (PushOperationType opType in Enum.GetValues(typeof(PushOperationType)))
            {
                await _pushOperationCacheFactory.Create(opType).ClearAsync();
            }
        }

        public async Task DeleteAsync(IPushOperation pushOperationToDelete)
        {
            await _pushOperationCacheFactory.Create(pushOperationToDelete.PushOperationType).DeleteAsync(pushOperationToDelete);
        }
    }
}
