﻿using Microsoft.AppCenter.Crashes;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models.Cache;
using NCX.Mobile.Models.Cache.PushOperations;
using System.Threading.Tasks;

namespace NCX.Mobile.Services.Impl
{
    abstract class APIPushOperationRunner<T> : IPushOperationsRunner<T> where T : PushOperation
    {
        protected readonly INCXApiService      _apiService;
        protected readonly IUserSessionService _userSessionService;

        public APIPushOperationRunner(
            INCXApiService      ncxApiService, 
            IUserSessionService userSessionService)
        {
            _apiService = ncxApiService;
            _userSessionService = userSessionService;
        }

        public abstract Task PushAsync(T pushOperation);

        public Task PushAsync(PushOperation pushOperation)
        {
            return PushAsync((T)pushOperation);
        }
    }

    class PushLoadOperationStatusRunner : APIPushOperationRunner<UpdateLoadOperationStatusOperation>
    {
        public PushLoadOperationStatusRunner(
            INCXApiService      ncxApiService, 
            IUserSessionService userSessionService)
            : base(ncxApiService, userSessionService)
        {
        }

        public override Task PushAsync(UpdateLoadOperationStatusOperation pushOperation)
        {
            return _apiService.UpdateLoadOperationAsync(
                _userSessionService.CurrentSession.AuthToken,
                new Models.LoadOperationUpdate(
                    pushOperation.LoadOperationId    ,
                    pushOperation.LoadOperationStatus, 
                    pushOperation.CreatedOn
                    ));
        }
    }

    class PushUpdateDriverLocationOperationRunner : APIPushOperationRunner<UpdateDriverLocationOperation>
    {
        public PushUpdateDriverLocationOperationRunner(
            INCXApiService      ncxApiService,
            IUserSessionService userSessionService)
            : base(ncxApiService, userSessionService)
        {
        }

        public override Task PushAsync(UpdateDriverLocationOperation pushOperation)
        {
            return _apiService.UpdateDriverLocationAsync(
                _userSessionService.CurrentSession.AuthToken,
                new Models.DriverLocationUpdate(
                    _userSessionService.CurrentSession.User.Id,
                    pushOperation.Latitude                    ,
                    pushOperation.Longitude                   ,
                    pushOperation.CreatedOn
                    )
                );
        }
    }

    class PushUpdateLoadTrackingOperationRunner : APIPushOperationRunner<UpdateLoadTrackingOperation>
    {
        public PushUpdateLoadTrackingOperationRunner(
            INCXApiService      ncxApiService,
            IUserSessionService userSessionService)
            : base(ncxApiService, userSessionService)
        {
        }

        public override Task PushAsync(UpdateLoadTrackingOperation pushOperation)
        {
            return _apiService.PostLoadTrackingAsync(
                _userSessionService.CurrentSession.AuthToken,
                new Models.LoadTrackingUpdate()
                {
                    Latitude     = pushOperation.Latitude                                         ,
                    Longitude    = pushOperation.Longitude                                        ,
                    Speed        = pushOperation.Speed                                            ,
                    Accuracy     = pushOperation.Accuracy                                         ,
                    CreatedAt    = pushOperation.CreatedOn                                        ,
                    Haul         = new Models.Haul() { Id = pushOperation.HaulId }                ,
                    Load         = new Models.Load() { Id = pushOperation.LoadId }                ,
                    Operation    = new Models.LoadOperation { Id = pushOperation.LoadOperationId },
                    Organization = new Models.Organization { Id = pushOperation.OrganizationId }
                });
        }
    }

    class PushPODOperationRunner : APIPushOperationRunner<SendPODOperation>
    {
        public PushPODOperationRunner(
            INCXApiService ncxApiService, 
            IUserSessionService userSessionService)
            : base(ncxApiService, userSessionService)
        {
        }

        public override async Task PushAsync(SendPODOperation pushOperation)
        {
            try 
            {
                if (pushOperation == null)
                {
                    throw new ArgumentNullException(nameof(pushOperation));
                }

                var loadImageList = LoadImageListSerializer.Deserialize(pushOperation.LoadImageListSerialized);
                
                // Validate the image data
                if (loadImageList == null || !loadImageList.Any())
                {
                    throw new Exception("No valid images found in the POD data");
                }

                // Filter out invalid images and validate remaining ones
                loadImageList = loadImageList.Where(img => 
                    img != null && 
                    !string.IsNullOrEmpty(img.Data) && 
                    !string.IsNullOrEmpty(img.MimeType)).ToList();

                if (!loadImageList.Any())
                {
                    throw new Exception("No valid images remain after filtering invalid entries");
                }

                foreach (var image in loadImageList)
                {
                    if (string.IsNullOrEmpty(image.Data))
                    {
                        throw new Exception($"Image with MimeType {image.MimeType} has no data");
                    }

                    // Validate base64 string
                    try
                    {
                        var testDecode = Convert.FromBase64String(image.Data);
                        if (testDecode.Length == 0)
                        {
                            throw new Exception($"Image with MimeType {image.MimeType} has invalid base64 data (zero length)");
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"Image with MimeType {image.MimeType} has invalid base64 format: {ex.Message}");
                    }
                }

                var podInfo = new Models.SendProofOfDeliveryInfo
                {
                    LoadOperationId = pushOperation.LoadOperationId,
                    LoadImageList = loadImageList,
                };

                // Additional validation
                if (podInfo.LoadOperationId <= 0)
                {
                    throw new ArgumentException("Invalid LoadOperationId", nameof(pushOperation));
                }

                await _apiService.PostPODAsync(
                    _userSessionService.CurrentSession.AuthToken,
                    podInfo);
            }
            catch (Exception ex)
            {
                var errorProps = new Dictionary<string, string>
                {
                    { "OperationType", "PushPODOperation" },
                    { "LoadOperationId", pushOperation?.LoadOperationId.ToString() ?? "null" },
                    { "HasImageList", (pushOperation?.LoadImageListSerialized != null).ToString() },
                    { "ImageListLength", pushOperation?.LoadImageListSerialized?.Length.ToString() ?? "0" }
                };
                
                Crashes.TrackError(ex, errorProps);
                throw;
            }
        }
    }

    class PushUpdateLoadStatusRunnner : APIPushOperationRunner<UpdateLoadStatusOperation>
    {
        public PushUpdateLoadStatusRunnner(
            INCXApiService      ncxApiService, 
            IUserSessionService userSessionService)
            : base(ncxApiService, userSessionService)
        {
        }

        public override Task PushAsync(UpdateLoadStatusOperation pushOperation)
        {
            return _apiService.UpdateLoadAsync(
                _userSessionService.CurrentSession.AuthToken,
                new Models.LoadUpdate(
                    pushOperation.LoadId    ,
                    pushOperation.LoadStatus, 
                    pushOperation.CreatedOn));

        }
    }

    class PushCreateLoadDelayOperationRunner : APIPushOperationRunner<CreateLoadDelayOperation>
    {
        public PushCreateLoadDelayOperationRunner(
            INCXApiService      ncxApiService,
            IUserSessionService userSessionService)
            : base(ncxApiService, userSessionService)
        {
        }

        public override Task PushAsync(CreateLoadDelayOperation pushOperation)
        {
            return _apiService.PostLoadDelayAsync(
                _userSessionService.CurrentSession.AuthToken,
                new Models.SendLoadDelayInfo()
                {
                    Latitude     = pushOperation.Latitude                         ,
                    Longitude    = pushOperation.Longitude                        ,
                    Reason       = pushOperation.Reason                           ,
                    Haul         = new Models.Haul() { Id = pushOperation.HaulId },
                    Load         = new Models.Load() { Id = pushOperation.LoadId },
                    Organization = new Models.Organization { Id = pushOperation.OrganizationId }
                });
        }
    }
}
