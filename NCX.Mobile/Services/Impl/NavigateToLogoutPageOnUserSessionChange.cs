﻿using NCX.Mobile.Events;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using Prism.Events;
using Prism.Navigation;

namespace NCX.Mobile.Services.Impl
{
    class NavigateToLogoutPageOnUserSessionChange : IUserSessionChangeListener
    {
        readonly object s_stateChangeLock = new object();

        readonly IEventAggregator _eventAggregator;
        readonly IPlatformTools _platformTools;
        readonly IAppSettings _appSettingsService;
        readonly INavigationService _navigationService;

        bool? _isAppActive;
        bool _shouldNavigateToLoginOnNextAppResume = false;
        bool? _hasSessionCloseBecauseExpired;

        public NavigateToLogoutPageOnUserSessionChange(
            IEventAggregator eventAggregator,
            IPlatformTools platformTools,
            IAppSettings appSettingsService,
            INavigationService navigationService)
        {
            _platformTools = platformTools;
            _appSettingsService = appSettingsService;
            _eventAggregator = eventAggregator;
            _navigationService = navigationService;

            LoadStatesFromSettings();
        }

        public void OnAppStateChanged(AppState appState)
        {
            lock (s_stateChangeLock)
            {
                _isAppActive = (appState != AppState.Paused);

                if (_isAppActive == true && _shouldNavigateToLoginOnNextAppResume)
                {
                    ExecuteShowLoginCommand(_hasSessionCloseBecauseExpired.Value);

                    UpdateStates(shouldNavigateToLoginOnNextAppResume: false, hasSessionCloseBecauseExpired: null);
                }
            }
        }

        public void OnUserSessionClosed(bool reasonIsSessionExpired)
        {
            lock (s_stateChangeLock)
            {
                if (_isAppActive == true || _isAppActive == null) // Handle null case
                {
                    // App is active or state unknown, assume active
                    _platformTools.InvokeOnMainThread(async () =>
                    {
                        ExecuteShowLoginCommand(reasonIsSessionExpired);
                        // Force navigation to login page
                        await _navigationService.NavigateAsync($"/{NavigationServiceKeys.Login}");
                    });

                    UpdateStates(shouldNavigateToLoginOnNextAppResume: false, hasSessionCloseBecauseExpired: null);
                }
                else
                {
                    // App is inactive
                    UpdateStates(shouldNavigateToLoginOnNextAppResume: true, hasSessionCloseBecauseExpired: reasonIsSessionExpired);
                }
            }
        }

        void ExecuteShowLoginCommand(bool reasonIsSessionExpired)
        {
            _eventAggregator.GetEvent<ShowLoginRequestEvent>().Publish(reasonIsSessionExpired);
        }

        void UpdateStates(bool shouldNavigateToLoginOnNextAppResume, bool? hasSessionCloseBecauseExpired)
        {
            _shouldNavigateToLoginOnNextAppResume = shouldNavigateToLoginOnNextAppResume;
            _hasSessionCloseBecauseExpired = hasSessionCloseBecauseExpired;

            SaveStatesToSettings();
        }

        void LoadStatesFromSettings()
        {
            _shouldNavigateToLoginOnNextAppResume = _appSettingsService.ShouldNavigateToLoginOnNextAppResume;
            _hasSessionCloseBecauseExpired = _appSettingsService.SessionClosedBecauseExpired;
        }

        void SaveStatesToSettings()
        {
            _appSettingsService.ShouldNavigateToLoginOnNextAppResume = _shouldNavigateToLoginOnNextAppResume;
            _appSettingsService.SessionClosedBecauseExpired = _hasSessionCloseBecauseExpired;
        }
    }
}
