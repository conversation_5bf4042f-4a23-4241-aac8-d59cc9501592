﻿using NCX.Logging.Interfaces;
using NCX.Mobile.Properties;
using Newtonsoft.Json;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace NCX.Mobile.Services.Impl
{
    class AppUpdateCheckerService : IAppUpdateCheckerService
    {
        readonly IPlatformTools _platformTools;
        readonly ILogService _logger;
        readonly IUserInteractionService _userInteractionService;

        public AppUpdateCheckerService(
            IPlatformTools platformTools,
            ILogService logger,
            IUserInteractionService userInteractionService)
        {
            _platformTools = platformTools;
            _logger = logger;
            _userInteractionService = userInteractionService;
        }

        /// <summary>
        /// Checks if app update exists.
        /// </summary>
        /// <returns>True if checking for update was sucessful (it doesn't necessarily mean an update exists)</returns>
        public async Task<bool> CheckForUpdateAsync()
        {
            Version updateVersion = null;

            try
            {
                updateVersion = await GetLatestVersion();

                if (updateVersion != null && updateVersion > _platformTools.AppVersion)
                {
                    OnAppUpdateAvailable(updateVersion);
                }

                _logger.LogDebug(Helpers.Constants.Log_Tag, "Checking for latest app version completed");

                return true;
            }
            catch (Exception ex)
            {
                Microsoft.AppCenter.Crashes.Crashes.TrackError(ex);
                _logger.LogHigh(Helpers.Constants.Log_Tag, "Checking for latest app version failed", ex);

                return false;
            }
        }

        async void OnAppUpdateAvailable(Version updateVersion)
        {
            if (await _userInteractionService.ShowConfirmationAsync(
                Resources.AppUpdateAvailableMessage, Resources.AppUpdateAvailableTitle, Resources.AppUpdateButtonText, Resources.AppUpdateNotNowButtonText))
            {
                _userInteractionService.NavigateTo(_platformTools.AppStoreUri);
            }
        }

        async Task<Version> GetLatestVersion()
        {
            AppUpdateManifest m = await GetUpdateManifestAsync().ConfigureAwait(false);
            if (m is null) return null;

            switch (_platformTools.CurrentPlatform)
            {
                case TargetPlatform.Android:
                    return m.Android;
                case TargetPlatform.iOS:
                    return m.iOS;
                default:
                    throw new ArgumentException("Unexpected platform type");
            }
        }

        async Task<AppUpdateManifest> GetUpdateManifestAsync()
        {
            if (string.IsNullOrWhiteSpace(Helpers.Constants.AppUpdateManifestUri))
            {
                return null;
            }

            string json = null;
            using (var client = new HttpClient())
                json = await client.GetStringAsync(Helpers.Constants.AppUpdateManifestUri).ConfigureAwait(false);

            return JsonConvert.DeserializeObject<AppUpdateManifest>(json);
        }

        class AppUpdateManifest
        {
            public Version Android { get; set; }
            public Version iOS { get; set; }
        }
    }
}