﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using LadyBug;
using Microsoft.AppCenter.Crashes;
using NCX.Logging.Interfaces;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using NCX.Mobile.Models.Api;
using Refit;
using Newtonsoft.Json;
using JsonApiNet.Helpers;
using JsonApiNet.Resolvers;

namespace NCX.Mobile.Services.Impl
{
    class NCXApiService : INCXApiService
    {
        readonly INCXApi _ncxApi;
        readonly ILogService _logger;

        public NCXApiService(INCXApi ncxApi, ILogService logger)
        {
            _ncxApi = ncxApi;
            _logger = logger;
        }

        public Task<AuthToken> LoginAsync(LoginRequest loginRequest, string deviceOS, string deviceToken)
        {
            _logger.LogDebug(Constants.Log_Tag, $"{nameof(NCXApiService)} {nameof(LoginAsync)}, username:{loginRequest.Username}, server: {_ncxApi.Client.BaseAddress}");

            return RunTaskAsync(_ncxApi.LoginAsync(loginRequest, deviceOS, deviceToken));
        }

        public Task LogoutAsync(AuthToken auth)
        {
            _logger.LogDebug(Constants.Log_Tag, $"{nameof(NCXApiService)} {nameof(LogoutAsync)}, username:{auth.Email}, server: {_ncxApi.Client.BaseAddress}");

            return _ncxApi.LogoutAsync(auth.Email, auth.Token);
        }

        public async Task<User> GetDriverAsync(AuthToken auth, CancellationToken cancellationToken)
        {
            _logger.LogDebug(Constants.Log_Tag, $"{nameof(NCXApiService)} {nameof(GetDriverAsync)}, username:{auth.Email}, server: {_ncxApi.Client.BaseAddress}");

            var json = await RunTaskAsync(_ncxApi.GetDriverAsync(auth.Id, auth.Email, auth.Token, cancellationToken));

            return JsonApiNet.JsonApi.ResourceFromDocument<User>(json);
        }

        public Task ResetUserPasswordAsync(string email)
        {
            _logger.LogDebug(Constants.Log_Tag, $"{nameof(NCXApiService)} {nameof(ResetUserPasswordAsync)}, username:{email}, server: {_ncxApi.Client.BaseAddress}");

            return RunTaskAsync(_ncxApi.ResetUserPasswordAsync(new ResetUserPasswordRequest(email)));
        }

        public Task UpdateUserPasswordAsync(string currentPassword, string newPassword, AuthToken auth)
        {
            _logger.LogDebug(Constants.Log_Tag, $"{nameof(NCXApiService)} {nameof(UpdateUserPasswordAsync)}, username:{auth.Email}, server: {_ncxApi.Client.BaseAddress}");

            return RunTaskAsync(_ncxApi.UpdateDriverPasswordAsync(new UpdateUserPasswordRequest(currentPassword, newPassword), auth.Email, auth.Token));
        }

        public async Task<IEnumerable<Haul>> GetHaulsAsync(AuthToken auth, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug(Constants.Log_Tag, $"{nameof(NCXApiService)} {nameof(GetHaulsAsync)}, username:{auth.Email}, server: {_ncxApi.Client.BaseAddress}");

            string json = await RunTaskAsync(_ncxApi.GetHaulsAsync(auth.Id, auth.Email, auth.Token, cancellationToken));

            if (string.IsNullOrEmpty(json))
            {
                _logger.LogWarn(Constants.Log_Tag, "Received empty JSON response from GetHaulsAsync");
                return Enumerable.Empty<Haul>();
            }

            _logger.LogDebug(Constants.Log_Tag, $"Parsed Information: {json}");

            var hauls = JsonApiNet.JsonApi.ResourceFromDocument<IEnumerable<Haul>>(json, ignoreMissingRelationships: true);
            _logger.LogDebug(Constants.Log_Tag, $"Parsed {hauls.Count()} hauls from JSON response");
            
            return hauls;
        }

        // Update load operation status
        public Task UpdateLoadOperationAsync(AuthToken auth, LoadOperationUpdate loadOperationUpdate)
        {
            _logger.LogDebug(Constants.Log_Tag, $"{nameof(NCXApiService)} LoadOperationUpdate:{loadOperationUpdate}, username:{auth.Email}, server: {_ncxApi.Client.BaseAddress}");

            string json = Helpers.JsonApi.JsonAPISerializer.ResourceToJson(loadOperationUpdate, "load-operations");

            return RunTaskAsync(
                _ncxApi.UpdateLoadOperationAsync(
                    loadOperationUpdate.Id,  
                    auth.Email            ,
                    auth.Token            ,
                    json));
        }

        public Task UpdateDriverLocationAsync(AuthToken auth, DriverLocationUpdate driverLocationUpdate)
        {
            _logger.LogDebug(Constants.Log_Tag, $"{nameof(NCXApiService)} DriverLocationUpdate:{driverLocationUpdate}, username:{auth.Email}, server: {_ncxApi.Client.BaseAddress}");

            string json = Helpers.JsonApi.JsonAPISerializer.ResourceToJson(driverLocationUpdate, "drivers");

            return RunTaskAsync(
                _ncxApi.UpdateDriverLocationAsync(auth.Id,
                auth.Email,
                auth.Token,
                json));
        }

        // Send load operation tracking location
        public Task PostLoadTrackingAsync(AuthToken auth, LoadTrackingUpdate loadTracking)
        {
            _logger.LogDebug(Constants.Log_Tag, $"{nameof(NCXApiService)} {nameof(PostLoadTrackingAsync)} LoadTrackingUpdate:{loadTracking}, username:{auth.Email}, server: {_ncxApi.Client.BaseAddress}");

            string s = Helpers.JsonApi.JsonAPISerializer.ResourceToJson(loadTracking, "load-trackings");

            return RunTaskAsync(_ncxApi.PostLoadTrackingAsync(auth.Email, auth.Token, s));
        }

        public async Task PostPODAsync(AuthToken auth, SendProofOfDeliveryInfo pod)
        {
            try
            {
                if (pod == null)
                    throw new ArgumentNullException(nameof(pod));

                string s = Helpers.JsonApi.JsonAPISerializer.ResourceToJson(pod.LoadImageList, "load-images", true);
                
                _logger.LogDebug(Constants.Log_Tag, $"POD payload:\n{s}");

                await RunTaskAsync(_ncxApi.PostPODListAsync(
                    pod.LoadOperationId,
                    auth.Email,
                    auth.Token,
                    s));
            }
            catch (Exception ex)
            {
                var errorProps = new Dictionary<string, string>
                {
                    { "LoadOperationId", pod?.LoadOperationId.ToString() ?? "null" },
                    { "ImageCount", (pod?.LoadImageList?.Count() ?? 0).ToString() },
                    { "ErrorLocation", "PostPODAsync" }
                };
                
                Crashes.TrackError(ex, errorProps);
                _logger.LogHigh(Constants.Log_Tag, 
                    $"Failed to send POD for LoadOperationId={pod?.LoadOperationId}", errorProps, ex);
                throw;
            }
        }

        public Task UpdateLoadAsync(AuthToken auth, LoadUpdate loadUpdate)
        {
            _logger.LogDebug(Constants.Log_Tag, $"{nameof(NCXApiService)} LoadUpdate:{loadUpdate}, username:{auth.Email}, server: {_ncxApi.Client.BaseAddress}");

            string json = Helpers.JsonApi.JsonAPISerializer.ResourceToJson(loadUpdate, "loads");

            return RunTaskAsync(
                _ncxApi.UpdateLoadAsync(
                    loadUpdate.Id,
                    auth.Email,
                    auth.Token,
                    json));
        }

        public Task PostLoadDelayAsync(AuthToken auth, SendLoadDelayInfo info)
        {
            _logger.LogDebug(Constants.Log_Tag, $"{nameof(NCXApiService)} {nameof(PostLoadDelayAsync)}, LoadId={info.Load.Id}, Reason={info.Reason} username:{auth.Email}, server: {_ncxApi.Client.BaseAddress}");

            string s = Helpers.JsonApi.JsonAPISerializer.ResourceToJson(info, "load-delays");

            return RunTaskAsync(_ncxApi.PostLoadDelayAsync(auth.Email, auth.Token, s));
        }

        public Task UpdateUserDeviceTokenAsync(AuthToken auth, string deviceToken, string deviceOS)
        {
            _logger.LogDebug(Constants.Log_Tag, $"{nameof(NCXApiService)} UpdateUserDeviceTokenAsync:{auth.Id} {auth.Email} {auth.Token} {deviceToken} {deviceOS} server: {_ncxApi.Client.BaseAddress}");

            return RunTaskAsync(
                _ncxApi.UpdateUserDeviceTokenAsync(
                    driverId: auth.Id,
                    username: auth.Email,
                    token: auth.Token,
                    deviceToken: deviceToken,
                    deviceOS: deviceOS));
        }

        async Task<TResult> RunTaskAsync<TResult>(Task<TResult> task)
        {
            try
            {
                return await task;
            }
            catch (ApiException apiEx)
            {
                if (apiEx.StatusCode == HttpStatusCode.Unauthorized)
                {
                    _logger.LogHigh(Constants.Log_Tag, "API unauthorized call", apiEx);

                    throw new ApiUnauthorizedException(apiEx);
                }
                else
                {
                    _logger.LogHigh(Constants.Log_Tag, $"API call failed. Response Content:{apiEx.Content}", apiEx);

                    throw new ApiErrorException(apiEx, apiEx.StatusCode, apiEx.ReasonPhrase);
                }
            }
            catch (TaskCanceledException)
            {
                _logger.LogHigh(Constants.Log_Tag, "API call failed: TaskCanceledException");

                throw;
            }
            catch (Exception ex)
            {
                Microsoft.AppCenter.Crashes.Crashes.TrackError(ex);
                _logger.LogHigh(Constants.Log_Tag, "API call failed", ex);

                throw new ApiErrorException(ex);
            }
        }

        public async Task<IEnumerable<PowerUnit>> GetPowerUnits(AuthToken auth)
        {
            string json = await RunTaskAsync(_ncxApi.GetPowerUnits(auth.Email, auth.Token));

            return JsonApiNet.JsonApi.ResourceFromDocument<IEnumerable<PowerUnit>>(json);
        }

        public Task PostFuelStop(AuthToken auth, FuelStop fuelStop)
        {
            string s = Helpers.JsonApi.JsonAPISerializer.ResourceToJson(fuelStop, "fuel-stops");

            return RunTaskAsync(_ncxApi.PostLoadDelayAsync(auth.Email, auth.Token, s));
        }
    }
}
