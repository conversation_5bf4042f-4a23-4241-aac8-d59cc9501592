﻿using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using Prism.Navigation;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace NCX.Mobile.Services.Impl
{
    class ShowPopupOnNewLoadsCommand
    {
        readonly IHaulService _haulService;
        readonly ICurrentPageService _currentPageService;
        bool _isShowingPopup;

        public ShowPopupOnNewLoadsCommand(
            IHaulService haulService,
            ICurrentPageService currentPageService)
        {
            _haulService = haulService;
            _currentPageService = currentPageService;
        }

        public void Execute()
        {
            ShowPopupForNextPendingHaul();
        }

        public void OnAppStateChanged(AppState appState)
        {
            if (appState == AppState.Started)
            {
                _isShowingPopup = false;
            }
        }

        async void ShowPopupForNextPendingHaul()
        {
            try
            {
                if (_isShowingPopup ||
                    !_currentPageService.IsMainPageSet ||
                    _currentPageService.IsNewLoadPopupPageActiveAndInForeground())
                {
                    return;
                }

                _isShowingPopup = true;

                Haul nextPendingHaul = await TryGetFirstPendingHaulAsync();
                if (nextPendingHaul != null && _currentPageService.IsMainPageSet && _isShowingPopup)
                {
                    await ShowPopupForPendingHaulAsync(nextPendingHaul);
                }

                _isShowingPopup = false;
            }
            catch
            {
                throw;
            }
        }

        Task ShowPopupForPendingHaulAsync(Haul pendingHaul)
        {
            return Task.CompletedTask;
            // TODO: Refactor this navigation on Pending Haul
            //return _navigationServiceFactory().NavigateAsync(
            //    NavigationServiceKeys.NewLoadPopup,
            //    new NavigationParameters
            //    {
            //        { "haul", pendingHaul }
            //    },
            //    true,
            //    false);
        }

        async Task<Haul> TryGetFirstPendingHaulAsync()
        {
            return (await _haulService.GetDriverHaulsAsync(true))
                .FirstOrDefault(h => h.Status == HaulStatus.Pending);
        }
    }
}
