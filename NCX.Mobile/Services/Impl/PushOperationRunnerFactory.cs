﻿using NCX.Mobile.Models.Cache.PushOperations;
using System;
using System.Threading.Tasks;

namespace NCX.Mobile.Services.Impl
{
    class PushOperationRunnerFactory
    {
        Func<PushLoadOperationStatusRunner> _updateLoadOperationStatusRunnerFactory;
        Func<PushUpdateLoadStatusRunnner> _updateLoadStatusRunnerFactory;
        Func<PushUpdateDriverLocationOperationRunner> _updateDriverLocationRunnerFactory;
        Func<PushUpdateLoadTrackingOperationRunner> _updateLoadTrackingRunnerFactory;
        Func<PushPODOperationRunner> _sendPODRunnerFactory;
        Func<PushCreateLoadDelayOperationRunner> _createLoadDelayRunnerFactory;

        public PushOperationRunnerFactory(
            Func<PushLoadOperationStatusRunner> r1,
            Func<PushUpdateLoadStatusRunnner> r2,
            Func<PushUpdateDriverLocationOperationRunner> r3,
            Func<PushUpdateLoadTrackingOperationRunner> r4,
            Func<PushPODOperationRunner> r5,
            Func<PushCreateLoadDelayOperationRunner> r6)
        {
            _updateLoadOperationStatusRunnerFactory = r1;
            _updateLoadStatusRunnerFactory = r2;
            _updateDriverLocationRunnerFactory = r3;
            _updateLoadTrackingRunnerFactory = r4;
            _sendPODRunnerFactory = r5;
            _createLoadDelayRunnerFactory = r6;
        }

        public IPushOperationRunner Create(PushOperation pushOperation)
        {
            switch (pushOperation.PushOperationType)
            {
                case PushOperationType.UpdateLoadOperationStatus:
                    return _updateLoadOperationStatusRunnerFactory();
                case PushOperationType.UpdateLoadStatus:
                    return _updateLoadStatusRunnerFactory();
                case PushOperationType.UpdateDriverLocation:
                    return _updateDriverLocationRunnerFactory();
                case PushOperationType.UpdateLoadTracking:
                    return _updateLoadTrackingRunnerFactory();
                case PushOperationType.SendPOD:
                    return _sendPODRunnerFactory();
                case PushOperationType.CreateLoadDelay:
                    return _createLoadDelayRunnerFactory();
                default:
                    throw new Exception("Unknown push operation type");
            }
        }
    }

    interface IPushOperationRunner
    {
        Task PushAsync(PushOperation pushOperation);
    }

    interface IPushOperationsRunner<T> : IPushOperationRunner
    {
        Task PushAsync(T pushOperation);
    }
}
