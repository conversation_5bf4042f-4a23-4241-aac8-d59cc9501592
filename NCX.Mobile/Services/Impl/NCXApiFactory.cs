﻿using NCX.Logging.Interfaces;
using NCX.Mobile.Helpers;
using Refit;
using System.Net.Http;
using System.Net.Http.Headers;

namespace NCX.Mobile.Services.Impl
{
    /// <summary>
    ///  Used to created INCXApi instance with custom settings
    /// </summary>
    class NCXApiFactory
    {
        readonly UrlResolver _urlResolver;
        readonly ILogService _logService;

        public NCXApiFactory(UrlResolver urlResolver, ILogService logService)
        {
            _urlResolver = urlResolver;
            _logService = logService;
        }

        public INCXApi GetInstance()
        {
            var httpClientHandler = new HttpClientHandler
            {
                // With cookie being sent, the backend doesn't return 401 Unauthorized when a second signin takes place for same driver
                UseCookies = false

                //Proxy = new WebProxy(new Uri("http://*************:8888")),
                //UseProxy = true
            };

#if DEBUG
            var httpClient = new HttpClient(new HttpLoggingHandler(_logService, httpClientHandler))
#else
            var httpClient = new HttpClient(httpClientHandler)
#endif
            {
                BaseAddress = _urlResolver.BaseAddress
            };

            httpClient.DefaultRequestHeaders.CacheControl = new CacheControlHeaderValue()
            {
                NoCache = true
            };

            return RestService.For<INCXApi>(httpClient);
        }
    }
}
