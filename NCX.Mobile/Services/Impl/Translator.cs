﻿using NCX.Mobile.Properties;
using System;
using System.Globalization;
using System.Resources;

namespace NCX.Mobile.Services
{
    public class Translator : ITranslator
    {
        readonly ResourceManager _resourceManager = new ResourceManager(typeof(Resources));

        public string Translate(string resourceId, bool toUpper = false)
        {
            string translation = _resourceManager.GetString(resourceId, CultureInfo.CurrentUICulture);

            if (string.IsNullOrWhiteSpace(translation))
            {
                translation = resourceId;
            }

            translation = translation.Replace("\\n", Environment.NewLine);

            return toUpper ? translation.ToUpper() : translation;   
        }
    }
}
