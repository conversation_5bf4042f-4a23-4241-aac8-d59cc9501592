﻿using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using NCX.Mobile.Services.Impl;
using Shiny.Jobs;

namespace NCX.Mobile.Services
{
    internal class ApiCacheSyncJob : IJob
    {
        IPushOperationProcessor _processor { get; }
        DefaultChainedPushOperationErrorHandler _defaultPushOperationErrorHandler { get; }
        IUserService _userService { get; }

        public ApiCacheSyncJob(IPushOperationProcessor processor, IUserService userService, DefaultChainedPushOperationErrorHandler handler)
        {
            _processor = processor ?? throw new ArgumentNullException(nameof(processor));
            _userService = userService ?? throw new ArgumentNullException(nameof(userService));
            _defaultPushOperationErrorHandler = handler ?? throw new ArgumentNullException(nameof(handler));
            Debug.WriteLine("ApiCacheSyncJob instantiated.");
        }

        public async Task Run(JobInfo jobInfo, CancellationToken cancelToken)
        {
            Debug.WriteLine($"ApiCacheSyncJob started: {jobInfo.Identifier}");

            if (!_userService.IsLoggedIn)
            {
                Debug.WriteLine("User not logged in, exiting job.");
                return;
            }

            try
            {
                Debug.WriteLine("Starting PushOperationProcessor.RunAsync...");
                await _processor.RunAsync(_defaultPushOperationErrorHandler);
                Debug.WriteLine("PushOperationProcessor.RunAsync completed successfully.");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ApiCacheSyncJob failed: {ex}");
                Microsoft.AppCenter.Crashes.Crashes.TrackError(ex);
                throw; // Re-throw to signal WorkManager of failure
            }
        }
    }
}
