﻿using System;
using System.Threading.Tasks;
using Microsoft.Maui.ApplicationModel;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;

namespace NCX.Mobile.Services
{
    public class AppPermissionsService : IAppPermissionsService
    {
        readonly IUserInteractionService _userInteractionService;

        public AppPermissionsService(IUserInteractionService userInteractionService)
        {
            _userInteractionService = userInteractionService;
        }

        public Task<bool> CheckLocationPermissionAsync()
        {
            return CheckAndRequestPermissionAsync<Permissions.LocationWhenInUse>(
                Resources.LocationPermissionDeniedMessageText,
                Resources.LocationPermissionDeniedEnableSettingsMessageText);
        }

        public async Task<bool> CheckPODTakePhotoPermissionAsync()
        {
            bool cameraGranted = await CheckAndRequestPermissionAsync<Permissions.Camera>(
                Resources.PODCameraPermissionDeniedMessageText,
                Resources.PODCameraPermissionDeniedEnableSettingsMessageText);
            if (!cameraGranted)
                return false;

            return await CheckPODStoragePermissionAsync();
        }

        public Task<bool> CheckPODPickPhotoPermissionAsync()
        {
            return CheckPODStoragePermissionAsync();
        }

        public async void OnAppStateChanged(AppState appState)
        {
            if (appState == AppState.Started || appState == AppState.Resumed)
            {
                await CheckLocationPermissionAsync();
            }
        }

        private Task<bool> CheckPODStoragePermissionAsync()
        {
            // Using StorageRead as an example. Change to StorageWrite if needed.
            return CheckAndRequestPermissionAsync<Permissions.StorageRead>(
                Resources.PODStoragePermissionDeniedMessageText,
                Resources.PODStoragePermissionDeniedEnableSettingsMessageText);
        }

        private async Task<bool> CheckAndRequestPermissionAsync<T>(string rationaleMessage, string enableSettingsMessage)
            where T : Permissions.BasePlatformPermission, new()
        {
            PermissionStatus status = await Permissions.CheckStatusAsync<T>();
            if (status != PermissionStatus.Granted)
            {
                status = await Permissions.RequestAsync<T>();
            }

            if (status == PermissionStatus.Granted)
            {
                return true;
            }
            else
            {
                // Show user an alert with the rationale
                await _userInteractionService.ShowAlertAsync(
                    enableSettingsMessage,
                    Resources.PermissionDeniedText,
                    Resources.ShowAppSettingsText);

                // Open application settings for user to manually enable the permission
                AppInfo.ShowSettingsUI();
                return false;
            }
        }
    }
}
