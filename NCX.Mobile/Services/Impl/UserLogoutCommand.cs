﻿using NCX.Logging.Interfaces;
using NCX.Mobile.Helpers;
using NCX.Mobile.Properties;
using System;
using System.Windows.Input;

namespace NCX.Mobile.Services.Impl
{
    public class UserLogoutCommand : ICommand
    {
        readonly IUserService _userService;
        readonly IUserInteractionService _userInteractionService;
        readonly ILogService _logger;

        public event EventHandler CanExecuteChanged;

        public UserLogoutCommand(IUserService userService, IUserInteractionService userInteractionService, ILogService logService)
        {
            _userService = userService;
            _userInteractionService = userInteractionService;
            _logger = logService;
        }

        public bool CanExecute(object parameter)
        {
            return true;
        }

        public void Execute(object parameter)
        {
            Logout((bool?)parameter ?? false);
        }

        async void Logout(bool reasonIsSessionExpired = false)
        {
            if (reasonIsSessionExpired || await _userInteractionService.ShowConfirmationAsync(Resources.UserLogoutMessageText, Resources.ConfirmationText))
            {
                try
                {
                    _userInteractionService.ShowLoading();

                    await _userService.LogoutAsync(reasonIsSessionExpired);

                    _userInteractionService.HideLoading();

                    // The navigation will be handled by NavigateToLogoutPageOnUserSessionChange
                    // which is subscribed to ShowLoginRequestEvent
                }
                catch (Exception ex)
                {
                    Microsoft.AppCenter.Crashes.Crashes.TrackError(ex);
                    _logger.LogCritical(Constants.Log_Tag, "User manual logout failed", ex);
                }
            }
        }
    }
}
