﻿using System.Collections.Generic;
using Microsoft.Maui.ApplicationModel.Communication;

namespace NCX.Mobile.Services.Impl
{
    class MessagingService : IMessagingService
    {
        public bool CanMakePhoneCall => PhoneDialer.Default.IsSupported;

        public void MakePhoneCall(string number, string name = null)
        {
            if (CanMakePhoneCall)
            {
                PhoneDialer.Open(number);
            }
        }

        public bool CanSendEmail => true;

        public async void StartComposeEmail(string to)
        {
            if (CanSendEmail)
            {
                var message = new EmailMessage
                {
                    To = new List<string> { to }
                };
                await Email.ComposeAsync(message);
            }
        }
    }
}
