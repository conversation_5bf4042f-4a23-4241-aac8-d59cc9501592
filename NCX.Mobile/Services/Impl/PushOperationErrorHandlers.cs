﻿using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using LadyBug;
using NCX.Logging.Interfaces;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using NCX.Mobile.Models.Cache.PushOperations;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AppCenter.Crashes;

namespace NCX.Mobile.Services.Impl
{
    

    public class ChainedPushOperationErrorHandler : List<IChainedPushOperationErrorHandler>, IPushOperationErrorHandler
    {
        public async Task HandleAsync(IPushOperation pushOperation, Exception ex)
        {
            foreach (IChainedPushOperationErrorHandler errorHandler in this)
            {
                if (await errorHandler.HandleAsync(pushOperation, ex))
                {
                    return;
                }
            }
        }
    }

    public class DefaultChainedPushOperationErrorHandler : ChainedPushOperationErrorHandler
    {
        readonly Func<NcxApiUnauthorizedErrorHandler> _ncxApiUnauthorizedErrorHandlerFactory;
        readonly Func<NcxApiBadClientDataErrorHandler> _ncxApiBadClientDataErrorHandlerFactory;

        public DefaultChainedPushOperationErrorHandler(
            Func<NcxApiUnauthorizedErrorHandler> ncxApiUnauthorizedErrorHandlerFactory,
            Func<NcxApiBadClientDataErrorHandler> ncxApiBadClientDataErrorHandlerFactory)
        {
            _ncxApiUnauthorizedErrorHandlerFactory = ncxApiUnauthorizedErrorHandlerFactory;
            _ncxApiBadClientDataErrorHandlerFactory = ncxApiBadClientDataErrorHandlerFactory;

            AddRange(new IChainedPushOperationErrorHandler[] {
                _ncxApiUnauthorizedErrorHandlerFactory(),
                _ncxApiBadClientDataErrorHandlerFactory()
            });
        }
    }

    // if there's a 401 HTTP status -> show login dialog
    public class NcxApiUnauthorizedErrorHandler : IChainedPushOperationErrorHandler
    {
        readonly Func<UserServiceLogoutCommand> _logoutCommandFactory;

        public NcxApiUnauthorizedErrorHandler(Func<UserServiceLogoutCommand> logoutCommandFactory)
        {
            _logoutCommandFactory = logoutCommandFactory;
        }

        public Task<bool> HandleAsync(IPushOperation pushOperation, Exception ex)
        {
            var apiUnauthorizedEx = ex as ApiUnauthorizedException;
            if (apiUnauthorizedEx != null)
            {
                _logoutCommandFactory().Execute(true);

                return Task.FromResult(true);
            }
            else
            {
                return Task.FromResult(false);
            }
        }
    }

    // if there's a 4xx HTTP Status -> remove operation from queue
    public class NcxApiBadClientDataErrorHandler : IChainedPushOperationErrorHandler
    {
        readonly IPushOperationCacheService _pushOperationCacheService;
        readonly IAppSettings _settingService;
        readonly ILogService _logService;

        public NcxApiBadClientDataErrorHandler(
            IPushOperationCacheService pushOperationCacheService,
            IAppSettings settingService,
            ILogService logService)
        {
            _pushOperationCacheService = pushOperationCacheService;
            _settingService = settingService;
            _logService = logService;
        }

        public async Task<bool> HandleAsync(IPushOperation pushOperation, Exception ex)
        {
            var apiErrorEx = ex as ApiErrorException;
            if (apiErrorEx == null)
            {
                return false;
            }
            else if (apiErrorEx.HttpStatusCode != null &&
                    (int)apiErrorEx.HttpStatusCode >= 400 &&
                    (int)apiErrorEx.HttpStatusCode != 404 && // Not found
                    (int)apiErrorEx.HttpStatusCode < 500)
            {
                _logService.LogHigh(Constants.Log_Tag, $"Deleting operation Id={pushOperation.Id} Type={pushOperation.PushOperationType} because of HTTP error code={apiErrorEx.HttpStatusCode}");

                await _pushOperationCacheService.DeleteAsync(pushOperation);

                _settingService.LoadsNeedRefresh = true;

                _logService.LogHigh(Constants.Log_Tag, $"Operation Id={pushOperation.Id} Type={pushOperation.PushOperationType} deleted");

                return true;
            }
            else
            {
                return false;
            }
        }
    }

    // if there's any other error -> run action (for example, schedule the queue to be run again)
    public class RunActionPushOperationCacheErrorHandler : IChainedPushOperationErrorHandler
    {
        readonly Action _onError;

        public RunActionPushOperationCacheErrorHandler(Action a)
        {
            _onError = a;
        }

        public Task<bool> HandleAsync(IPushOperation pushOperation, Exception ex)
        {
            _onError();

            string json = "";
            try
            {

                var settings = new JsonSerializerSettings()
                {
                    Formatting = Newtonsoft.Json.Formatting.Indented
                };

                json = JsonConvert.SerializeObject(pushOperation, settings);
            }
            catch (Exception exInner)
            {
                var innerProperties = new Dictionary<string, string>
                {
                    { "Operation", "SerializePushOperation" },
                    { "OriginalError", ex.Message }
                };
                Crashes.TrackError(exInner, innerProperties);
            }

            var properties = new Dictionary<string, string>
            {
                { "Operation", "HandlePushOperationError" },
                { "PushOperationJson", json }
            };
            Crashes.TrackError(ex, properties);
            return Task.FromResult(true);
        }
    }
}
