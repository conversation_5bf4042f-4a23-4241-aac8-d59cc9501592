﻿using Microsoft.Maui.Controls;
using NCX.Mobile.Views;

namespace NCX.Mobile.Services.Impl
{
    class CurrentPageService : ICurrentPageService
    {
        public bool IsApplicationStarted => Application.Current != null;

        public bool IsMainPageSet => IsApplicationStarted && Application.Current.MainPage != null;

        public bool IsMainPageActiveAndInForeground()
        {
            if (!IsApplicationStarted || Application.Current.MainPage == null)
            {
                return false;
            }

            var currentPage = GetCurrentPage(Application.Current.MainPage);
            return currentPage?.GetType() == typeof(MainPage);
        }

        public bool IsNewLoadPopupPageActiveAndInForeground()
        {
            if (!IsApplicationStarted || Application.Current.MainPage == null)
            {
                return false;
            }

            var currentPage = GetCurrentPage(Application.Current.MainPage);
            return currentPage?.GetType() == typeof(NewLoadPopupPage);
        }

        private Page GetCurrentPage(Page page)
        {
            // Recursively get the current page from NavigationPage, TabbedPage, or FlyoutPage.
            if (page is NavigationPage navPage)
            {
                return GetCurrentPage(navPage.CurrentPage);
            }
            if (page is TabbedPage tabbedPage)
            {
                return GetCurrentPage(tabbedPage.CurrentPage);
            }
            if (page is FlyoutPage flyoutPage)
            {
                return GetCurrentPage(flyoutPage.Detail);
            }
            return page;
        }
    }
}
