﻿using System;
using NCX.Mobile.Models;
using Prism.Mvvm;
using Shiny.Locations;

namespace NCX.Mobile.Services.Impl
{
    class GeolocationImplementation : BindableBase, IGeolocation
    {
        private Position _position;
        public Position Position
        {
            get => _position;
            set => SetProperty(ref _position, value);
        }

        private double _speed;
        public double Speed
        {
            get => _speed;
            set => SetProperty(ref _speed, value);
        }

        private double _positionAccuracy;
        public double PositionAccuracy
        {
            get => _positionAccuracy;
            set => SetProperty(ref _positionAccuracy, value);
        }

        private DateTime timestamp;
        public DateTime Timestamp
        {
            get => timestamp;
            set => SetProperty(ref timestamp, value);
        }

        public GeoPosition GetLocation()
        {
            return new GeoPosition(Position.Latitude, Position.Longitude, Speed, PositionAccuracy);
        }

        public void SetLocation(GpsReading reading)
        {
            Position = reading.Position;
            Speed = reading.Speed;
            PositionAccuracy = reading.PositionAccuracy;
            Timestamp = reading.Timestamp.DateTime;
        }
    }
}
