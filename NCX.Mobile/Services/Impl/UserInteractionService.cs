﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Maui.Controls;
using Microsoft.Maui.ApplicationModel;
using NCX.Mobile.Helpers;
using NCX.Mobile.Properties;
using Prism;
using Prism.Commands;
using Prism.Common;
using Prism.Ioc;
using Prism.Mvvm;
using Prism.Navigation;

namespace NCX.Mobile.Services.Impl
{
    // Local stub for IPageAware if not provided by Prism for MAUI.
    public interface IPageAware
    {
        Page Page { get; set; }
    }

    static class PageUtilities
    {
        public static void OnNavigatedTo(View view, INavigationParameters navParams) { }
        public static void InvokeViewAndViewModelAction<T>(View view, Action<T> action) { }
        public static Task OnInitializedAsync(View view, INavigationParameters navParameters) => Task.CompletedTask;
    }

    class UserInteractionService : IUserInteractionService
    {
        private WeakReference<View> _currentBusyIndicator;
        private IContainerProvider ContainerProvider { get; }

        public UserInteractionService(IContainerExtension container)
        {
            ContainerProvider = container;
        }

        public Task ShowAlertAsync(string message, string title, string OKButtonText = null)
        {
            return Application.Current.MainPage.DisplayAlert(title, message, OKButtonText ?? Resources.CloseText);
        }

        public Task<bool> ShowConfirmationAsync(string message, string title, string OKButtonText = null, string CancelButtonText = null)
        {
            return Application.Current.MainPage.DisplayAlert(title,
                                                             message,
                                                             OKButtonText ?? Resources.OKButtonText,
                                                             CancelButtonText ?? Resources.CancelButtonText);
        }

        public Task<string> ShowActionSheetAsync(string title, string cancelButtonText, params string[] actions)
        {
            return Application.Current.MainPage.DisplayActionSheet(title,
                                                                   cancelButtonText,
                                                                   null,
                                                                   actions);
        }

        public async Task<TResult> ShowPopupAsync<TViewModel, TResult>(INavigationParameters navParameters = null)
            where TViewModel : BindableBase, INavigationResultProducer<TResult>
        {
            ContentPage currentPage = GetCurrentPage();
            View popupView = await CreateViewInstaceFromViewModelType<TViewModel>(navParameters, currentPage);

            PageUtilities.OnNavigatedTo(popupView, navParameters);

            View popupHostView = InsertPopupViewInCurrentPage(popupView, Application.Current.Resources["PopupOverlayStyle"] as Style, currentPage);

            var tcs = new TaskCompletionSource<TResult>();

            (popupView.BindingContext as INavigationResultProducer<TResult>).SetResultCommand = new DelegateCommand<TResult>(result =>
            {
                (popupHostView.Parent as Grid).Children.Remove(popupHostView);

                PageUtilities.InvokeViewAndViewModelAction<IDestructible>(popupHostView, d => d.Destroy());

                tcs.SetResult(result);
            });

            return await tcs.Task;
        }

        public void ShowLoading()
        {
            if (Application.Current.MainPage == null)
            {
                return;
            }

            HideLoading();

            _currentBusyIndicator = new WeakReference<View>(CreateBusyIndicator());
        }

        public void HideLoading()
        {
            if (_currentBusyIndicator != null && _currentBusyIndicator.TryGetTarget(out View busyIndicator))
            {
                HideLoading(busyIndicator);
                _currentBusyIndicator = null;
            }
        }

        public async void NavigateTo(Uri uri)
        {
            try
            {
                await Launcher.OpenAsync(uri);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(ex);
            }
        }

        static void HideLoading(View busyIndicator)
        {
            (busyIndicator.Parent as Grid).Children.Remove(busyIndicator);
        }

        private View CreateBusyIndicator()
        {
            var activityIndicator = new ActivityIndicator()
            {
                IsRunning = true,
                HorizontalOptions = LayoutOptions.Center,
                VerticalOptions = LayoutOptions.Center,
                Scale = DeviceInfo.Platform == DevicePlatform.iOS ? 2.5 : 1
            };
            ContentPage currentPage = GetCurrentPage();
            return InsertPopupViewInCurrentPage(activityIndicator, Application.Current.Resources["BusyIndicatorOverlayStyle"] as Style, currentPage);
        }

        private ContentPage GetCurrentPage()
        {
            if (Application.Current.MainPage == null)
            {
                return null;
            }

            Page mainPage = Application.Current.MainPage;
            ContentPage currPage = null;

            if (mainPage is ContentPage contentPage)
            {
                currPage = contentPage;
            }
            else if (mainPage is FlyoutPage flyoutPage)
            {
                if (flyoutPage.Detail is NavigationPage navPage && navPage.CurrentPage is ContentPage cp)
                {
                    currPage = cp;
                }
            }
            else if (mainPage is NavigationPage nav && nav.Navigation.NavigationStack.LastOrDefault() is ContentPage lastPage)
            {
                currPage = lastPage;
            }

            return currPage;
        }

        private View InsertPopupViewInCurrentPage(View popupView, Style overlayStyle, ContentPage currentPage)
        {
            var popupHostView = new Grid()
            {
                BackgroundColor = DeviceInfo.Platform == DevicePlatform.iOS ? Colors.Transparent : Colors.White
            };

            var overlayView = new BoxView()
            {
                Style = overlayStyle
            };

            popupHostView.Children.Add(overlayView);
            popupHostView.Children.Add(popupView);

            if (!(currentPage.Content is Grid hostGrid))
            {
                hostGrid = new Grid();
                hostGrid.Children.Add(currentPage.Content);
                currentPage.Content = hostGrid;
            }
            else
            {
                if (hostGrid.RowDefinitions.Count > 1)
                {
                    Grid.SetRowSpan(popupHostView, hostGrid.RowDefinitions.Count);
                }
                if (hostGrid.ColumnDefinitions.Count > 1)
                {
                    Grid.SetColumnSpan(popupHostView, hostGrid.ColumnDefinitions.Count);
                }
            }

            hostGrid.Children.Add(popupHostView);

            return popupHostView;
        }

        async Task<View> CreateViewInstaceFromViewModelType<TViewModel>(INavigationParameters navParameters, ContentPage currentPage) where TViewModel : BindableBase
        {
            string viewTypeName = typeof(TViewModel).FullName.Replace(".ViewModels.", ".Views.");
            viewTypeName = viewTypeName.Replace("ViewModel", "View");

            Type viewType = Type.GetType(viewTypeName);

            View view = ContainerProvider.Resolve(viewType) as View;

            var navService = ContainerProvider.Resolve<INavigationService>("NavigationService");
            if (navService is IPageAware pa)
            {
                pa.Page = currentPage;
            }

            var viewModel = ContainerProvider.Resolve<TViewModel>((typeof(INavigationService), navService));
            view.BindingContext = viewModel;

            await PageUtilities.OnInitializedAsync(view, navParameters);

            return view;
        }
    }
}
