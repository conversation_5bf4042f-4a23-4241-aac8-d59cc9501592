﻿namespace NCX.Mobile.Services.Impl
{
    public class UserServiceLogoutCommand : IUserServiceLogoutCommand
    {
        private readonly Lazy<IUserService> _userService;

        public UserServiceLogoutCommand(Lazy<IUserService> userService)
        {
            _userService = userService;
        }

        public async void Execute(bool reasonIsSessionExpired)
        {
            await _userService.Value.LogoutAsync(reasonIsSessionExpired);
        }
    }
}
