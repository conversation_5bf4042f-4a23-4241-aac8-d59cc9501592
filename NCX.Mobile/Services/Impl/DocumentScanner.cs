﻿using System;
using System.IO;
using System.Reactive.Linq;
using System.Threading.Tasks;
using Microsoft.AppCenter.Crashes;
using Microsoft.Maui.ApplicationModel;
using Microsoft.Maui.Media;
using NCX.Mobile.Helpers;
using Shiny.Locations;

namespace NCX.Mobile.Services.Impl
{
    class DocumentScanner : IDocumentScanner
    {
        private IGpsManager GpsManager { get; }

        public DocumentScanner(IGpsManager gpsManager)
        {
            GpsManager = gpsManager;
        }

        public async Task<string> ScanDocumentAsync()
        {
            try
            {
                var cameraStatus = await Permissions.CheckStatusAsync<Permissions.Camera>();
                if (cameraStatus != PermissionStatus.Granted)
                {
                    cameraStatus = await Permissions.RequestAsync<Permissions.Camera>();
                    if (cameraStatus != PermissionStatus.Granted)
                        return null;
                }

                if (!MediaPicker.Default.IsCaptureSupported)
                {
                    return null;
                }

                var options = new MediaPickerOptions
                {
                    Title = "Take Photo"
                };

                var photo = await MediaPicker.Default.CapturePhotoAsync(options);
                if (photo == null)
                    return null;

                // Add logging
                Console.WriteLine($"Photo captured: {photo.FileName}, Full Path: {photo.FullPath}");

                using var stream = await photo.OpenReadAsync();
                var result = await HandleMediaFile(stream);

                // Add logging
                Console.WriteLine($"HandleMediaFile result length: {result?.Length ?? 0}");

                return result;
            }
            catch (Exception ex)
            {
                Crashes.TrackError(ex);
                Console.WriteLine($"Error in ScanDocumentAsync: {ex}");
                return null;
            }
        }

        public async Task<string> PickDocumentAsync()
        {
            var photosStatus = await Permissions.CheckStatusAsync<Permissions.Photos>();
            if (photosStatus != PermissionStatus.Granted &&
                await Permissions.RequestAsync<Permissions.Photos>() != PermissionStatus.Granted)
                return null;

            var photo = await MediaPicker.Default.PickPhotoAsync();
            if (photo == null)
                return null;

            using var stream = await photo.OpenReadAsync();
            return await HandleMediaFile(stream);
        }

        private async Task<string> HandleMediaFile(Stream stream)
        {
            try
            {
                using var memoryStream = new MemoryStream();
                await stream.CopyToAsync(memoryStream);
                byte[] imageData = memoryStream.ToArray();
                
                if (imageData == null || imageData.Length == 0)
                {
                    Crashes.TrackError(new Exception("Image data is null or empty"));
                    return null;
                }

                // Convert to base64 string
                var base64String = Convert.ToBase64String(imageData);
                
                // Validate the base64 string
                try
                {
                    var testDecode = Convert.FromBase64String(base64String);
                    if (testDecode.Length == 0)
                    {
                        Crashes.TrackError(new Exception("Decoded base64 string is empty"));
                        return null;
                    }
                }
                catch (Exception ex)
                {
                    Crashes.TrackError(ex, new Dictionary<string, string>
                    {
                        { "error", "Base64 validation failed" }
                    });
                    return null;
                }

                return base64String;
            }
            catch (Exception ex)
            {
                Crashes.TrackError(ex);
                return null;
            }
        }

        public static byte[] ConvertToBase64(Stream stream)
        {
            using var memoryStream = new MemoryStream();
            stream.CopyTo(memoryStream);
            return memoryStream.ToArray();
        }
    }
}
