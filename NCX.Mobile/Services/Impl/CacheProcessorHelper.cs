using Microsoft.AppCenter.Crashes;
using NCX.Logging.Interfaces;
using NCX.Mobile.Models.Cache.PushOperations;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace NCX.Mobile.Services.Impl
{
    public class CacheProcessorHelper
    {
        private readonly IPushOperationProcessor _pushOperationProcessor;
        private readonly IPushOperationErrorHandler _errorHandler;
        private readonly ILogService _logger;
        
        // Keep strong references to async operations
        private readonly List<Task> _activeOperations = new List<Task>();
        private readonly SemaphoreSlim _operationLock = new SemaphoreSlim(1, 1);

        public CacheProcessorHelper(
            IPushOperationProcessor pushOperationProcessor,
            IPushOperationErrorHandler errorHandler,
            ILogService logger)
        {
            _pushOperationProcessor = pushOperationProcessor;
            _errorHandler = errorHandler;
            _logger = logger;
        }

        public bool RunCacheProcessor()
        {
            try
            {
                _logger.LogDebug("CacheProcessor", "Starting cache processor execution");
                
                // Use a cancellation token with reasonable timeout
                var cts = new CancellationTokenSource(TimeSpan.FromMinutes(5));
                
                // Start the task and maintain a reference to it
                var processorTask = RunProcessorAsync(cts.Token);
                
                // Wait for completion with timeout
                if (!processorTask.Wait(TimeSpan.FromMinutes(5)))
                {
                    _logger.LogHigh("CacheProcessor", "Cache processor timed out");
                    cts.Cancel();
                    return true; // Reschedule
                }
                
                return processorTask.Result;
            }
            catch (Exception ex)
            {
                var properties = new Dictionary<string, string>
                {
                    { "Component", "CacheProcessorHelper" },
                    { "Operation", "RunCacheProcessor" }
                };
                
                _logger.LogHigh("CacheProcessor", 
                    $"Error in cache processor: {ex.Message}", properties, ex);
                Crashes.TrackError(ex, properties);
                
                return true; // Reschedule on error
            }
        }

        private async Task<bool> RunProcessorAsync(CancellationToken cancellationToken)
        {
            try
            {
                await _operationLock.WaitAsync(cancellationToken);
                
                try
                {
                    _logger.LogDebug("CacheProcessor", "Running processor async");
                    
                    // Clean up completed operations
                    _activeOperations.RemoveAll(t => t.IsCompleted);
                    
                    // Run the processor and keep a reference to the task
                    var task = _pushOperationProcessor.RunAsync(_errorHandler);
                    _activeOperations.Add(task);
                    
                    bool result = await task;
                    _logger.LogDebug("CacheProcessor", $"Processor completed with result: {result}");
                    
                    return result;
                }
                finally
                {
                    _operationLock.Release();
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogDebug("CacheProcessor", "Operation was canceled");
                return true; // Reschedule
            }
            catch (Exception ex)
            {
                _logger.LogHigh("CacheProcessor", 
                    $"Exception in RunProcessorAsync: {ex.Message}", exception: ex);
                Crashes.TrackError(ex);
                return true; // Reschedule on error
            }
        }
    }
}