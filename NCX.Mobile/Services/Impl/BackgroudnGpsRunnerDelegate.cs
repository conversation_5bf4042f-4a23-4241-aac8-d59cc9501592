﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reactive.Linq;
using System.Threading.Tasks;
using Microsoft.AppCenter.Crashes;
using Microsoft.Maui.Storage;
using NCX.Mobile.Events;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using NCX.Mobile.Models.Cache;
using NCX.Mobile.Models.Cache.PushOperations;
using NCX.Mobile.Services.Impl;
using Prism.Events;
using Prism.Mvvm;
using Shiny;
using Shiny.Locations;
using Shiny.Logging;
using Shiny.Net;
using Shiny.Notifications;
using Newtonsoft.Json;
using NCX.Logging.Interfaces;

// Aliases to resolve ambiguities
using ShinyConnectivity = Shiny.Net.IConnectivity;
using ShinyNetworkAccess = Shiny.Net.NetworkAccess;

namespace NCX.Mobile.Services
{
    internal class BackgroudnGpsRunnerDelegate : BindableBase, IGpsDelegate
    {
        private static readonly object _UpdateToStorageLock = new object();
        private const string LastLoadPollTimeKey = "LastLoadPollTime";
        private const string LOG_TAG = "GpsDelegate";

        private readonly PushOperationRunnerFactory _factory;
        private readonly IEventAggregator _eventAggregator;
        private readonly IPushOperationService _pushOperationService;
        private readonly IUserService _userService;
        private readonly ShinyConnectivity _connectivity;
        private readonly INotificationManager _notificationManager;
        private readonly IGeolocation _geolocation;
        private readonly IUserInteractionService _userInteractionService;
        private readonly IHaulService _haulService;
        private readonly ILogService _logger;

        DateTime lastSent;

        public BackgroudnGpsRunnerDelegate(
            PushOperationRunnerFactory factory,
            IPushOperationService pushOperationService,
            IUserService userService,
            IEventAggregator eventAggregator,
            ShinyConnectivity connectivity,
            IGeolocation geolocation,
            INotificationManager notificationManager,
            IUserInteractionService userInteractionService,
            IHaulService haulService,
            ILogService logger)
        {
            _factory = factory;
            _pushOperationService = pushOperationService;
            _userService = userService;
            _eventAggregator = eventAggregator;
            _connectivity = connectivity;
            _geolocation = geolocation;
            _notificationManager = notificationManager;
            _userInteractionService = userInteractionService;
            _haulService = haulService;
            _logger = logger;

            lastSent = default(DateTime);
            if (string.IsNullOrEmpty(LastLoadPollTime))
            {
                LastLoadPollTime = DateTime.Now.ToString();
            }
        }

        private bool _isLoadingHauls;
        private DateTime? _lastNotification;
        public DateTime? LastNotification
        {
            get => _lastNotification;
            set => SetProperty(ref _lastNotification, value);
        }

        public string LastLoadPollTime
        {
            get => Preferences.Get(LastLoadPollTimeKey, "");
            set
            {
                if (LastLoadPollTime == value) return;
                Preferences.Set(LastLoadPollTimeKey, value);
                RaisePropertyChanged(nameof(LastLoadPollTime));
            }
        }

        public async Task OnReading(GpsReading reading)
        {
            if (!await ShouldHandleReadingChange(reading))
            {
                return;
            }

            PushOperation operation = null;
            try
            {
                _geolocation.SetLocation(reading);
                _eventAggregator.GetEvent<GpsReadingEvent>().Publish(reading);

                if (!_userService.IsLoggedIn)
                {
                    return;
                }
                
                if (DateTime.Now - lastSent < TimeSpan.FromSeconds(3))
                {
                    return;
                }

                var activeLoadOperationInfo = _userService.ActiveLoadInfo;
                string operationType = "None";

                if (await _userService.HasActiveLoad())
                {
                    operationType = "UpdateLoadTrackingOperation";
                    _logger.LogDebug(LOG_TAG, $"Creating {operationType} for HaulId={activeLoadOperationInfo.HaulId}, LoadId={activeLoadOperationInfo.LoadId}");
                    
                    operation = new UpdateLoadTrackingOperation(
                        GeoPosition.FromReading(reading),
                        activeLoadOperationInfo.HaulId,
                        activeLoadOperationInfo.LoadId,
                        activeLoadOperationInfo.LoadOperationId.Value,
                        activeLoadOperationInfo.OrganizationId.Value,
                        _userService.PowerUnitId);
                }
                else if (_userService.IsLoggedIn)
                {
                    operationType = "UpdateDriverLocationOperation";
                    _logger.LogDebug(LOG_TAG, $"Creating {operationType} for driver, PowerUnitId={_userService.PowerUnitId}");
                    
                    operation = new UpdateDriverLocationOperation
                    {
                        CreatedOn = DateTime.Now,
                        Latitude = reading.Position.Latitude,
                        Longitude = reading.Position.Longitude,
                        PushOperationType = PushOperationType.UpdateDriverLocation,
                        PowerUnitId = _userService.PowerUnitId
                    };
                }

                lastSent = DateTime.Now;

                if (_connectivity.Access == ShinyNetworkAccess.Internet && !DoGPS_AsOfflineCall)
                {
                    await _factory.Create(operation).PushAsync(operation);
                }
                else
                {
                    _logger.LogDebug(LOG_TAG, $"No internet or offline mode enabled, queueing {operationType} for later");
                    await _pushOperationService.AddAndRunAsync(operation);
                }
                
                // Check if it's time to poll for hauls
                if (DateTime.Now >= Convert.ToDateTime(LastLoadPollTime).AddMinutes(1))
                {
                    LastLoadPollTime = DateTime.Now.ToString();

                    try
                    {
                        _isLoadingHauls = true;
                        _logger.LogDebug(LOG_TAG, "Fetching driver hauls from server");

                        IEnumerable<Models.Haul> hauls;
                        using (await UserSessionSyncContext.AsyncLock.LockAsync())
                        {
                            hauls = await _haulService.GetDriverHaulsAsync(false);
                        }

                        _logger.LogDebug(LOG_TAG, $"Retrieved {hauls.Count()} hauls from server");
                        
                        // Process hauls data - note this appears to be unused currently
                        var LoadItemGroups = hauls.GroupBy(haul => haul.Status)
                            .OrderBy(g => g.Key == HaulStatus.En_Route || g.Key == HaulStatus.Checked_In ? 0 : 1)
                            .Select(g => new LoadItemGroup(g.OrderBy(haul => haul.CreatedAt)
                            .Select(haul => new LoadItemViewModel(haul))));

                        var LoadCount = hauls.Count();
                        var firstHaul = hauls.FirstOrDefault();
                        var IsIftaEnabled = firstHaul?.Organization.IftaReporting ?? false;
                        
                        _logger.LogDebug(LOG_TAG, $"Processed hauls: Count={LoadCount}, IFTA Enabled={IsIftaEnabled}");
                    }
                    catch (ApiUnauthorizedException ex1)
                    {
                        _logger.LogHigh(LOG_TAG, "API authorization failed during haul refresh", ex1);
                        Crashes.TrackError(ex1);
                        await _userInteractionService.ShowAlertAsync(ex1.Message, "API Auth: fail");
                    }
                    catch (DataConsistencyException ex2)
                    {
                        _logger.LogHigh(LOG_TAG, "Data consistency error during haul refresh", ex2);
                        Crashes.TrackError(ex2);
                        await _userInteractionService.ShowAlertAsync(ex2.Message, "InvalidDataFromBackendTitle");
                    }
                    catch (SQLite.SQLiteException sqlEx)
                    {
                        _logger.LogHigh(LOG_TAG, "SQLite error during haul refresh", sqlEx);
                        Crashes.TrackError(sqlEx);
                        await _userInteractionService.ShowAlertAsync("We ran into a problem refreshing your Load data. Please try again.", "Data Sync Error");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogHigh(LOG_TAG, "Unexpected error during haul refresh", ex);
                        Crashes.TrackError(ex);
                        await _userInteractionService.ShowAlertAsync(ex.Message, "GPS reading Error");
                    }
                    finally
                    {
                        _isLoadingHauls = false;
                        _logger.LogDebug(LOG_TAG, "Completed haul refresh operation");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogHigh(LOG_TAG, "Error in OnReading", ex);
                Crashes.TrackError(ex);
                Debug.WriteLine($"OnReading error: {ex.Message}");
            }
        }

        public bool DoGPS_AsOfflineCall = true;
        private DateTime DefaultDateTime = default(DateTime);

        private async Task<bool> ShouldHandleReadingChange(GpsReading reading)
        {
            if (_geolocation.Timestamp == DefaultDateTime || _geolocation.Position is null)
            {
                _logger.LogDebug(LOG_TAG, "First GPS reading or no previous position, handling required");
                return true;
            }

            bool HasBeenMoreThan3Min = LastNotification != null && DateTime.Now - LastNotification > TimeSpan.FromMinutes(3);
            var timeSinceLastReading = reading.Timestamp - _geolocation.Timestamp;
            var DistanceFromLastPoint = reading.Position.GetDistanceTo(_geolocation.Position);
            bool shouldTakeReading;
            bool hasActiveLoad = await _userService.HasActiveLoad();

            _logger.LogDebug(LOG_TAG, $"GPS evaluation: TimeSinceLastReading={timeSinceLastReading.TotalMinutes:F2}min, Distance={DistanceFromLastPoint.TotalMeters:F2}m, HasActiveLoad={hasActiveLoad}");

            if (_userService.IsActive || hasActiveLoad)
            {
                shouldTakeReading = DistanceFromLastPoint.TotalMeters > 0.1;
                if (HasBeenMoreThan3Min || (shouldTakeReading && timeSinceLastReading > TimeSpan.FromMinutes(1) && !hasActiveLoad))
                {
                    _logger.LogDebug(LOG_TAG, "Sending inactivity notification to user");
                    await SendNotification("It appears you've stopped driving. Did you want to go inactive?");
                }
                return shouldTakeReading;
            }

            shouldTakeReading = DistanceFromLastPoint.TotalMeters > 0.1 && timeSinceLastReading > TimeSpan.FromSeconds(15);
            if (HasBeenMoreThan3Min || shouldTakeReading || timeSinceLastReading > TimeSpan.FromMinutes(5))
            {
                await SendNotification("It appears you've started driving. Did you want to go active?");
            }
            return shouldTakeReading;
        }

        private async Task SendNotification(string message)
        {
            var notification = new Notification
            {
                Title = "Driver Alert",
                Message = message
            };

            await _notificationManager.Send(notification);
            LastNotification = DateTime.Now;
        }
    }
}
