﻿using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using System;

namespace NCX.Mobile.Services.Impl
{
    /// <summary>
    /// Resolves URL's using current backend environment host
    /// </summary>
    class UrlResolver
    {
        readonly IAppSettings _appSettingsService;

        public Uri BaseAddress
        {
            get
            {
                return new Uri(GetUrl(string.Empty), "/api/v1");
            }
        }

        public UrlResolver(IAppSettings appSettingsService)
        {
            _appSettingsService = appSettingsService;
        }

        public Uri GetUrl(string relativeUri)
        {
            return new Uri(new Uri(_appSettingsService.ApiHost), relativeUri);
        }
    }
}
