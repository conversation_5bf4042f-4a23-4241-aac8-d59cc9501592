﻿using System;
using LadyBug;
using Microsoft.AppCenter.Crashes;
using NCX.Mobile.Events;
using NCX.Mobile.Helpers;
using Prism.Events;

namespace NCX.Mobile.Services.Impl
{
    // Refresh hauls when a push notification is received
    class RefreshHaulsPushNotificationListener : IPushNotificationListener
    {
        readonly IEventAggregator _eventAggregator;

        public RefreshHaulsPushNotificationListener(IEventAggregator eventAggregator)
        {
            _eventAggregator = eventAggregator;
        }

        public void OnPushNotificationReceived()
        {
            _eventAggregator.GetEvent<ForceRefreshHaulsEvent>().Publish();
        }
    }
}
