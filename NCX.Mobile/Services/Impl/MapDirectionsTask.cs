﻿using System.Threading.Tasks;
using Microsoft.Maui.ApplicationModel;

namespace NCX.Mobile.Services.Impl
{
    class MapDirectionsTask : IMapDirectionsTask
    {
        public async Task<bool> NavigateToAsync(string name, double latitude, double longitude)
        {
            // Construct a URL for directions using Google Maps.
            // This URL scheme works on most platforms and devices.
            var uri = $"https://www.google.com/maps/dir/?api=1&destination={latitude},{longitude}&travelmode=driving";

            try
            {
                await Launcher.OpenAsync(uri);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
