﻿using System;
using System.Threading.Tasks;

namespace NCX.Mobile.Services.Impl
{
    class AppUpdateCheckerScheduler
    {
        readonly IAppUpdateCheckerService _appUpdateCheckerService;
        DateTime? _lastCheckedOn;
        bool _isChecking;

        public AppUpdateCheckerScheduler(IAppUpdateCheckerService appUpdateCheckerService)
        {
            _appUpdateCheckerService = appUpdateCheckerService;
        }

        public async void OnAppStateChanged(AppState state)
        {
            if (state != AppState.Paused && HasEnoughTimeElapsedSinceLastCheck() && !_isChecking)
            {
                _isChecking = true;

                try
                {
                    await CheckNowAsync();
                }
                finally
                {
                    _isChecking = false;
                }
            }
        }

        async Task CheckNowAsync()
        {
            if (await _appUpdateCheckerService.CheckForUpdateAsync())
            {
                _lastCheckedOn = DateTime.Now.ToUniversalTime();
            }
        }

        bool HasEnoughTimeElapsedSinceLastCheck()
        {
            return _lastCheckedOn == null || (DateTime.Now.ToUniversalTime() - _lastCheckedOn > TimeSpan.FromDays(1));
        }
    }
}
