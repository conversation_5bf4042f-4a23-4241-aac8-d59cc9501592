﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reactive.Subjects;
using System.Threading;
using System.Threading.Tasks;
using LadyBug;
using Microsoft.AppCenter;
using Microsoft.AppCenter.Crashes;
using Microsoft.Maui.Devices;
using Microsoft.Maui.Storage;
using NCX.Logging.Interfaces;
using NCX.Mobile.Events;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using Prism.Events;
using Shiny;
using Shiny.Jobs;
using Shiny.Locations;

namespace NCX.Mobile.Services.Impl
{
    internal class UserService : IUserService
    {
        const string IFTA_Tracking = "IFTA_ISTRAVELING_PREFERENCE";
        const string IFTA_Current_PowerUnit = "IFTA_CURRENT_POWERUNIT_PREFERENCE";

        private IEventAggregator _eventAggregator;
        private IUserSessionService _userSessionService;
        private INCXApiService _apiService;
        private ILogService _logger;
        private IAppSettings _appSettingsService;
        private IPushOperationService _pushOperationService;
        private IHaulService _haulService;
        private IGpsManager _gpsManager;
        private IJobManager _jobManager;
        private IUserSessionChangeListener _userSessionChangeListener;
        private IPushNotificationTokenCacheService _pushNotificationTokenService;

        public bool IsLoggedIn => _userSessionService.CurrentSession != null;
        private readonly Subject<bool> _isActiveSubject = new Subject<bool>();
        public bool IsActive { get; private set; }
        public IObservable<bool> WhenIsActiveChanged() => _isActiveSubject;
        public ActiveLoadInfo ActiveLoadInfo => _userSessionService.CurrentSession?.ActiveLoadOperationInfo;
        public UserSession CurrentSession => _userSessionService.CurrentSession;

        private readonly ApiErrorActionHandler _apiErrorActionHandler;

        public UserService(
            IEventAggregator eventAggregator,
            ILogService logger,
            IUserSessionService userSessionService,
            INCXApiService apiService,
            IAppSettings appSettingsService,
            IPushOperationService pushOperationService,
            IHaulService haulService,
            IGpsManager gpsManager,
            IJobManager jobManager,
            IUserSessionChangeListener userSessionChangeListener,
            IPushNotificationTokenCacheService pushNotificationTokenService,
            ApiErrorActionHandler apiErrorActionHandler)
        {
            _eventAggregator = eventAggregator;
            _logger = logger;
            _userSessionService = userSessionService;
            _apiService = apiService;
            _appSettingsService = appSettingsService;
            _pushOperationService = pushOperationService;
            _haulService = haulService;
            _gpsManager = gpsManager;
            _jobManager = jobManager;
            _userSessionChangeListener = userSessionChangeListener;
            _pushNotificationTokenService = pushNotificationTokenService;
            _apiErrorActionHandler = apiErrorActionHandler;
            SetupEventSubscriptions();

            SetTravel(Preferences.Get(IFTA_Tracking, false));
        }

        public int? PowerUnitId
        {
            get
            {
                try
                {
                    if (ActiveLoadInfo?.PowerUnitId != null)
                    {
                        PowerUnitId = ActiveLoadInfo.PowerUnitId;
                        return ActiveLoadInfo.PowerUnitId;
                    }

                    var value = Preferences.Get(IFTA_Current_PowerUnit, string.Empty);
                    if (string.IsNullOrEmpty(value))
                    {
                        return null;
                    }

                    return int.Parse(value);
                }
                catch (Exception ex)
                {
                    Crashes.TrackError(ex);
                    return null;
                }
            }
            set => Preferences.Set(IFTA_Current_PowerUnit, value?.ToString());
        }

        private void SetupEventSubscriptions()
        {
            _eventAggregator.GetEvent<ForceRefreshHaulsEvent>()
                .Subscribe(async () => await RefreshDriverHaulsFromOnlineAsync());
            _eventAggregator.GetEvent<UpdateUserDeviceTokenEvent>()
                .Subscribe(async t => await UpdateUserDeviceTokenAsync(t));
        }

        public async Task<bool> LoginAsync(string username, string password)
        {
            try
            {
                AuthToken authToken = await _apiService.LoginAsync(
                    new LoginRequest(username, password),
                    DeviceInfo.Platform.ToString().ToUpper(),
                    await _pushNotificationTokenService.GetTokenAsync());

                User loggedInUser = await _apiService.GetDriverAsync(authToken, CancellationToken.None);
                AppCenter.SetUserId(loggedInUser.Email);

                await ClearPreviousCachedOperationsAsync(loggedInUser);
                _appSettingsService.LastLoggedInUserId = loggedInUser.Id;

                _userSessionService.Save(new UserSession(loggedInUser, authToken));

                LogUserSession();

                await StartGpsListener();

                return true;
            }
            catch (ApiUnauthorizedException)
            {
                return false;
            }
            catch (Exception ex)
            {
                Crashes.TrackError(ex);
                _logger.LogHigh(Constants.Log_Tag, "Login failed", ex);
                throw;
            }
        }

        public async Task LogoutAsync(bool reasonIsSessionExpired = false)
        {
            if (!IsLoggedIn)
            {
                return;
            }

            if (!reasonIsSessionExpired)
            {
                using (await UserSessionSyncContext.AsyncLock.LockAsync())
                {
                    if (!IsLoggedIn)
                    {
                        return;
                    }

                    await LogoutAsyncInternal(reasonIsSessionExpired);
                }
            }
            else
            {
                await LogoutAsyncInternal(reasonIsSessionExpired);
            }
        }

        public async Task<User> GetDriverProfileAsync(CancellationToken cancellationToken = default)
        {
            if (!IsLoggedIn)
            {
                return null;
            }

            using (await UserSessionSyncContext.AsyncLock.LockAsync())
            {
                if (!IsLoggedIn)
                {
                    return null;
                }

                return await GetDriverProfileAsyncInternal(cancellationToken);
            }
        }

        public async Task<bool?> UpdateUserPasswordAsync(string currentPassword, string newPassword, ApiServiceExceptionHandler errorHandler)
        {
            if (!IsLoggedIn)
            {
                return null;
            }

            using (await UserSessionSyncContext.AsyncLock.LockAsync())
            {
                if (!IsLoggedIn)
                {
                    return null;
                }

                return await UpdateUserPasswordAsyncInternal(currentPassword, newPassword, errorHandler);
            }
        }

        public async Task UpdateUserDeviceTokenAsync(string newDeviceToken)
        {
            try
            {
                if (!IsLoggedIn)
                {
                    return;
                }

                using (await UserSessionSyncContext.AsyncLock.LockAsync())
                {
                    if (!IsLoggedIn)
                    {
                        return;
                    }

                    await _apiService.UpdateUserDeviceTokenAsync(
                        auth: _userSessionService.CurrentSession.AuthToken,
                        deviceToken: newDeviceToken,
                        deviceOS: DeviceInfo.Platform.ToString().ToUpper());
                }
            }
            catch (ApiUnauthorizedException)
            {
                await LogoutAsync(true);
            }
        }

        public async Task RefreshDriverHaulsFromOnlineAsync()
        {
            try
            {
                if (!IsLoggedIn)
                {
                    return;
                }

                using (await UserSessionSyncContext.AsyncLock.LockAsync())
                {
                    if (!IsLoggedIn)
                    {
                        return;
                    }

                    await _haulService.GetDriverHaulsAsync(false);
                }
            }
            catch (Exception ex)
            {
                var errorProperties = new Dictionary<string, string>
                {
                    { "Operation", "RefreshDriverHaulsFromOnlineAsync" },
                    { "User", this.CurrentSession?.User?.Name ?? "Unknown" }
                };
                Crashes.TrackError(ex, errorProperties);
                
                _apiErrorActionHandler.HandleException(ex);
            }
        }

        async Task LogoutAsyncInternal(bool reasonIsSessionExpired = false)
        {
            _logger.LogWarn(Constants.Log_Tag, $"Start user logout: reasonIsSessionExpired={reasonIsSessionExpired}");

            try
            {
                await _haulService.ClearDataOnUserLogoutAsync();
            }
            catch (Exception ex)
            {
                Crashes.TrackError(ex);
                _logger.LogHigh(Constants.Log_Tag, "Error on logging out", ex);
            }

            try
            {
                await StopGpsListener();
                _jobManager.CancelAll();
            }
            catch (Exception ex)
            {
                Crashes.TrackError(ex);
                _logger.LogHigh(Constants.Log_Tag, "Error on logging out: stopping location tracking failed", ex);
            }

            if (!reasonIsSessionExpired)
            {
                try
                {
                    await _apiService.LogoutAsync(_userSessionService.CurrentSession.AuthToken);
                }
                catch (Exception ex)
                {
                    Crashes.TrackError(ex);
                    _logger.LogHigh(Constants.Log_Tag, "Error on logging out: Logout API call failed", ex);
                }
            }

            try
            {
                _userSessionService.Save(null);
            }
            catch (Exception ex)
            {
                Crashes.TrackError(ex);
                _logger.LogHigh(Constants.Log_Tag, "Error on logging out: error clearing session", ex);
            }

            _userSessionChangeListener.OnUserSessionClosed(reasonIsSessionExpired);
            _logger.LogDebug(Constants.Log_Tag, "User logout completed");
        }

        async Task ClearPreviousCachedOperationsAsync(User loggedInUser)
        {
            if (loggedInUser.Id != _appSettingsService.LastLoggedInUserId)
            {
                await _pushOperationService.ClearAsync();
                _logger.LogDebug(Constants.Log_Tag,
                    $"Push operation cache deleted because new user is different than previous one loggedIn:{loggedInUser.Id} previousId:{_appSettingsService.LastLoggedInUserId}");
            }
        }

        async Task<User> GetDriverProfileAsyncInternal(CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogDebug(Constants.Log_Tag, "Getting driver profile");
                User user = await _apiService.GetDriverAsync(_userSessionService.CurrentSession.AuthToken, cancellationToken);
                _userSessionService.Save(new UserSession(user, _userSessionService.CurrentSession.AuthToken));
                return user;
            }
            catch (TaskCanceledException)
            {
                _logger.LogDebug(Constants.Log_Tag, "Getting driver profile was cancelled");
                return null;
            }
            catch (ApiUnauthorizedException)
            {
                _userSessionChangeListener.OnUserSessionClosed(true);
                return null;
            }
            catch (ApiErrorException)
            {
                return _userSessionService.CurrentSession.User;
            }
        }

        async Task<bool> UpdateUserPasswordAsyncInternal(string currentPassword, string newPassword, ApiServiceExceptionHandler errorHandler)
        {
            try
            {
                await _apiService.UpdateUserPasswordAsync(currentPassword, newPassword, _userSessionService.CurrentSession.AuthToken);
                return true;
            }
            catch (Exception ex)
            {
                Crashes.TrackError(ex);
                errorHandler.HandleException(ex);
                return false;
            }
        }

        void LogUserSession()
        {
            _logger.Track(Constants.Log_Tag, "logging in", new Dictionary<string, string>()
            {
                { "server", _appSettingsService.ApiHost },
                { "userid", _userSessionService.CurrentSession.User.Id.ToString() },
                { "username", _userSessionService.CurrentSession.User.Email },
                { "fullname", _userSessionService.CurrentSession.User.Name }
            });
        }

        public IObservable<GpsReading> LastLocation => _gpsManager.GetLastReading();

        public async Task StartGpsListener()
        {
            try 
            {
                if (_gpsManager.IsListening())
                {
                    await _gpsManager.StopListener();
                }
                
                // Check permissions before starting GPS listener
                var permissionService = Prism.Ioc.ContainerLocator.Container.Resolve<IAppPermissionsService>();
                bool hasPermission = await permissionService.CheckLocationPermissionAsync();
                
                if (hasPermission)
                {
                    // For Android 11+, we need to check for background location permission
                    if (DeviceInfo.Platform == DevicePlatform.Android && 
                        OperatingSystem.IsAndroidVersionAtLeast(30)) // Android 11+
                    {
                        var backgroundLocationStatus = await Permissions.CheckStatusAsync<Permissions.LocationAlways>();
                        if (backgroundLocationStatus != PermissionStatus.Granted)
                        {
                            _logger.LogWarn(Constants.Log_Tag, "Background location permission not granted");
                            
                            // Show a dialog explaining why we need background location
                            var userInteraction = Prism.Ioc.ContainerLocator.Container.Resolve<IUserInteractionService>();
                            bool shouldRequest = await userInteraction.ShowConfirmationAsync(
                                "Background Location Required", 
                                "NCX needs to access your location in the background to track your position even when the app is minimized. Please grant 'Allow all the time' permission on the next screen.",
                                "Continue", "Cancel");
                                
                            if (shouldRequest)
                            {
                                backgroundLocationStatus = await Permissions.RequestAsync<Permissions.LocationAlways>();
                                if (backgroundLocationStatus != PermissionStatus.Granted)
                                {
                                    _logger.LogWarn(Constants.Log_Tag, "Background location permission request denied");
                                    return;
                                }
                            }
                            else
                            {
                                _logger.LogWarn(Constants.Log_Tag, "User declined to request background location");
                                return;
                            }
                        }
                    }
                    
                    await _gpsManager.StartListener(GpsRequest.Realtime(background: true));
                    _logger.LogDebug(Constants.Log_Tag, "GPS listener started successfully");
                }
                else
                {
                    _logger.LogWarn(Constants.Log_Tag, "Cannot start GPS listener - location permission not granted");
                }
            }
            catch (TimeoutException ex)
            {
                _logger.LogWarn(Constants.Log_Tag, "Cannot start GPS listener - no active activity to request permissions", ex);
                // Schedule retry when app is in foreground
                MainThread.BeginInvokeOnMainThread(async () => {
                    await Task.Delay(5000); // Wait 5 seconds
                    await StartGpsListener();
                });
            }
            catch (Exception ex)
            {
                _logger.LogHigh(Constants.Log_Tag, "Error starting GPS listener", ex);
                Microsoft.AppCenter.Crashes.Crashes.TrackError(ex);
            }
        }

        public async Task StopGpsListener()
        {
            await _gpsManager.StopListener();
        }

        public async Task StartTravelAsync()
        {
            await StartGpsListener();
            SetTravel(true);
        }

        public Task StopTravelAsync()
        {
            PowerUnitId = null;
            SetTravel(false);
            return Task.CompletedTask;
        }

        private void SetTravel(bool isTraveling)
        {
            Preferences.Set(IFTA_Tracking, isTraveling);
            IsActive = isTraveling;
            _isActiveSubject.OnNext(isTraveling);
        }
    }
}
