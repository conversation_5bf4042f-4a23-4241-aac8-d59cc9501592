﻿using NCX.Mobile.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

namespace NCX.Mobile.Services
{
    public class Localize : ILocalize
    {

        private IAppSettings _appSettingsService { get; }

        public Localize(IAppSettings appSettingsService)
        {
            _appSettingsService = appSettingsService;
        }

        public string CurrentLanguageCode => CultureInfo.DefaultThreadCurrentUICulture?.TwoLetterISOLanguageName ??
            CultureInfo.CurrentCulture.TwoLetterISOLanguageName;

        public IEnumerable<Language> GetAllSupportedLanguages(Func<CultureInfo, bool> predicate = null)
        {
            return GetCultures().Select(c => new Language(UppercaseFirst(c.NativeName), c.TwoLetterISOLanguageName))
                                .OrderBy(l => l.DisplayName);
        }

        public IEnumerable<CultureInfo> GetCultures() =>
            new[]
            {
                CultureInfo.GetCultureInfo("en"),
                CultureInfo.GetCultureInfo("es"),
                //CultureInfo.GetCultureInfo("fr")
            };

        public void Initialize()
        {
            SetLocale(_appSettingsService.Language);
        }

        public void SetLanguage(string culture)
        {
            _appSettingsService.Language = culture;

            SetLocale(culture);
        }

        static string UppercaseFirst(string s)
        {
            if (string.IsNullOrEmpty(s))
            {
                return string.Empty;
            }

            return char.ToUpper(s[0]) + s.Substring(1);
        }

        protected void SetLocale(string cultureName)
        {
            var ci = CultureInfo.GetCultureInfo(cultureName);

            CultureInfo.DefaultThreadCurrentCulture = ci;
            CultureInfo.DefaultThreadCurrentUICulture = ci;
        }
    }
}
