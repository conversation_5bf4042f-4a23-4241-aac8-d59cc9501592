﻿using System.Collections.Generic;
using System.Linq;
using NCX.Mobile.Events;
using NCX.Mobile.Models;
using Prism.Events;
using Prism.Ioc;

namespace NCX.Mobile.Services.Impl
{
    public class ShowPopupOnNewLoadsService
    {
        private IAppSettings _appSettingsService { get; }
        private IHaulService _haulService { get; }
        private IContainerProvider _containerProvider { get; }
        private IEventAggregator _eventAggregator { get; }

        bool? _isAppInForeground;

        bool ShouldShowNewLoadsOnNextAppResume =>
            _appSettingsService.ShouldShowNewLoadsOnNextAppResume;

        public ShowPopupOnNewLoadsService(
            IContainerExtension containerProvider,
            IAppSettings appSettingsService,
            IEventAggregator eventAggregator,
            IHaulService haulService)
        {
            _containerProvider = containerProvider;
            _appSettingsService = appSettingsService;
            _eventAggregator = eventAggregator;
            _haulService = haulService;
        }

        public void Initialize()
        {
            _eventAggregator.GetEvent<HaulsFetchedFromOnline>().Subscribe(HaulService_HaulsFetchedFromOnline);
            _eventAggregator.GetEvent<HaulStateChangedFromViewedToPending>().Subscribe(HaulService_HaulStateChangedFromViewedToPending);
        }

        public void OnAppStateChanged(AppState appState)
        {
            _isAppInForeground = (appState != AppState.Paused);
        }

        public void OnUserSessionStarted()
        {
            ShowPopupIfFlagIsSetAndAppIsInForeground();
        }

        public void OnMainPageNavigated()
        {
            ShowPopupIfFlagIsSetAndAppIsInForeground();
        }

        void ShowPopupIfFlagIsSetAndAppIsInForeground()
        {
            // Check for pending hauls in local cache on app start
            if (_isAppInForeground == true && ShouldShowNewLoadsOnNextAppResume)
            {
                ShowPopupForNextPendingHaul();
            }
        }

        void HaulService_HaulsFetchedFromOnline(IEnumerable<Haul> hauls) =>
            ShowPopupIfPendingHaulsExistInCache(hauls);

        async void HaulService_HaulStateChangedFromViewedToPending()
        {
            ShowPopupIfPendingHaulsExistInCache(await _haulService.GetDriverHaulsAsync(true));
        }

        void ShowPopupIfPendingHaulsExistInCache(IEnumerable<Haul> cacheHauls)
        {
            _appSettingsService.ShouldShowNewLoadsOnNextAppResume = cacheHauls.Any(h => h.Status == HaulStatus.Pending);

            ShowPopupIfFlagIsSetAndAppIsInForeground();
        }


        void ShowPopupForNextPendingHaul()
        {
            _containerProvider.Resolve<ShowPopupOnNewLoadsCommand>().Execute();
        }
    }
}
