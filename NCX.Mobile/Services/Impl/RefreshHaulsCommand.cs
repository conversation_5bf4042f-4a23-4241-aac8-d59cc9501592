﻿using NCX.Mobile.Events;
using Prism.Events;

namespace NCX.Mobile.Services.Impl
{
    class RefreshHaulsCommand : IRefreshHaulsCommand
    {
        readonly IEventAggregator _eventAggregator;

        public RefreshHaulsCommand(
            IEventAggregator eventAggregator)
        {
            _eventAggregator = eventAggregator;
        }

        public void Execute()
        {
            _eventAggregator.GetEvent<ForceRefreshHaulsEvent>().Publish();
        }
    }
}
