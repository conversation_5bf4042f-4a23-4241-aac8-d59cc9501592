﻿using LadyBug;
using Microsoft.AppCenter.Crashes;
using NCX.Logging.Interfaces;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models.Cache.PushOperations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace NCX.Mobile.Services.Impl
{
    class PushOperationProcessor : IPushOperationProcessor
    {
        private readonly IPushOperationCacheService _pushOperationCacheService;
        private readonly PushOperationRunnerFactory _pushOperationRunnerFactory;
        private readonly ILogService _logger;
        
        // Use this to prevent garbage collection of async operations
        private readonly List<object> _asyncOperationReferences = new List<object>();
        private readonly SemaphoreSlim _operationLock = new SemaphoreSlim(1, 1);

        public PushOperationProcessor(
            IPushOperationCacheService pushOperationCacheService,
            PushOperationRunnerFactory pushOperationRunnerFactory,
            ILogService logger)
        {
            _pushOperationCacheService = pushOperationCacheService;
            _pushOperationRunnerFactory = pushOperationRunnerFactory;
            _logger = logger;
        }

        /// <summary>
        /// Process all pending operations
        /// </summary>
        /// <returns>true if it should be scheduled to be run again</returns>
        public async Task<bool> RunAsync(IPushOperationErrorHandler errorHandler)
        {
            await _operationLock.WaitAsync();
            
            try
            {
                _logger.LogDebug(Constants.Log_Tag, "Processing operations");

                IEnumerable<IPushOperation> pendingOperations = await _pushOperationCacheService.GetAllAsync();

                _logger.LogDebug(Constants.Log_Tag, $"Found {pendingOperations.Count()} operations");

                bool pushedData = false;
                foreach (PushOperation pushOperationToRun in pendingOperations)
                {
                    _logger.LogDebug(Constants.Log_Tag, $"Processing operation Id={pushOperationToRun.Id} Type={pushOperationToRun.PushOperationType}");
                    var msg = new Dictionary<string, string>();
                    string msgStr = "";
                    msgStr += "pushOperationToRun.Id: " + pushOperationToRun.Id.ToString();
                    msgStr += "pushOperationToRun.PushOperationType: " + pushOperationToRun.PushOperationType.ToString();

                    try
                    {
                        var runner = _pushOperationRunnerFactory.Create(pushOperationToRun);
                        
                        // Store reference to prevent GC
                        var pushTask = runner.PushAsync(pushOperationToRun);
                        _asyncOperationReferences.Add(pushTask);
                        
                        await pushTask;

                        _logger.LogDebug(Constants.Log_Tag, $"Operation Id={pushOperationToRun.Id} Type={pushOperationToRun.PushOperationType} successfully sent");

                        await _pushOperationCacheService.DeleteAsync(pushOperationToRun);
                        pushedData = true;
                    }
                    catch (Exception ex)
                    {
                        var msg2 = new Dictionary<string, string>
                        {
                            { "pushOperationToRun.Id", pushOperationToRun.Id.ToString() },
                            { "pushOperationToRun.PushOperationType", pushOperationToRun.PushOperationType.ToString() },
                            { "ErrorType", ex.GetType().FullName },
                            { "ErrorMessage", ex.Message }
                        };

                        Crashes.TrackError(ex, msg2);
                      
                        _logger.LogHigh(Constants.Log_Tag, $"Error processing operation Id={pushOperationToRun.Id} Type={pushOperationToRun.PushOperationType}", msg2, ex);

                        await errorHandler.HandleAsync(pushOperationToRun, ex);
                    }
                }

                // Clean up completed references
                CleanupCompletedReferences();
                
                return pendingOperations.Any();
            }
            catch (Exception ex)
            {
                var properties = new Dictionary<string, string>
                {
                    { "Component", "PushOperationProcessor" },
                    { "Operation", "RunAsync" }
                };
                
                _logger.LogHigh(Constants.Log_Tag, "Error in RunAsync", properties, ex);
                Crashes.TrackError(ex, properties);
                
                return true; // Reschedule on error
            }
            finally
            {
                _operationLock.Release();
            }
        }
        
        private void CleanupCompletedReferences()
        {
            try
            {
                // Remove completed Task references
                _asyncOperationReferences.RemoveAll(obj => 
                    obj is Task task && task.IsCompleted);
                
                // If the list gets too large, clear older items
                if (_asyncOperationReferences.Count > 100)
                {
                    _logger.LogDebug(Constants.Log_Tag, "Cleaning up async operation references");
                    _asyncOperationReferences.RemoveRange(0, 50);
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(Constants.Log_Tag, $"Error cleaning up references: {ex.Message}");
            }
        }
    }
}
