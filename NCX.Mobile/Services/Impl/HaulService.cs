﻿using NCX.Logging.Interfaces;
using NCX.Mobile.Data;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using NCX.Mobile.Models.Cache.PushOperations;
using Prism.Events;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NCX.Mobile.Events;
using System.Threading;
using LadyBug;
using Microsoft.AppCenter.Crashes;

namespace NCX.Mobile.Services.Impl
{
    class HaulService : IHaulService
    {
        private IHaulCache _haulCache { get; }
        private ITemporaryHaulCache _haulTemporaryCache { get; }
        private INCXApiService _apiService { get; }
        private IUserSessionService _userSessionService { get; }
        private IPushOperationService _pushOperationService { get; }
        private IUserInteractionService _userInteractionService { get; }
        private IPushOperationCacheService _pushOperationCacheService { get; }
        private ILogService _logger { get; }
        private ICurrentPageService _currPageService { get; }
        private IAppSettings _appSettingsService { get; }
        private IEventAggregator _eventAggregator { get; }

        public HaulService(
            ILogService logger,
            IHaulCache haulCache,
            ITemporaryHaulCache haulTemporaryCache,
            INCXApiService apiService,
            IUserSessionService userSessionService,
            IUserInteractionService userInteractionService,
            IPushOperationService pushOperationService,
            IPushOperationCacheService pushOperationCacheService,
            ICurrentPageService currPageService,
            IEventAggregator eventAggregator,
            IAppSettings appSettingsService)
        {
            _logger = logger;
            _haulCache = haulCache;
            _haulTemporaryCache = haulTemporaryCache;
            _apiService = apiService;
            _userSessionService = userSessionService;
            _userInteractionService = userInteractionService;
            _pushOperationService = pushOperationService;
            _pushOperationCacheService = pushOperationCacheService;
            _currPageService = currPageService;
            _eventAggregator = eventAggregator;
            _appSettingsService = appSettingsService;
        }

        /// <summary>
        /// Get driver hauls
        /// </summary>
        /// <param name="fromCache">if false, tries to get it from online</param>
        /// <returns></returns>
        public async Task<IEnumerable<Haul>> GetDriverHaulsAsync(bool fromCache, CancellationToken cancellationToken = default)
        {
            bool haulsUpdatedFromOnline = false;

            if (!fromCache)
            {
                try
                {
                    // Try get hauls from backend
                    IEnumerable<Haul> onlineHauls = await _apiService.GetHaulsAsync(_userSessionService.CurrentSession.AuthToken, cancellationToken);

                    HaulDataConsistency.EnsureConsistency(onlineHauls);

                    // Hauls retrieved from backend, save them to local cache
                    await MergeCacheHaulsWithOnlineHaulsAsync(onlineHauls);
                    _logger.LogDebug(Constants.Log_Tag, $"After merge - Online hauls count: {onlineHauls.Count()}");

                    haulsUpdatedFromOnline = true;
                }
                catch (ApiErrorException)
                {
                    // Getting online hauls failed, return hauls from cache
                    _logger.LogWarn(Constants.Log_Tag, "Unable to get online hauls, get hauls from cache");
                }
                catch (DataConsistencyException dce)
                {
                    _logger.LogCritical(Constants.Log_Tag, dce.Message);
                    await _userInteractionService.ShowAlertAsync(dce.Message, "Error");
                }
            }

            haulsUpdatedFromOnline |= await EnsureMergeCacheHaulsWithTemporaryHaulsAsync();

            IEnumerable<Haul> haulsToReturn = await _haulCache.GetActiveHaulsAsync();

            if (haulsUpdatedFromOnline)
            {
                // Update user's active load operation info based on latest hauls
                ComputeAndSaveUserActiveLoadOperationInfo(haulsToReturn);

                _eventAggregator.GetEvent<HaulsFetchedFromOnline>().Publish(haulsToReturn);
            }

            return haulsToReturn;
        }

        public async Task UpdateLoadOperationStatusAsync(Haul haul, int loadId, int loadOperationId, LoadOperationStatus newLoadOperationStatus)
        {
            Load loadToUpdate = haul.Loads.Single(load => load.Id == loadId);

            // Update load operation status
            LoadOperation loadOperationToUpdate = loadToUpdate.GetLoadOperation(loadOperationId);
            loadOperationToUpdate.Status = newLoadOperationStatus;

            // Update haul and load status
            ComputeAndUpdateHaulAndLoadStatus(haul, loadToUpdate);

            // Create and add push operation to update load operation status
            await UpdateLoadOperationStatusInCacheAsync(haul, loadId, loadOperationId, newLoadOperationStatus);
        }

        public async Task UpdateLoadToViewedStatusAsync(Haul haul)
        {
            haul.Status = HaulStatus.Viewed;

            Load load = haul.Loads.Single();
            load.Status = LoadStatus.Viewed;

            // Update load operation status in cache
            await _haulCache.AddOrUpdateHaulsAsync(new Haul[] { haul });

            await _pushOperationService.AddAndRunAsync(new UpdateLoadStatusOperation(haul.Id, load.Id, LoadStatus.Viewed));

            _eventAggregator.GetEvent<HaulStateChangedFromViewedToPending>().Publish();
        }

        public async Task ClearDataOnUserLogoutAsync()
        {
            try
            {
                await _haulCache.ClearAsync();
            }
            catch (Exception ex)
            {
                Microsoft.AppCenter.Crashes.Crashes.TrackError(ex);
                _logger.LogHigh(Constants.Log_Tag, "Error clearing haul cache", ex);
            }
        }

        public async Task AddLoadDelayAsync(Haul haul, int loadId, LoadDelay loadDelay)
        {
            haul.Loads.First(l => l.Id == loadId).Delays.Add(loadDelay);

            await _haulCache.AddOrUpdateHaulsAsync(new Haul[] { haul });
        }

        async Task MergeCacheHaulsWithOnlineHaulsAsync(IEnumerable<Haul> onlineHauls)
        {
            IEnumerable<Haul> cacheHaulList = await _haulCache.GetAllHaulsAsync();
            
            // Delete hauls that no longer exist online
            var cacheToDelete = cacheHaulList.Where(x => !onlineHauls.Any(o => o.Id == x.Id));
            foreach(var removedHaul in cacheToDelete)
            {
                await _haulCache.DeleteHaulAsync(removedHaul);
            }

            // Update all online hauls in cache immediately
            foreach (var onlineHaul in onlineHauls)
            {
                var existingHaul = cacheHaulList.FirstOrDefault(h => h.Id == onlineHaul.Id);
                if (existingHaul != null)
                {
                    // Preserve local status if not cancelled/deleted
                    if (onlineHaul.Status != HaulStatus.Canceled && onlineHaul.Status != HaulStatus.Deleted)
                    {
                        onlineHaul.Status = existingHaul.Status;
                    }
                    
                    foreach (var onlineLoad in onlineHaul.Loads)
                    {
                        var existingLoad = existingHaul.Loads.FirstOrDefault(l => l.Id == onlineLoad.Id);
                        if (existingLoad != null && onlineLoad.Status != LoadStatus.Deleted && onlineLoad.Status != LoadStatus.Canceled)
                        {
                            onlineLoad.Status = existingLoad.Status;
                        }
                    }
                }
                
                await _haulCache.AddOrUpdateHaulsAsync(new[] { onlineHaul });
            }

            _logger.LogDebug(Constants.Log_Tag, $"Updated {onlineHauls.Count()} hauls in cache");
        }

        async Task AddNewOnlineHauls(IEnumerable<Haul> onlineHauls, IEnumerable<int> cacheHaulIdList)
        {
            foreach (Haul onlineHaul in onlineHauls)
            {
                if (!cacheHaulIdList.Contains(onlineHaul.Id))
                {
                    await _haulCache.AddOrUpdateHaulsAsync(onlineHaul);
                }
            }
        }

        async Task<bool> EnsureMergeCacheHaulsWithTemporaryHaulsAsync()
        {
            if (_appSettingsService.ShouldMergeHaulCache &&
                (!_currPageService.IsApplicationStarted || _currPageService.IsMainPageActiveAndInForeground()))
            {
                _appSettingsService.ShouldMergeHaulCache = false;

                await MergeCacheHaulsWithTemporaryHaulsAsync();

                return true;
            }
            else
            {
                return false;
            }
        }

        async Task MergeCacheHaulsWithTemporaryHaulsAsync()
        {
            IEnumerable<Haul> temporaryHaulList = await _haulTemporaryCache.GetHaulsAsync();

            if (temporaryHaulList.Count() == 0)
            {
                await _haulCache.ClearAsync();

                return;
            }

            IEnumerable<Haul> cacheHaulList = await _haulCache.GetAllHaulsAsync();
            IEnumerable<int> tempHaulIdList = temporaryHaulList.Select(h => h.Id).ToList();

            // When user navigates to main page, for each haul in temporay cache (online hauls):  
            //    a. update cache haul with all information except status, delay, POD
            //    b. if online haul\load\loadoperation status is Deleted\Cancelled, update its status in local cache
            foreach (Haul tempHaul in temporaryHaulList)
            {
                Haul cacheHaul = cacheHaulList.Single(h => h.Id == tempHaul.Id);

                await MergeCacheHaulWithTemporaryHaulsAsync(cacheHaul, tempHaul);
            }

            // Delete any cache hauls not returned by backend
            foreach (Haul cacheHaul in cacheHaulList)
            {
                if (!tempHaulIdList.Contains(cacheHaul.Id))
                {
                    await _haulCache.DeleteHaulAsync(cacheHaul);
                }
            }

            // Delete temporary cache
            await _haulTemporaryCache.ClearAsync();
        }

        async Task MergeCacheHaulWithTemporaryHaulsAsync(Haul cacheHaul, Haul tempHaul)
        {
            if (tempHaul.Status != HaulStatus.Canceled && tempHaul.Status != HaulStatus.Deleted)
            {
                tempHaul.Status = cacheHaul.Status;
            }

            foreach (Load tempLoad in tempHaul.Loads)
            {
                Load cacheLoad = cacheHaul.Loads.SingleOrDefault(load => load.Id == tempLoad.Id);
                if (cacheLoad != null)
                {
                    MergeCacheLoadWithTemporaryLoad(cacheLoad, tempLoad);
                }
            }

            await _haulCache.AddOrUpdateHaulsAsync(new Haul[] { tempHaul });
        }

        void MergeCacheLoadWithTemporaryLoad(Load cacheLoad, Load tempLoad)
        {
            if (tempLoad.Status != LoadStatus.Deleted && tempLoad.Status != LoadStatus.Canceled)
            {
                tempLoad.Status = cacheLoad.Status;
            }

            tempLoad.Delays = cacheLoad.Delays;

            foreach (LoadOperation tempLoadOp in tempLoad.Operations)
            {
                LoadOperation cacheLoadOp = cacheLoad.Operations.SingleOrDefault(loadOp => loadOp.Id == tempLoadOp.Id);
                if (cacheLoadOp != null)
                {
                    MergeCacheLoadOperationWithTemporaryLoadOperation(cacheLoadOp, tempLoadOp);
                }
            }
        }

        void MergeCacheLoadOperationWithTemporaryLoadOperation(LoadOperation cacheLoadOp, LoadOperation tempLoadOp)
        {
            if (tempLoadOp.Status != LoadOperationStatus.Deleted && tempLoadOp.Status != LoadOperationStatus.Canceled)
            {
                tempLoadOp.Status = cacheLoadOp.Status;
            }
        }

        async Task UpdateLoadOperationStatusInCacheAsync(Haul haul, int loadId, int loadOperationId, LoadOperationStatus newLoadOperationStatus)
        {
            // Update load operation status in cache
            await _haulCache.AddOrUpdateHaulsAsync(new Haul[] { haul });

            // Add push operation to cache
            await _pushOperationService.AddAndRunAsync(new UpdateLoadOperationStatusOperation(haul.Id, loadId, loadOperationId, newLoadOperationStatus));

            // Update user active load info
            ComputeAndSaveUserActiveLoadOperationInfo(new Haul[] { haul });
        }

        void ComputeAndSaveUserActiveLoadOperationInfo(IEnumerable<Haul> hauls)
        {
            SaveUserActiveLoadOperationInfo(GetUserActiveLoadOperationInfoFromHauls(hauls));
        }

        void SaveUserActiveLoadOperationInfo(ActiveLoadInfo newActiveLoadOperationInfo)
        {
            if (ActiveLoadInfo.Compare(_userSessionService.CurrentSession.ActiveLoadOperationInfo, newActiveLoadOperationInfo))
            {
                return;
            }

            _userSessionService.CurrentSession.ActiveLoadOperationInfo = newActiveLoadOperationInfo;
            _userSessionService.Save(_userSessionService.CurrentSession);

            _eventAggregator.GetEvent<ActiveLoadInfoChanged>().Publish(newActiveLoadOperationInfo);

            if (newActiveLoadOperationInfo != null)
            {
                _logger.LogDebug(Constants.Log_Tag, $"Saved user active load operation info, haulId: {newActiveLoadOperationInfo.HaulId}, loadId: {newActiveLoadOperationInfo.LoadId}, loadOperationId: {newActiveLoadOperationInfo.LoadOperationId}, organizationId: {newActiveLoadOperationInfo.OrganizationId}");
            }
            else
            {
                _logger.LogDebug(Constants.Log_Tag, "Saved user active load operation info: no current active load");
            }
        }

        static ActiveLoadInfo GetUserActiveLoadOperationInfoFromHauls(IEnumerable<Haul> hauls)
        {
            foreach (Haul haul in hauls)
            {
                ActiveLoadInfo activeLoadInfo = GetUserActiveLoadInfoFromHaul(haul);

                if (activeLoadInfo != null)
                {
                    return activeLoadInfo;
                }
            }

            return null;
        }

        static ActiveLoadInfo GetUserActiveLoadInfoFromHaul(Haul haul)
        {
            Load activeLoad = haul.Loads.Single();

            if (activeLoad.IsActive)
            {
                LoadOperation activeLoadOperation = activeLoad.GetActiveLoadOperation();

                if (activeLoadOperation != null)
                {
                    return new ActiveLoadInfo(haul.Id, activeLoad.Id, activeLoadOperation.Id, haul.Organization.Id, activeLoad?.PowerUnit?.Id);
                }
                else
                {
                    return new ActiveLoadInfo(haul.Id, activeLoad.Id, null, null, activeLoad?.PowerUnit?.Id);
                }
            }

            return null;
        }

        static void ComputeAndUpdateHaulAndLoadStatus(Haul haulToUpdate, Load loadToUpdate)
        {
            loadToUpdate.Status = ComputeLoadStatusByLoadOperationsStatus(loadToUpdate);
            haulToUpdate.Status = (HaulStatus)loadToUpdate.Status;
        }

        // Update load status according to the status of the load operations
        // The bussiness logic here should be the same with the business logic on server side
        static LoadStatus ComputeLoadStatusByLoadOperationsStatus(Load load)
        {
            LoadOperation loadOperation = load.GetActiveLoadOperation();

            if (loadOperation != null)
            {
                // Active load operation exists, it's either in EnRoute or Checked-In
                return loadOperation.Status == LoadOperationStatus.En_Route ? LoadStatus.En_Route : LoadStatus.Checked_In;
            }
            // No active load operation, check if there's any remaining pending load operation
            else if (load.Operations.Any(op => op.Status == LoadOperationStatus.Pending))
            {
                // No active load operation exists but there are operations in pending
                // Driver will choose the next operation to update to En Route
                return LoadStatus.En_Route;
            }
            else
            {
                // No operation is active nor pending
                // Either all operations are completed, or some or all are deleted or some or all are cancelled
                // Note that currently driver cannot cancel or delete an operation, so it's being assumed some load operation was updated to either EnRoute or Checked-in or Completed
                //  but some of the operations were deleted or cancelled by dispatcher
                return LoadStatus.Completed;
            }
        }

        // Computes and update status of next operation
        // Returns the updated operation, if any
        static LoadOperation ComputeAndUpdateStatusOfNextActiveOperation(Load load, LoadOperation updatedLoadOperation)
        {
            if (updatedLoadOperation.Status == LoadOperationStatus.Completed)
            {
                if (updatedLoadOperation.IsPickup)
                {
                    // Check if there's any remaining pending pickups
                    if (load.Pickups.Any(pickup => pickup.Status == LoadOperationStatus.Pending))
                    {
                        // if there's a single remaining pending pickup, update it to en-route
                        return UpdateSingleRemainingPendingLoadOperationToEnRoute(load, LoadOperationType.Pickup);
                    }
                    else
                    {
                        // No pending pickups
                        // If there's a single remaining pending dropoff, update it to en-route
                        return UpdateSingleRemainingPendingLoadOperationToEnRoute(load, LoadOperationType.Dropoff);
                    }
                }
                else
                {
                    // A dropoff operation was updated
                    // If there's a single remaining pending dropoff, update it to en-route
                    return UpdateSingleRemainingPendingLoadOperationToEnRoute(load, LoadOperationType.Dropoff);
                }
            }
            else
            {
                return null;
            }
        }

        static LoadOperation UpdateSingleRemainingPendingLoadOperationToEnRoute(Load load, LoadOperationType loadOperationType)
        {
            IEnumerable<LoadOperation> loadOperationsOfType = load.Operations.Where(op => op.SourceType == loadOperationType);

            // If there's a single remaining pending pickup/dropoff, update it to en-route
            if (loadOperationsOfType.Count(op => op.Status == LoadOperationStatus.Pending) == 1)
            {
                LoadOperation loadOperationToUpdate = loadOperationsOfType.Single(op => op.Status == LoadOperationStatus.Pending);
                loadOperationToUpdate.Status = LoadOperationStatus.En_Route;

                return loadOperationToUpdate;
            }
            else
            {
                return null;
            }
        }
    }
}
