﻿using NCX.Logging.Interfaces;
using NCX.Mobile.Models;
using Newtonsoft.Json;
using System;
using Microsoft.Maui.Storage;

namespace NCX.Mobile.Services.Impl
{
    public class UserSessionService : IUserSessionService
    {
        private static readonly object _UpdateToStorageLock = new object();
        private const string SettingsKey = "UserSession";

        private readonly ILogService _logger;

        private UserSession _currentUserSession;

        public UserSession CurrentSession
        {
            get
            {
                lock (_UpdateToStorageLock)
                {
                    return _currentUserSession;
                }
            }
            private set
            {
                _currentUserSession = value;
            }
        }

        public UserSessionService(ILogService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            CurrentSession = LoadSessionFromStorage();
        }

        public void Save(UserSession session)
        {
            lock (_UpdateToStorageLock)
            {
                SaveSessionToStorage(session);
                CurrentSession = session;
            }
        }

        private UserSession LoadSessionFromStorage()
        {
            try
            {
                var json = Preferences.Get(SettingsKey, string.Empty);
                if (string.IsNullOrEmpty(json))
                {
                    _logger.LogDebug("UserSessionService", "No existing user session found in storage.");
                    return null;
                }

                var session = JsonConvert.DeserializeObject<UserSession>(json);
                _logger.LogDebug("UserSessionService", "User session loaded successfully from storage.");
                return session;
            }
            catch (JsonException jsonEx)
            {
                _logger.LogHigh("UserSessionService", "Failed to deserialize UserSession from storage.", jsonEx);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogHigh("UserSessionService", "Unexpected error while loading UserSession from storage.", ex);
                return null;
            }
        }

        private void SaveSessionToStorage(UserSession session)
        {
            if (session != null)
            {
                try
                {
                    var json = JsonConvert.SerializeObject(session);
                    Preferences.Set(SettingsKey, json);
                    _logger.LogDebug("UserSessionService", "User session saved successfully to storage.");
                }
                catch (JsonException jsonEx)
                {
                    _logger.LogHigh("UserSessionService", "Failed to serialize UserSession for storage.", jsonEx);
                }
                catch (Exception ex)
                {
                    _logger.LogHigh("UserSessionService", "Unexpected error while saving UserSession to storage.", ex);
                }
            }
            else
            {
                try
                {
                    Preferences.Remove(SettingsKey);
                    _logger.LogDebug("UserSessionService", "User session removed from storage.");
                }
                catch (Exception ex)
                {
                    _logger.LogHigh("UserSessionService", "Error while removing UserSession from storage.", ex);
                }
            }
        }
    }
}
