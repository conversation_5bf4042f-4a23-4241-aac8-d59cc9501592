﻿using NCX.Logging.Interfaces;
using NCX.Mobile.Events;
using NCX.Mobile.Models;
using NCX.Mobile.Models.Cache.PushOperations;
using Prism.Events;
using System;
using System.Threading;
using System.Threading.Tasks;
using LadyBug;
using Microsoft.AppCenter.Crashes;

namespace NCX.Mobile.Services.Impl
{
    class PushNotificationTokenCacheService : IPushNotificationTokenCacheService
    {
        private readonly IAppSettings _appSettingService;
        private readonly IPlatformTools _platformTools;
        private readonly IEventAggregator _eventAggregator;
        private readonly ILogService _logService;

        private TaskCompletionSource<string> _tcs;

        public PushNotificationTokenCacheService(
            IAppSettings appSettingService,
            IPlatformTools platformTools,
            IEventAggregator eventAggregator,
            ILogService logService)
        {
            _appSettingService = appSettingService;
            _platformTools = platformTools;
            _eventAggregator = eventAggregator;
            _logService = logService;
        }

        public async Task<string> GetTokenAsync()
        {
            // If a token is already cached, return it.
            if (!string.IsNullOrWhiteSpace(_appSettingService.PushNotificationToken))
            {
                return _appSettingService.PushNotificationToken;
            }

            // If the platform already provides a token, use it.
            if (!string.IsNullOrWhiteSpace(_platformTools.PushNotificationToken))
            {
                _appSettingService.PushNotificationToken = _platformTools.PushNotificationToken;
                return _platformTools.PushNotificationToken;
            }

            // Otherwise, set up the task completion source.
            if (_tcs == null)
            {
                _tcs = new TaskCompletionSource<string>();
            }

            // Wait for the token to be set, with a timeout to prevent indefinite waiting.
            var timeoutTask = Task.Delay(TimeSpan.FromSeconds(30));
            var completedTask = await Task.WhenAny(_tcs.Task, timeoutTask);
            if (completedTask == timeoutTask)
            {
                throw new TimeoutException("Push notification token was not received within the timeout period.");
            }

            return await _tcs.Task;
        }

        public void SaveToken(string newDeviceToken)
        {
            try
            {
                _appSettingService.PushNotificationToken = newDeviceToken;
                _eventAggregator.GetEvent<UpdateUserDeviceTokenEvent>().Publish(newDeviceToken);

                if (_tcs != null)
                {
                    _tcs.SetResult(newDeviceToken);
                    _tcs = null;
                }
            }
            catch (Exception ex)
            {
                Crashes.TrackError(ex);
                _logService.LogCritical(Helpers.Constants.Log_Tag, "Error on save token", ex);
                throw;
            }
        }
    }
}
