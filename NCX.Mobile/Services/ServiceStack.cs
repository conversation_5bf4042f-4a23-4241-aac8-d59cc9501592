﻿using System;
using DryIoc;
using NCX.Logging.AppCenter;
using NCX.Logging.Commands;
using NCX.Logging.Interfaces;
using NCX.Logging.Services;
using NCX.Mobile.Data;
using NCX.Mobile.Data.Impl;
using NCX.Mobile.Helpers;
using NCX.Mobile.Services.Impl;
using Prism.Ioc;

namespace NCX.Mobile.Services
{
    public class ServiceStack
    {
        private readonly IContainerProvider _containerProvider;
        protected IContainerRegistry ContainerRegistry { get; private set; }

        public ServiceStack(IContainerProvider containerProvider, IContainerRegistry containerRegistry)
        {
            if (containerProvider == null)
                throw new ArgumentNullException(nameof(containerProvider));
            if (containerRegistry == null)
                throw new ArgumentNullException(nameof(containerRegistry));

            _containerProvider = containerProvider;
            ContainerRegistry = containerRegistry;
        }

        public void InitializeInternal()
        {
            RegisterServiceTypes(ContainerRegistry);
            InitializeServices();
        }

        protected virtual void RegisterServiceTypes(IContainerRegistry containerRegistry)
        {
            // Log Service Registration
            containerRegistry.RegisterSingleton<ILogProcessor, LogProcessor>();
            containerRegistry.RegisterSingleton<ILogService, LogService>();
            RegisterLogCommands(containerRegistry);

            containerRegistry.RegisterSingleton<INCXApi>(factory => _containerProvider.Resolve<NCXApiFactory>().GetInstance());
            containerRegistry.RegisterSingleton<INCXApiService, NCXApiService>();
            containerRegistry.RegisterSingleton<UrlResolver>();
            containerRegistry.RegisterSingleton<IUserSessionService, UserSessionService>();
            containerRegistry.RegisterSingleton<IUserService, UserService>();
            containerRegistry.RegisterSingleton<IHaulCache, HaulCache>();
            containerRegistry.RegisterSingleton<ITemporaryHaulCache, TemporaryHaulCache>();
            containerRegistry.RegisterSingleton<IHaulService, HaulService>();
            containerRegistry.RegisterSingleton<IPushOperationCacheFactory, PushOperationCacheFactory>();
            containerRegistry.RegisterSingleton<IPushOperationCacheService, PushOperationCacheService>();
            containerRegistry.RegisterSingleton<IPushOperationProcessor, PushOperationProcessor>();
            containerRegistry.RegisterSingleton<IPushOperationService, PushOperationService>();
            containerRegistry.RegisterSingleton<IUserServiceLogoutCommand, UserServiceLogoutCommand>();
            containerRegistry.RegisterSingleton<NavigateToLogoutPageOnUserSessionChange>();
            containerRegistry.RegisterSingleton<ShowPopupOnNewLoadsService>();
            containerRegistry.RegisterSingleton<IAppPermissionsService, AppPermissionsService>();
            containerRegistry.RegisterSingleton<ITranslator, Translator>();
            containerRegistry.RegisterSingleton<IMessagingService, MessagingService>();
            containerRegistry.RegisterSingleton<IMapDirectionsTask, MapDirectionsTask>();

            containerRegistry.RegisterSingleton<PushNotificationService>();
            containerRegistry.Register<IPushNotificationListener, PushNotificationService>();
            containerRegistry.Register<IPushNotificationService, PushNotificationService>();

            containerRegistry.RegisterSingleton<IPushNotificationTokenCacheService, PushNotificationTokenCacheService>();
            containerRegistry.RegisterSingleton<RefreshHaulsPushNotificationListener>();

            containerRegistry.RegisterSingleton<ICurrentPageService, CurrentPageService>();
            containerRegistry.RegisterSingleton<ApiHostManager>();
            containerRegistry.RegisterSingleton<IRefreshHaulsCommand, RefreshHaulsCommand>();

            containerRegistry.RegisterSingleton<AppUpdateCheckerScheduler>();
        }

        protected virtual void InitializeServices()
        {
            _containerProvider.Resolve<ILocalize>().Initialize();
            RegisterPushNotificationListeners();
            _containerProvider.Resolve<ShowPopupOnNewLoadsService>().Initialize();
        }

        protected virtual void RegisterLogCommands(IContainerRegistry containerRegistry)
        {
            var logProcessor = _containerProvider.Resolve<ILogProcessor>();
#if !DEBUG
            logProcessor.RegisterCommand(new AppCenterLogCommand());
#else
            logProcessor.RegisterCommand(new ExceptionLogCommand());
            logProcessor.RegisterCommand(new DebugLogCommand());
#endif
        }

        void RegisterPushNotificationListeners()
        {
            var pushNotificationService = _containerProvider.Resolve<IPushNotificationService>();
            pushNotificationService.AddListener(_containerProvider.Resolve<RefreshHaulsPushNotificationListener>());
        }
    }
}
