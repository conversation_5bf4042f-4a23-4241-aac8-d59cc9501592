﻿using System.Collections.Generic;

namespace NCX.Mobile.Services
{
    public class PushNotificationService : IPushNotificationService, IPushNotificationListener
    {
        List<IPushNotificationListener> _listenerList = new List<IPushNotificationListener>();

        // Called on main thread
        public void OnPushNotificationReceived()
        {
            foreach (IPushNotificationListener listener in _listenerList)
            {
                listener.OnPushNotificationReceived();
            }
        }

        public void AddListener(IPushNotificationListener listener)
        {
            if(listener != this)
            {
                _listenerList.Add(listener);
            }
        }

        public void RemoveListener(IPushNotificationListener listener)
        {
            if(listener != this)
            {
                _listenerList.Remove(listener);
            }
        }
    }
}
