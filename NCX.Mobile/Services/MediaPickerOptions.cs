﻿namespace NCX.Mobile.Services
{
    public class StoreMediaOptions
    {
        public string Directory { get; set; }
        public string Name { get; set; }
    }

    public class PickMediaOptions
    {
        public int? MaxSize { get; set; }
        public int CompressionQuality { get; set; }

        /// <summary>
        /// Should the library rotate image according to received exif orientation.
        /// Set to true by default.
        /// </summary>
        public bool RotateImage { get; set; }

        public PickMediaOptions()
        {
            RotateImage = true;
        }
    }

    public class StoreCameraMediaOptions : StoreMediaOptions
    {
        public int? MaxSize { get; set; }
        public int CompressionQuality { get; set; }
        public bool SaveToAlbum { get; set; }

        /// <summary>
        /// Should the library rotate image according to received exif orientation.
        /// Set to true by default.
        /// </summary>
        public bool RotateImage { get; set; }

        public StoreCameraMediaOptions()
        {
            RotateImage = true;
        }
    }
}
