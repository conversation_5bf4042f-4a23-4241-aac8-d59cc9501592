﻿using Microsoft.Extensions.DependencyInjection;
using Shiny;
using Shiny.Jobs;
using Shiny.Locations;
using Shiny.Notifications;
using NCX.Mobile.Services;
using NCX.Mobile.Models;

namespace NCX.Mobile.Services
{
    public class ShinyStartup
    {
        public void ConfigureServices(IServiceCollection services)
        {
            // Register Shiny services
            services.Add(ServiceDescriptor.Singleton<IGpsDelegate, BackgroudnGpsRunnerDelegate>());

            // Register Jobs
            services.AddSingleton<ApiCacheSyncJob>();
            services.AddSingleton<BackgroundSyncJobInfo>();

            // Add other Shiny-related services here
        }
    }
}
