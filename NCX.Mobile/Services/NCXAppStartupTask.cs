using System.Threading.Tasks;
using Microsoft.AppCenter;
using Microsoft.AppCenter.Analytics;
using Microsoft.AppCenter.Crashes;
using NCX.Logging.Interfaces;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using NCX.Mobile.Services.Impl;
using Shiny;
using Shiny.Jobs;
using Shiny.Logging;

namespace NCX.Mobile.Services
{
    internal class NCXAppStartupTask : IShinyStartupTask
    {
        readonly ILogService _logger;
        private IUserService UserService { get; }
        private ILocalize Localize { get; }
        private IJobManager JobManager { get; }
        private ShowPopupOnNewLoadsService ShowPopupOnNewLoadsService { get; }

        public NCXAppStartupTask(IUserService userService,
                                 IHaulService haulService,
                                 ILocalize localize,
                                 IJobManager jobManager,
                                 ShowPopupOnNewLoadsService showPopupOnNewLoadsService, 
                                 IPushNotificationService pushNotificationService,
                                 IPushNotificationListener pushNotificationListener)
        {
            UserService = userService;
            Localize = localize;
            JobManager = jobManager;
            ShowPopupOnNewLoadsService = showPopupOnNewLoadsService;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Potential Code Quality Issues", "RECS0165:Asynchronous methods should return a Task instead of void", Justification = "Async Task is awaited with proper catch.")]
        public async void Start()
        {
            try
            {
               //FRED AppCenter.Start(Secrets.AppCenterSecret, typeof(Analytics), typeof(Crashes));
                Localize.Initialize();
                ShowPopupOnNewLoadsService.Initialize();

                await StartAsync();
            }
            catch (System.Exception ex)
            {
                _logger.LogHigh("NCXAppStartupTask", "Error System Exception", ex);
            }
        }

        async Task StartAsync()
        {
            if (await JobManager.RequestAccess() == AccessState.Available)
            {
                var name = BackgroundSyncJobInfo.Name;
                JobManager.Register(new JobInfo(name, typeof(ApiCacheSyncJob))
                {
                    BatteryNotLow = true,
                    RequiredInternetAccess = InternetAccess.Any
                });

                //await JobManager.Schedule(new BackgroundSyncJobInfo());

                await JobManager.Run(name);
            }

            using (await UserSessionSyncContext.AsyncLock.LockAsync())
            {
                if (!UserService.IsLoggedIn)
                {
                    return;
                }

                await UserService.StartGpsListener();

                ShowPopupOnNewLoadsService.OnUserSessionStarted();
            }
        }
    }
}
