﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             xmlns:nav="clr-namespace:Prism.Navigation.Xaml;assembly=Prism.Maui"
             xmlns:abstractions="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             xmlns:behaviours="clr-namespace:NCX.Mobile.Behaviors;assembly=NCX.Mobile.Forms"
             xmlns:controls="clr-namespace:NCX.Mobile.Controls;assembly=NCX.Mobile.Forms"
             x:Class="NCX.Mobile.MainPage"
             x:Name="page"
             prism:ViewModelLocator.AutowireViewModel="Automatic"
             Style="{StaticResource PageStyle}"
             Title="{x:Static abstractions:Resources.LoadListPageHeader}"
             BackgroundColor="#F7F7F7">

    <ContentPage.ToolbarItems>
        <ToolbarItem Text="{x:Static abstractions:Resources.RefreshBtnText}"
                     Order="Primary"
                     Command="{Binding RefreshCommand}" />
    </ContentPage.ToolbarItems>

    <StackLayout>
        <!-- Network Connectivity Alert -->
        <ContentView IsVisible="{Binding IsNetworkConnectivityDown}"
                     VerticalOptions="Start"
                     Padding="0,7"
                     BackgroundColor="#F9E831">
            <Label Text="{x:Static abstractions:Resources.DataConnectivityIssue}"
                   TextColor="Black"
                   VerticalOptions="Center"
                   Margin="10,0" />
        </ContentView>

        <!-- Start Travel Button -->
        <Button Text="{x:Static abstractions:Resources.StartTravel}"
                Style="{StaticResource BtnFormStyle}"
                Command="{Binding ToggleTravelCommand}"
                IsVisible="{Binding IftaEnabled}"
                Margin="20,10,20,0">
            <Button.Triggers>
                <DataTrigger TargetType="Button" Binding="{Binding IsActive}" Value="True">
                    <Setter Property="BackgroundColor" Value="{StaticResource SecondaryColor}" />
                    <Setter Property="Text" Value="{x:Static abstractions:Resources.StopTravel}" />
                </DataTrigger>
            </Button.Triggers>
        </Button>

        <!-- Dashboard Header -->
        <StackLayout Orientation="Horizontal"
                     VerticalOptions="Start"
                     Padding="14">
            <controls:DashboardClock x:Name="_dashboardClock"
                                 VerticalOptions="Center"
                                 HorizontalOptions="FillAndExpand"
                                 prism:ViewModelLocator.AutowireViewModel="Automatic"/>
            <Frame HasShadow="True">
                <StackLayout WidthRequest="80">
                    <Label Text="{x:Static abstractions:Resources.Loads}"
                           TextColor="{StaticResource LightTextColor}"
                           Margin="0,7"
                           HorizontalOptions="Center"
                           FontSize="14" />
                    <BoxView Style="{StaticResource SeparatorStyle}" />
                    <Label Text="{Binding LoadCount}"
                           HorizontalOptions="Center"
                           TextColor="#D0021B"
                           FontSize="34"
                           FontAttributes="Bold" />
                </StackLayout>
            </Frame>
        </StackLayout>

        <BoxView Style="{StaticResource SeparatorStyle}"
                 VerticalOptions="Start" />

        <!-- Haul List -->
        <Grid VerticalOptions="FillAndExpand">
            <ListView ItemsSource="{Binding LoadItemGroups}"
                      IsVisible="{Binding LoadCount, Converter={StaticResource ObjectToBoolConverter}}"
                      HasUnevenRows="True"
                      VerticalOptions="FillAndExpand"
                      SeparatorColor="{StaticResource SeparatorColor}"
                      IsGroupingEnabled="True"
                      Margin="14,0"
                      BackgroundColor="Transparent">
                <ListView.GroupHeaderTemplate>
                    <DataTemplate>
                        <ViewCell>
                            <Label Text="{Binding ., Converter={StaticResource LoadGroupHeaderConverter}}"
                                   FontSize="22"
                                   Margin="0,10" />
                        </ViewCell>
                    </DataTemplate>
                </ListView.GroupHeaderTemplate>
                <ListView.ItemTemplate>
                    <DataTemplate>
                        <ViewCell>
                            <StackLayout>
                                <Frame Margin="0,10"
                                       Padding="14"
                                       HasShadow="True">
                                    <StackLayout>
                                        <controls:LoadItem Haul="{Binding Haul}"
                                                      Load="{Binding Load}"
                                                      FirstPickup="{Binding FirstPickup}"
                                                      LastDropoff="{Binding LastDropoff}"
                                                      ShowLoadDetailsCommand="{Binding BindingContext.ShowLoadDetailsCommand, Source={x:Reference page}}" />

                                        <Button Style="{StaticResource BtnFormStyle}"
                                                Text="{x:Static abstractions:Resources.UpdateStatusText}"
                                                Command="{Binding BindingContext.UpdateLoadStatusCommand, Source={x:Reference page}}"
                                                CommandParameter="{Binding Haul}"
                                                Margin="14,10,14,0" />

                                        <Button Style="{StaticResource BtnSecondaryFormStyle}"
                                                Text="{x:Static abstractions:Resources.RecordFuelStop}"
                                                Command="{nav:NavigateTo RecordFuelStopPage}"
                                                IsVisible="{Binding BindingContext.User.IftaEnabled, Source={x:Reference page}}"
                                                Margin="14,10,14,0" />
                                    </StackLayout>
                                </Frame>
                            </StackLayout>
                        </ViewCell>
                    </DataTemplate>
                </ListView.ItemTemplate>
                <ListView.Behaviors>
                    <behaviours:ListViewSelectedItemBehavior />
                </ListView.Behaviors>
            </ListView>

            <Label Text="{x:Static abstractions:Resources.NoLoadsAssigned}"
                   HorizontalOptions="Center"
                   VerticalOptions="Center"
                   IsVisible="{Binding LoadCount, Converter={StaticResource ObjectToBoolInverseConverter}}" />
        </Grid>
    </StackLayout>
</ContentPage>
