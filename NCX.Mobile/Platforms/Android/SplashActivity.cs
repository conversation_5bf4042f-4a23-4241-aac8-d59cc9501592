﻿using Android.App;
using Android.Content;
using Android.OS;
using AndroidX.AppCompat.App;
using AndroidApp = Android.App.Application;

namespace NCX.Mobile
{
    [Activity(
        Exported = true,
        Label = "NCX",
        Icon = "@mipmap/icon",
        Theme = "@style/Maui.SplashTheme",
        NoHistory = true)]
    public class SplashActivity : AppCompatActivity
    {
        protected override void OnCreate(Bundle? savedInstanceState)
        {
            base.OnCreate(savedInstanceState);
            StartMainActivity();
        }

        protected override void OnNewIntent(Intent? intent)
        {
            base.OnNewIntent(intent);
        }

        private void StartMainActivity()
        {
            var intent = new Intent(AndroidApp.Context, typeof(MainActivity));
            
            // Handle push notification data
            if (Intent?.HasExtra("message") == true)
            {
                intent.PutExtra("message", Intent.GetStringExtra("message"));
            }

            StartActivity(intent);
            Finish();
        }
    }
}
