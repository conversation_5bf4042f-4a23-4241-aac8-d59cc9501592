﻿using System;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models.Cache.PushOperations;
using NCX.Mobile.Services;
using NCX.Mobile.Services.Impl;

namespace NCX.Mobile.Droid.Helpers
{
    class CacheProcessorHelper
    {
        readonly IUserService _userService;
        readonly IPushOperationProcessor _cacheProcessor;
        readonly DefaultChainedPushOperationErrorHandler _defaultPushOperationErrorHandler;

        public CacheProcessorHelper(
            IUserService userService,
            IPushOperationProcessor cacheProcessor,
            DefaultChainedPushOperationErrorHandler defaultPushOperationErrorHandler)
        {
            _userService = userService;
            _cacheProcessor = cacheProcessor;
            _defaultPushOperationErrorHandler = defaultPushOperationErrorHandler;
        }

        public bool Handle(PushOperation pushOperation, Exception ex)
        {
            return true;
        }

        public bool RunCacheProcessor()
        {
            if (!_userService.IsLoggedIn)
            {
                return false;
            }

            bool shouldReschedule = false;

            AsyncHelpers.RunSync(() => System.Threading.Tasks.Task.Run(async () =>
            {
                using (await UserSessionSyncContext.AsyncLock.LockAsync())
                {
                    if (!_userService.IsLoggedIn)
                    {
                        return;
                    }

                    _defaultPushOperationErrorHandler.Add(
                        new RunActionPushOperationCacheErrorHandler(() => shouldReschedule = true));

                    await _cacheProcessor.RunAsync(_defaultPushOperationErrorHandler);
                }
            }));

            return shouldReschedule;
        }
    }
}