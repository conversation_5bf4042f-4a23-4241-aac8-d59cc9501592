using System; // For Uri
using Android.App;
using Android.Content;
using Android.Graphics;
using Android.OS;
using Microsoft.Maui; // For BuildVersionCodes, etc.
using NCX.Mobile.Platforms.Android; // Ensure this matches your MainActivity's namespace
using AndroidApp = Android.App.Application; // Alias to disambiguate from MAUI Application
using JavaClass = Java.Lang.Class; // Alias for clarity

namespace NCX.Mobile.Platforms.Android.Helpers
{
    public static class NotificationBuilder
    {
        public static string UrgentChannel = "com.ncx.mobile.urgent";
        public static string DebugChannel = "com.ncx.mobile.debug";

        public static PendingIntent CreatePendingIntent(int requestCode)
        {
            // Use JavaClass.FromType to convert the type to Java.Lang.Class
            var intent = new Intent(AndroidApp.Context, JavaClass.FromType(typeof(MainActivity)));
            return PendingIntent.GetActivity(
                AndroidApp.Context,
                requestCode,
                intent,
                PendingIntentFlags.Immutable | PendingIntentFlags.UpdateCurrent
            );
        }

        public static Notification CreateNotification(
            string title,
            string text,
            string channelId,
            bool autoCancel = false,
            PendingIntent pendingIntent = null)
        {
            if (pendingIntent == null)
            {
                pendingIntent = CreatePendingIntent(0);
            }

            var notificationBuilder = BuildNotificationBuilder(title, text, channelId, pendingIntent, autoCancel);
            return notificationBuilder.Build();
        }

        private static Notification.Builder BuildNotificationBuilder(
            string title,
            string text,
            string channelId,
            PendingIntent pendingIntent,
            bool autoCancel)
        {
            if (Build.VERSION.SdkInt < BuildVersionCodes.O)
            {
                // For devices below Android Oreo (suppress obsolete warning)
#pragma warning disable CS0618
                return new Notification.Builder(AndroidApp.Context)
                    .SetContentIntent(pendingIntent)
                    .SetContentTitle(title)
                    .SetContentText(text)
                    .SetSmallIcon(GetSmallIcon())
                    .SetLargeIcon(BitmapFactory.DecodeResource(AndroidApp.Context.Resources, Resource.Drawable.icon))
                    .SetAutoCancel(autoCancel);
#pragma warning restore CS0618
            }

            // For Android Oreo and above
            var notificationManager = (NotificationManager)AndroidApp.Context.GetSystemService(Context.NotificationService);
            CreateChannelIfNotExists(notificationManager, channelId);

            return new Notification.Builder(AndroidApp.Context, channelId)
                .SetContentIntent(pendingIntent)
                .SetContentTitle(title)
                .SetContentText(text)
                .SetSmallIcon(GetSmallIcon())
                .SetLargeIcon(BitmapFactory.DecodeResource(AndroidApp.Context.Resources, Resource.Drawable.icon))
                .SetAutoCancel(autoCancel);
        }

        private static int GetSmallIcon()
        {
            return Build.VERSION.SdkInt >= BuildVersionCodes.Lollipop
                ? Resource.Drawable.notification_icon
                : Resource.Drawable.icon;
        }

        private static void CreateChannelIfNotExists(NotificationManager notificationManager, string channelId)
        {
            var channel = notificationManager.GetNotificationChannel(channelId);
            if (channel == null)
            {
                CreateChannel(notificationManager, channelId);
            }
        }

        private static void CreateChannel(NotificationManager notificationManager, string channelId)
        {
            string channelName = channelId switch
            {
                var id when id == DebugChannel => "Info",
                var id when id == UrgentChannel => "Urgent",
                _ => "Unknown"
            };

            var channel = new NotificationChannel(channelId, channelName, NotificationImportance.High)
            {
                LockscreenVisibility = NotificationVisibility.Public
            };
            channel.EnableVibration(true);
            channel.EnableLights(true);

            notificationManager.CreateNotificationChannel(channel);
        }
    }
}
