﻿using System.Collections.Generic;

namespace NCX.Mobile.Droid.Helpers
{
    // Keeps flags for the state of dialogs
    // Used to know the state of permissiond dialogs so they are displayed again on the onResme of the Activity 
    static class ActivityDialogManager
    {
        static Dictionary<int, bool> IsDisplayedMap = new Dictionary<int, bool>();

        public static void OnActivityCreated()
        {
            IsDisplayedMap.Clear();
        }

        public static void OnActivityResult(int requestCode)
        {
            IsDisplayedMap[requestCode] = false;
        }

        public static void SetIsDisplayed(int id, bool isDisplayed)
        {
            IsDisplayedMap[id] = isDisplayed;
        }

        public static bool GetIsDisplayed(int id)
        {
            bool isDisplayed;

            return IsDisplayedMap.TryGetValue(id, out isDisplayed) && isDisplayed;
        }
    }
}