using Android.App;
using Android.Content;
using Android.Gms.Common;
using Android.Net;
using Android.OS;
using Android.Provider;
using NCX.Logging.Interfaces;
using NCX.Mobile.Helpers;

// Alias Android types to avoid ambiguity.
using AndroidApp = Android.App.Application;
using AndroidUri = Android.Net.Uri;

namespace NCX.Mobile.Droid.Helpers
{
    static class AppTools
    {
        /// <summary>
        /// Checks for Google Play Services state on device and displays appropriate dialog.
        /// </summary>
        /// <param name="activity"></param>
        /// <param name="logger"></param>
        /// <returns>true if Google Play Services is available</returns>
        public static bool EnsureGooglePlayServicesIsInstalled(Activity activity, ILogService logger)
        {
            int queryResult = GoogleApiAvailability.Instance.IsGooglePlayServicesAvailable(AndroidApp.Context);

            if (queryResult != ConnectionResult.Success)
            {
                if (GoogleApiAvailability.Instance.IsUserResolvableError(queryResult))
                {
                    string errorString = GoogleApiAvailability.Instance.GetErrorString(queryResult);

                    logger.LogHigh(Constants.Log_Tag, $"There is a problem with Google Play Services on this device: {queryResult}, {errorString}");

                    GoogleApiAvailability.Instance.GetErrorDialog(activity, queryResult, ActivityResultCodes.PlayServicesResolution).Show();
                }

                return true; // Note: adjust return value as needed.
            }
            else
            {
                return true;
            }
        }

        /// <summary>
        /// Checks if the app is ignoring battery optimization and, if not, displays a dialog asking the user to enable it.
        /// Enhanced for Android 11+ compatibility.
        /// </summary>
        /// <param name="activity"></param>
        public static void EnsureAppIsWhitelistedForDozeMode(Activity activity)
        {
            if (Build.VERSION.SdkInt < BuildVersionCodes.M || ActivityDialogManager.GetIsDisplayed(ActivityResultCodes.IgnoreBatteryOptimization))
            {
                return;
            }

#if !DEBUG
            PowerManager powerManager = (PowerManager)AndroidApp.Context.GetSystemService(Context.PowerService);

            if (!powerManager.IsIgnoringBatteryOptimizations(AndroidApp.Context.PackageName))
            {
                ActivityDialogManager.SetIsDisplayed(ActivityResultCodes.IgnoreBatteryOptimization, true);

                new AlertDialog.Builder(activity)
                    .SetTitle("NCX")
                    .SetMessage("NCX requires you to disable battery optimization to prevent screen blanking and ensure accurate location tracking.")
                    .SetCancelable(false)
                    .SetPositiveButton("Disable now", (sender, args) =>
                    {
                        try
                        {
                            var intent = new Intent(Settings.ActionRequestIgnoreBatteryOptimizations);
                            intent.SetData(AndroidUri.Parse($"package:{activity.PackageName}"));
                            activity.StartActivityForResult(intent, ActivityResultCodes.IgnoreBatteryOptimization);
                        }
                        catch (Exception ex)
                        {
                            // Log the error
                            System.Diagnostics.Debug.WriteLine($"Failed to launch battery optimization settings: {ex}");
                            ActivityDialogManager.SetIsDisplayed(ActivityResultCodes.IgnoreBatteryOptimization, false);
                        }
                    })
                    .Show();
            }
#endif
        }
    }
}
