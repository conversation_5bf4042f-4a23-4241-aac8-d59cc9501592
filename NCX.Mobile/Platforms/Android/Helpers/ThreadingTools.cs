﻿using Android.OS;
using System;

namespace NCX.Mobile.Droid.Helpers
{
    static class ThreadingTools
    {
        static Handler s_handler;

        public static void BeginInvokeOnMainThread(Action action)
        {
            if (s_handler == null || s_handler.Looper != Looper.MainLooper)
            {
                s_handler = new Handler(Looper.MainLooper);
            }

            s_handler.Post(action);
        }
    }
}