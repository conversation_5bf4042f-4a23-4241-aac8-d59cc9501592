﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Android.App;
using Android.Content;
using Android.Gms.Common.Apis;
using Android.Gms.Location;
using Android.Locations;
using Microsoft.AppCenter.Crashes;
using NCX.Logging.Interfaces;
using NCX.Mobile.Helpers;

// Create aliases to disambiguate types.
using AndroidApp = Android.App.Application;
using AndroidLoc = Android.Locations.Location;
using JavaClass = Java.Lang.Class;

namespace NCX.Mobile.Droid.Helpers
{
    static class LocationServicesHelper
    {
        public static async Task<IResult> StartGettingLocationUpdatesAsync(GoogleApiClient googleApiClient,
                                                                           float updateDisplacement,
                                                                           long locationUpdateInterval,
                                                                           Type pendingIntentType)
        {
            var locationRequest = BuildLocationRequest(
                priority: Android.Gms.Location.LocationRequest.PriorityHighAccuracy,
                minimumDisplacement: updateDisplacement,
                locationUpdateInterval: locationUpdateInterval);

            return await LocationServices.FusedLocationApi.RequestLocationUpdates(
                googleApiClient,
                locationRequest,
                CreatePendingIntent(pendingIntentType, PendingIntentFlags.UpdateCurrent));
        }

        public static void StopGettingLocationUpdates(GoogleApiClient googleApiClient, Type intentType)
        {
            LocationServices.FusedLocationApi.RemoveLocationUpdates(
                googleApiClient,
                CreatePendingIntent(intentType, PendingIntentFlags.NoCreate));
        }

        public static void SendLastLocation(GoogleApiClient googleApiClient, Type intentType, ILogService logger)
        {
            try
            {
                // Use Android.Locations.Location explicitly.
                AndroidLoc lastLocation = LocationServices.FusedLocationApi.GetLastLocation(googleApiClient);

                if (lastLocation != null)
                {
                    var intent = new Intent(AndroidApp.Context, JavaClass.FromType(intentType));
                    // Convert lastLocation to a list as expected by LocationResult.Create.
                    var locationList = new List<AndroidLoc> { lastLocation };
                    intent.PutExtra("LastKnownLocation", LocationResult.Create(locationList));
                    AndroidApp.Context.StartService(intent);

                    logger.LogDebug(Constants.Log_Tag, "Last location exists");
                }
                else
                {
                    logger.LogDebug(Constants.Log_Tag, "Last location is null");
                }
            }
            catch (Exception ex)
            {
                Crashes.TrackError(ex);
                logger.LogWarn(Constants.Log_Tag, "Error getting and handling last location", ex);
            }
        }

        public static Android.Gms.Location.LocationRequest BuildLocationRequest(int priority, float minimumDisplacement, long locationUpdateInterval)
        {
            // Create the LocationRequest object.
            var locationRequest = Android.Gms.Location.LocationRequest.Create();
            locationRequest.SetPriority(priority);
            locationRequest.SetSmallestDisplacement(minimumDisplacement);
            locationRequest.SetInterval(locationUpdateInterval);
            locationRequest.SetFastestInterval(locationUpdateInterval);
            return locationRequest;
        }

        public static async Task<bool?> CheckLocationSettingsAndDisplayResolutionDialogAsync(GoogleApiClient googleApiClient, Activity activity, int requestCode, ILogService logger)
        {
            var locationRequest = BuildLocationRequest(
                Android.Gms.Location.LocationRequest.PriorityHighAccuracy,
                5000, // dummy value
                1000);

            var builder = new LocationSettingsRequest.Builder().AddLocationRequest(locationRequest);

            LocationSettingsResult result = (LocationSettingsResult)await LocationServices.SettingsApi.CheckLocationSettings(googleApiClient, builder.Build());

            switch (result.Status.StatusCode)
            {
                case LocationSettingsStatusCodes.Success:
                    return true;
                case LocationSettingsStatusCodes.ResolutionRequired:
                    result.Status.StartResolutionForResult(activity, requestCode);
                    return false;
                default:
                    logger.LogWarn(Constants.Log_Tag, $"Could not check location settings, returned {result.Status.StatusCode}");
                    return null;
            }
        }

        static PendingIntent CreatePendingIntent(Type intentType, PendingIntentFlags flags)
        {
            return PendingIntent.GetService(
                AndroidApp.Context,
                0,
                new Intent(AndroidApp.Context, JavaClass.FromType(intentType)),
                flags);
        }
    }
}
