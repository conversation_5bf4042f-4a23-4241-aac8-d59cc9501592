﻿using Android.App;
using Android.Gms.Common;
using Android.Gms.Common.Apis;
using Android.OS;
using System;

namespace NCX.Mobile.Droid.Helpers
{
    // Connects to GoogleApiClient
    // Caller needs to disconnect GoogleApiClient when it no longer needs it
    class GoogleApiClientConnectionHelper
    {
        GoogleApiClient _googleApiClient;
        GoogleApiClientListener _listener;

        GoogleApiClientConnectionHelper()
        {
        }

        // Cancel connectiong to GoogleApiClient
        public void CancelConnecting()
        {
            if (_googleApiClient == null)
            {
                return;
            }

            UnregisterListener();

            _googleApiClient = null;
            _listener = null;
        }

        public static GoogleApiClientConnectionHelper StartConnecting(Api api, Action<GoogleApiClient> onConnectCompleted)
        {
            var connectionHelper = new GoogleApiClientConnectionHelper();
            connectionHelper.Connect(api, onConnectCompleted);

            return connectionHelper;
        }

        void Connect(Api api, Action<GoogleApiClient> onConnectCompleted)
        {
            _googleApiClient = null;
            _listener = null;

            _listener = new GoogleApiClientListener(() =>
            {
                UnregisterListener();
                _listener = null;

                onConnectCompleted(_googleApiClient);
                onConnectCompleted = null;
                _googleApiClient = null;
            });

            _googleApiClient = BuildGoogleApiClient(api, _listener);
            _googleApiClient.Connect();
        }

        static GoogleApiClient BuildGoogleApiClient(Api googleApiToConnectTo, GoogleApiClientListener listener)
        {
            return new GoogleApiClient.Builder(Android.App.Application.Context)
                  .AddConnectionCallbacks(listener)
                  .AddOnConnectionFailedListener(listener)
                  .AddApi(googleApiToConnectTo)
                  .Build();
        }

        void UnregisterListener()
        {
            _googleApiClient.UnregisterConnectionCallbacks(_listener);
            _googleApiClient.UnregisterConnectionFailedListener(_listener);
        }

        class GoogleApiClientListener : Java.Lang.Object, GoogleApiClient.IConnectionCallbacks, GoogleApiClient.IOnConnectionFailedListener
        {
            readonly Action _onConnectCompleted;

            public GoogleApiClientListener(Action onConnectCompleted)
            {
                _onConnectCompleted = onConnectCompleted;
            }

            public void OnConnected(Bundle connectionHint)
            {
                _onConnectCompleted();
            }

            public void OnConnectionFailed(ConnectionResult result)
            {
                _onConnectCompleted();
            }

            public void OnConnectionSuspended(int cause)
            {
                // Wait for reconnection, GoogleApiClient will automatically attempt to restore the connection
            }
        }
    }
}