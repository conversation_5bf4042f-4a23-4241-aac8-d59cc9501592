﻿using System;
using Android.Graphics;
using Android.Widget;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Platform;
using Microsoft.Maui.Controls.Platform.Compatibility;
using Microsoft.Maui.Platform;
using NCX.Mobile.Effects;
using NCX.Mobile.Droid.Effects;
using Microsoft.Maui;

[assembly: ResolutionGroupName("NCX.Mobile.Effects")]
[assembly: ExportEffect(typeof(UnderlineEffectHandler), nameof(UnderlineEffect))]

namespace NCX.Mobile.Droid.Effects
{
    public class UnderlineEffectHandler : PlatformEffect
    {
        protected override void OnAttached()
        {
            SetUnderline(true);
        }

        protected override void OnDetached()
        {
            SetUnderline(false);
        }

        protected override void OnElementPropertyChanged(System.ComponentModel.PropertyChangedEventArgs args)
        {
            base.OnElementPropertyChanged(args);

            if (args.PropertyName == Label.TextProperty.PropertyName || args.PropertyName == Label.FormattedTextProperty.PropertyName)
            {
                SetUnderline(true);
            }
        }

        private void SetUnderline(bool underlined)
        {
            if (Control is TextView textView)
            {
                if (underlined)
                {
                    textView.PaintFlags |= PaintFlags.UnderlineText;
                }
                else
                {
                    textView.PaintFlags &= ~PaintFlags.UnderlineText;
                }
            }
        }
    }
}
