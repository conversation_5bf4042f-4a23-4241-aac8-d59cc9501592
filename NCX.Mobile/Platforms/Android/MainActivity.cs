﻿using System; // Added for Uri
using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.OS;
using Android.Runtime;
using Microsoft.Maui;
using Microsoft.Maui.Controls.Platform;
using Microsoft.Extensions.DependencyInjection;
using NCX.Logging.Interfaces;
using NCX.Mobile.Helpers;
using NCX.Mobile.Services;
using Shiny;
using Shiny.Infrastructure;
using NCX.Mobile.Droid.Helpers;

namespace NCX.Mobile
{
    [Activity(
        Exported = true,
        Label = "NCX",
        Icon = "@mipmap/icon",
        Theme = "@style/Maui.SplashTheme",
        MainLauncher = true,
        LaunchMode = LaunchMode.SingleTask,
        ScreenOrientation = ScreenOrientation.Portrait,
        ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation)]
    [IntentFilter(new[]
        { Intent.ActionView },
        DataScheme = Constants.DeepLinkScheme,
        DataHost = Constants.DeepLinkHostLogin,
        Categories = new[] { Intent.CategoryDefault, Intent.CategoryBrowsable })]
    public class MainActivity : MauiAppCompatActivity
    {
        ILogService _logger;
        private PowerManager.WakeLock _wakeLock;

        protected override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

            // Resolve services via Dependency Injection
            _logger = MauiApplication.Current.Services.GetService<ILogService>();

            ActivityDialogManager.OnActivityCreated();

            if (!AppTools.EnsureGooglePlayServicesIsInstalled(this, _logger))
            {
                return;
            }

            // Acquire wake lock to prevent screen blanking
            AcquireWakeLock();

            // Handle the initial intent
            HandleIntent(Intent);
        }

        private void AcquireWakeLock()
        {
            try
            {
                var powerManager = (PowerManager)GetSystemService(Context.PowerService);
                _wakeLock = powerManager.NewWakeLock(WakeLockFlags.ScreenDim | WakeLockFlags.AcquireCausesWakeup, "NCX::MainActivityWakeLock");
                _wakeLock.Acquire();
            }
            catch (Exception ex)
            {
                _logger?.LogHigh("MainActivity", "Failed to acquire wake lock", ex);
            }
        }

        public override void OnRequestPermissionsResult(int requestCode, string[] permissions, [GeneratedEnum] Permission[] grantResults)
        {
            base.OnRequestPermissionsResult(requestCode, permissions, grantResults);
            // Handle permissions using MAUI's built-in permissions API if needed
        }

        protected override void OnResume()
        {
            base.OnResume();

            // Ensure the app is whitelisted for Doze Mode
            AppTools.EnsureAppIsWhitelistedForDozeMode(this);
            
            // Re-acquire wake lock if needed
            if (_wakeLock == null || !_wakeLock.IsHeld)
            {
                AcquireWakeLock();
            }
        }

        protected override void OnPause()
        {
            base.OnPause();
            
            // Release wake lock when app is in background
            if (_wakeLock != null && _wakeLock.IsHeld)
            {
                _wakeLock.Release();
            }
        }

        protected override void OnDestroy()
        {
            // Release wake lock when activity is destroyed
            if (_wakeLock != null && _wakeLock.IsHeld)
            {
                _wakeLock.Release();
                _wakeLock = null;
            }
            
            base.OnDestroy();
        }

        protected override void OnActivityResult(int requestCode, Result resultCode, Android.Content.Intent? data)
        {
            base.OnActivityResult(requestCode, resultCode, data);
            
            if (requestCode == ActivityResultCodes.IgnoreBatteryOptimization)
            {
                // Reset the dialog flag so it can show again if needed
                ActivityDialogManager.SetIsDisplayed(ActivityResultCodes.IgnoreBatteryOptimization, false);
                
                // Verify the battery optimization setting
                var powerManager = (PowerManager)GetSystemService(Context.PowerService);
                if (!powerManager.IsIgnoringBatteryOptimizations(PackageName))
                {
                    // If still not ignored, show the dialog again
                    AppTools.EnsureAppIsWhitelistedForDozeMode(this);
                }
            }
        }

        protected override void OnNewIntent(Intent intent)
        {
            base.OnNewIntent(intent);
            HandleIntent(intent);
        }

        void HandleIntent(Intent intent)
        {
            if (intent.Data != null)
            {
                HandleIntentDeepLinkReceived(intent);
            }
            else
            {
                // It must be because user tapped on the notification
                HandleIntentNotificationTapped(intent);
            }
        }

        void HandleIntentDeepLinkReceived(Intent intent)
        {
            var deepLinkHandler = MauiApplication.Current.Services.GetService<DeepLinkHandler>();
            deepLinkHandler?.TryHandle(new Uri(intent.Data.ToString()));
        }

        void HandleIntentNotificationTapped(Intent intent)
        {
            if (intent.HasExtra("message"))
            {
                var refreshHaulsCommand = MauiApplication.Current.Services.GetService<IRefreshHaulsCommand>();
                refreshHaulsCommand?.Execute();
            }
        }
    }
}
