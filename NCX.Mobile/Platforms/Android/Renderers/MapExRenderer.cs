﻿using Android.Gms.Maps;
using Android.Gms.Maps.Model;
using Microsoft.Maui;
using Microsoft.Maui.Controls.Compatibility;
using Microsoft.Maui.Controls.Hosting;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Maps.Handlers;
using NCX.Mobile.Controls.Common;
using System;

[assembly: ExportRenderer(typeof(MapEx), typeof(NCX.Mobile.Platforms.Android.Handlers.MapExHandler))]

namespace NCX.Mobile.Platforms.Android.Handlers
{
    public class MapExHandler : MapHandler
    {
        public MapExHandler() : base()
        {
        }

        protected override MapView CreatePlatformView()
        {
            var mapView = base.CreatePlatformView();
            mapView.GetMapAsync(new OnMapReadyCallback(this));
            return mapView;
        }

        private class OnMapReadyCallback : Java.Lang.Object, IOnMapReadyCallback
        {
            private readonly MapExHandler _handler;
            public OnMapReadyCallback(MapExHandler handler)
            {
                _handler = handler;
            }
            public void OnMapReady(GoogleMap googleMap)
            {
                _handler.OnMapReady(googleMap);
            }
        }

        private void OnMapReady(GoogleMap map)
        {
            if (VirtualView is MapEx mapEx && mapEx.LastMoveToRegion != null)
            {
                var region = mapEx.LastMoveToRegion;
                var center = new LatLng(region.Center.Latitude, region.Center.Longitude);
                float zoomLevel = CalculateZoomLevel(region.Radius.Meters);
                var cameraUpdate = CameraUpdateFactory.NewLatLngZoom(center, zoomLevel);
                map.MoveCamera(cameraUpdate);
            }
        }

        private float CalculateZoomLevel(double radiusInMeters)
        {
            double scale = radiusInMeters / 500;
            return (float)(16 - Math.Log(scale) / Math.Log(2));
        }
    }
}
