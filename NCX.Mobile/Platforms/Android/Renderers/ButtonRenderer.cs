﻿using System;
using Android.Content;
using Android.Graphics;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Platform;
using Microsoft.Maui.Controls.Compatibility.Platform.Android; // Ensure Compatibility is enabled
using NCX.Mobile.Droid.Renderers;
using NCX.Mobile.Helpers;
using Microsoft.Maui.Controls.Compatibility;

// Register the renderer with MAUI's compatibility system
[assembly: ExportRenderer(typeof(Button), typeof(ButtonRendererEx))]

namespace NCX.Mobile.Droid.Renderers
{
    // Inherit from MAUI's compatibility renderer
    internal class ButtonRendererEx : Microsoft.Maui.Controls.Compatibility.Platform.Android.AppCompat.ButtonRenderer
    {
        public ButtonRendererEx(Context context)
            : base(context)
        {
        }

        protected override void OnElementChanged(ElementChangedEventArgs<Button> e)
        {
            base.OnElementChanged(e);

            if (e.NewElement != null && ButtonEx.GetIsLinkButton(e.NewElement))
            {
                // Apply underline
                Control.PaintFlags |= PaintFlags.UnderlineText;

                // Set background to transparent
                Control.SetBackgroundColor(Android.Graphics.Color.Transparent);

                // Remove text transformation (e.g., all caps)
                Control.TransformationMethod = null;
            }
        }
    }
}
