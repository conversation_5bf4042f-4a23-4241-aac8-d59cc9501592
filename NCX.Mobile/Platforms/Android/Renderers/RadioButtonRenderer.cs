﻿using Android.Widget;
using Microsoft.Maui;
using Microsoft.Maui.Handlers;
using NCX.Mobile.Controls.Common;

namespace NCX.Mobile.Droid.Renderers;

public class RadioButtonHandler : ViewHandler<NCX.Mobile.Controls.Common.RadioButton, Android.Widget.RadioButton>
{
    public static IPropertyMapper<NCX.Mobile.Controls.Common.RadioButton, RadioButtonHandler> PropertyMapper =
        new PropertyMapper<NCX.Mobile.Controls.Common.RadioButton, RadioButtonHandler>(ViewHandler.ViewMapper)
        {
            [nameof(NCX.Mobile.Controls.Common.RadioButton.IsChecked)] = MapIsChecked,
        };

    public RadioButtonHandler() : base(PropertyMapper)
    {
    }

    protected override Android.Widget.RadioButton CreatePlatformView()
    {
        var context = MauiContext!.Context;
        return new Android.Widget.RadioButton(context);
    }

    protected override void ConnectHandler(Android.Widget.RadioButton platformView)
    {
        base.ConnectHandler(platformView);
        platformView.CheckedChange += OnCheckedChanged;
        platformView.Checked = VirtualView?.IsChecked ?? false;
    }

    protected override void DisconnectHandler(Android.Widget.RadioButton platformView)
    {
        platformView.CheckedChange -= OnCheckedChanged;
        base.DisconnectHandler(platformView);
    }

    private void OnCheckedChanged(object? sender, CompoundButton.CheckedChangeEventArgs e)
    {
        if (VirtualView != null)
            VirtualView.IsChecked = e.IsChecked;
    }

    private static void MapIsChecked(RadioButtonHandler handler, NCX.Mobile.Controls.Common.RadioButton view)
    {
        handler.PlatformView.Checked = view.IsChecked;
    }
}