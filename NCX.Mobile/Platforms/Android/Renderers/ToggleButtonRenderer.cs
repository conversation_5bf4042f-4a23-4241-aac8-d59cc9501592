﻿// NCX.Mobile.Platforms.Android/Handlers/ToggleButtonHandler.cs
using Android.Widget;
using Microsoft.Maui.Handlers;
using NCX.Mobile.Controls.Common;
using Microsoft.Maui.Graphics;
using Microsoft.Maui;
using System.ComponentModel;

// Alias the custom ToggleButton to avoid ambiguity with Android.Widget.ToggleButton
using CustomToggleButton = NCX.Mobile.Controls.Common.ToggleButton;
using Switch = Android.Widget.Switch;
using Microsoft.Maui.Platform;

namespace NCX.Mobile.Platforms.Android.Handlers
{
    public partial class ToggleButtonHandler : ViewHandler<CustomToggleButton, CompoundButton>
    {
        // Define the Property Mapper
        public static IPropertyMapper<CustomToggleButton, ToggleButtonHandler> ToggleButtonMapper =
            new PropertyMapper<CustomToggleButton, ToggleButtonHandler>(ViewHandler.ViewMapper)
            {
                [nameof(CustomToggleButton.IsChecked)] = MapIsChecked,
                [nameof(CustomToggleButton.Text)] = MapText,
                [nameof(CustomToggleButton.TextColor)] = MapTextColor,
                [nameof(CustomToggleButton.IsEnabled)] = MapIsEnabled
            };

        public ToggleButtonHandler() : base(ToggleButtonMapper)
        {
        }

        protected override CompoundButton CreatePlatformView()
        {
            var nativeToggle = new Switch(Context); // You can use Switch, RadioButton, etc.
            return nativeToggle;
        }

        protected override void ConnectHandler(CompoundButton nativeView)
        {
            base.ConnectHandler(nativeView);
            nativeView.CheckedChange += OnNativeCheckedChange;
        }

        protected override void DisconnectHandler(CompoundButton nativeView)
        {
            if (nativeView != null)
            {
                nativeView.CheckedChange -= OnNativeCheckedChange;
            }
            base.DisconnectHandler(nativeView);
        }

        // Property Mappers
        public static void MapIsChecked(ToggleButtonHandler handler, CustomToggleButton toggleButton)
        {
            if (handler.PlatformView != null && toggleButton != null)
            {
                handler.PlatformView.Checked = toggleButton.IsChecked;
            }
        }

        public static void MapText(ToggleButtonHandler handler, CustomToggleButton toggleButton)
        {
            if (handler.PlatformView != null && toggleButton != null)
            {
                handler.PlatformView.Text = toggleButton.Text;
            }
        }

        public static void MapTextColor(ToggleButtonHandler handler, CustomToggleButton toggleButton)
        {
            if (handler.PlatformView != null && toggleButton != null)
            {
                handler.PlatformView.SetTextColor(toggleButton.TextColor.ToPlatform());
            }
        }

        public static void MapIsEnabled(ToggleButtonHandler handler, CustomToggleButton toggleButton)
        {
            if (handler.PlatformView != null && toggleButton != null)
            {
                handler.PlatformView.Enabled = toggleButton.IsEnabled;
            }
        }

        private void OnNativeCheckedChange(object sender, CompoundButton.CheckedChangeEventArgs e)
        {
            if (VirtualView != null && VirtualView.IsChecked != e.IsChecked)
            {
                VirtualView.IsChecked = e.IsChecked;
            }
        }
    }
}
