﻿using Android.Content;
using Microsoft.Maui.Controls.Compatibility;
using Microsoft.Maui.Controls.Compatibility.Platform.Android;

[assembly: Export<PERSON><PERSON><PERSON>(typeof(NCX.Mobile.Controls.Common.CheckBox), typeof(NCX.Mobile.Droid.Renderers.CheckBoxRenderer))]

namespace NCX.Mobile.Droid.Renderers
{
    internal class CheckBoxRenderer : ViewRenderer<NCX.Mobile.Controls.Common.CheckBox, Android.Widget.CheckBox>
    {
        public CheckBoxRenderer(Context context)
            : base(context)
        {
        }

        protected override Android.Widget.CheckBox CreateNativeControl()
        {
            return new Android.Widget.CheckBox(Context);
        }
    }
}
