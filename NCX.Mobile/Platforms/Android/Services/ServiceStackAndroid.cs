﻿using Android.App;
using NCX.Logging.Interfaces;
using NCX.Mobile.Services;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.IO;
using SQLite;

namespace NCX.Mobile.Droid.Services
{
    public static class ServiceStackAndroid
    {
        public static void ConfigureServices(IServiceCollection services)
        {
            // Register SQLiteAsyncConnection factory
            services.AddSingleton<Func<SQLiteAsyncConnection>>(() =>
                new SQLiteAsyncConnection(GetDatabasePath()));

            // Register platform-specific services with appropriate lifetimes
            services.AddSingleton<IPushOperationProcessorService, PushOperationProcessorService>();
            services.AddSingleton<IPlatformTools, PlatformTools>();

            // If you have a platform-specific implementation of ILocalize, register it here.
            // e.g.: services.AddSingleton<ILocalize, LocalizeAndroid>();

            // (If you need to register logging commands or similar, do so here.)
        }

        private static string GetDatabasePath()
        {
            return Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Personal), "ncx_v2.db3");
        }
    }
}
