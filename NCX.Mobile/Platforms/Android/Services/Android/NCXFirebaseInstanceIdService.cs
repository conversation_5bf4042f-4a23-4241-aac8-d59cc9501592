﻿using Android.App;
using Android.Content;
using Firebase.Messaging;
using Prism.Ioc;
using NCX.Logging.Interfaces;
using NCX.Mobile.Services;
using System;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Maui;

namespace NCX.Mobile.Droid.Services.Android
{
    [Service(Exported = true)]
    [IntentFilter(new[] { "com.google.firebase.MESSAGING_EVENT" })]
    public class NCXFirebaseMessagingService : FirebaseMessagingService
    {
        readonly ILogService _logger;
        readonly IPushNotificationTokenCacheService _pushNotificationTokenCacheService;
        readonly IPlatformTools _platformTools;

        public NCXFirebaseMessagingService()
        {
            var serviceProvider = MauiApplication.Current.Services;
            _logger = serviceProvider.GetService<ILogService>();
            _pushNotificationTokenCacheService = serviceProvider.GetService<IPushNotificationTokenCacheService>();
            _platformTools = serviceProvider.GetService<IPlatformTools>();
        }

        public override void OnNewToken(string token)
        {
            try
            {
                _platformTools.InvokeOnMainThread(() =>
                {
                    try
                    {
                        _logger.LogDebug(Mobile.Helpers.Constants.Log_Tag, "New token: " + token);
                        _pushNotificationTokenCacheService.SaveToken(token);
                    }
                    catch (Exception ex)
                    {
                        Microsoft.AppCenter.Crashes.Crashes.TrackError(ex);
                        _logger.LogDebug(Mobile.Helpers.Constants.Log_Tag, "Error processing new token", ex);
                    }
                });
            }
            catch (Exception ex)
            {
                Microsoft.AppCenter.Crashes.Crashes.TrackError(ex);
                _logger.LogDebug(Mobile.Helpers.Constants.Log_Tag, "Error processing new token", ex);
            }
        }
    }
}
