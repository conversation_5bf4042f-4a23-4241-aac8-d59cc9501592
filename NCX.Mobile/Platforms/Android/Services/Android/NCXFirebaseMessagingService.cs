﻿using Android.App;
using Android.Content;
using Firebase.Messaging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Maui;
using NCX.Logging.Interfaces;
using NCX.Mobile.Services;
using System;

namespace NCX.Mobile.Platforms.Android.Services
{
    [Service(Exported = true)]
    [IntentFilter(new[] { "com.google.firebase.MESSAGING_EVENT" })]
    public class NCXFirebaseMessagingService : FirebaseMessagingService
    {
        private ILogService _logger;
        private IPushNotificationTokenCacheService _pushNotificationTokenCacheService;
        private IPlatformTools _platformTools;

        public override void OnCreate()
        {
            base.OnCreate();
            try
            {
                var serviceProvider = MauiApplication.Current.Services;
                _logger = serviceProvider.GetService<ILogService>();
                _pushNotificationTokenCacheService = serviceProvider.GetService<IPushNotificationTokenCacheService>();
                _platformTools = serviceProvider.GetService<IPlatformTools>();

                _logger.LogDebug("Firebase", "NCXFirebaseMessagingService OnCreate: Service is starting up.");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Error in OnCreate: " + ex);
            }
        }

        public override void OnNewToken(string token)
        {
            _logger?.LogDebug("Firebase", $"OnNewToken called with token: {token}");
            base.OnNewToken(token);
            HandleNewToken(token);
        }

        private void HandleNewToken(string token)
        {
            try
            {
                _platformTools.InvokeOnMainThread(() =>
                {
                    try
                    {
                        _logger.LogDebug("Firebase", $"Refreshed token: {token}");
                        _pushNotificationTokenCacheService.SaveToken(token);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogHigh("Firebase", "Error handling token on main thread", ex);
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogHigh("Firebase", "Error invoking main thread for token handling", ex);
            }
        }

        public override void OnDestroy()
        {
            _logger?.LogDebug("Firebase", "NCXFirebaseMessagingService OnDestroy: Service is being destroyed.");
            base.OnDestroy();
        }
    }
}
