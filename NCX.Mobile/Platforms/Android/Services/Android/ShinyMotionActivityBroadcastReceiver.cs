using System;
using Android.App;
using Android.Content;
using Android.Gms.Location;
using Android.Util;

[BroadcastReceiver(Name = "com.ncx.mobile.MotionActivityBroadcastReceiver", Exported = true)]
[IntentFilter(new[] { "com.google.android.gms.location.ACTIVITY_RECOGNITION_DATA" })]
public class ShinyMotionActivityBroadcastReceiver : BroadcastReceiver
{
    public override void OnReceive(Context context, Intent intent)
    {
        try
        {
            // Check if the intent contains an ActivityRecognitionResult
            if (ActivityRecognitionResult.HasResult(intent))
            {
                var result = ActivityRecognitionResult.ExtractResult(intent);
                var mostProbableActivity = result?.MostProbableActivity;
                Log.Debug("MotionActivityReceiver", $"Detected activity: {mostProbableActivity?.Type}, Confidence: {mostProbableActivity?.Confidence}");
                // Process the activity result as needed.
            }
            else
            {
                Log.Warn("MotionActivityReceiver", "No activity recognition result found in intent.");
            }
        }
        catch (Exception ex)
        {
            Log.Error("MotionActivityReceiver", $"Error processing activity recognition data: {ex.Message}");
        }
    }
}
