//using Android.App;
//using Android.Content;
//using Android.OS;
//using Prism.Ioc;
//using NCX.Logging.Interfaces;
//using NCX.Mobile.Droid.Helpers;
//using NCX.Mobile.Helpers;
//using NCX.Mobile.Services;
//using System;

//namespace NCX.Mobile.Droid.Services.Android
//{
//    [Service]
//    class LocationTrackingService : Service
//    {
//        ILogService _logger;

//        bool _isStarted = false;

//        public override void OnCreate()
//        {
//            base.OnCreate();

//            try
//            {
//                _logger = ServiceStack.Instance.Container.Resolve<ILogService>();

//                _logger.LogDebug(Constants.Log_Tag, $"{nameof(LocationTrackingService)}::OnCreate");
//            }
//            catch (Exception ex)
//            {
//                _logger.LogHigh(Constants.Log_Tag, "LocationRequest::OnCreate error", ex);
//            }
//        }

//        public override StartCommandResult OnStartCommand(Intent intent, StartCommandFlags flags, int startId)
//        {
//            base.OnStartCommand(intent, flags, startId);

//            if (_isStarted)
//            {
//                return StartCommandResult.NotSticky;
//            }

//            _logger.LogWarn(Constants.Log_Tag, $"Starting {nameof(LocationTrackingService)}");

//            try
//            {
//                // Let's stop notifying people constantly that the app is running... that should be obvious...
//                StartForeground(1000, NotificationBuilder.CreateNotification("NCX", Properties.Resources.LocationTrackingNotificationText, NotificationBuilder.DebugChannel));
//            }
//            catch (Exception ex)
//            {
//                _logger.LogHigh(Constants.Log_Tag, $"{nameof(LocationTrackingService)}:OnStartCommand error", ex);
//            }

//            return StartCommandResult.Sticky;
//        }

//        public override void OnDestroy()
//        {
//            base.OnDestroy();

//            StopForeground(true);

//            _isStarted = false;
//        }

//        public override IBinder OnBind(Intent intent)
//        {
//            return null;
//        }
//    }
//}