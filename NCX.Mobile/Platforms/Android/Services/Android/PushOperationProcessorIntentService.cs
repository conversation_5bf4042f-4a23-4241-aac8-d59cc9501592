﻿using Android.App;
using Android.Content;
using Android.OS;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Maui;
using NCX.Logging.Interfaces;
using NCX.Mobile.Helpers;
using NCX.Mobile.Services;
using System;
using System.Threading.Tasks;
using Microsoft.AppCenter.Crashes;
using NCX.Mobile.Droid.Helpers;
using NCX.Mobile.Droid.Services.Android;
using NCX.Mobile.Platforms.Android.Helpers;

namespace NCX.Mobile.Platforms.Android.Services
{
    [Service(Exported = true)]
    public class PushOperationProcessorService : Service
    {
        ILogService _logger;
        volatile int _runCount;

        public override void OnCreate()
        {
            base.OnCreate();
            // Resolve services using MAUI's DI container
            var provider = MauiApplication.Current.Services;
            _logger = provider.GetService<ILogService>();
            _logger?.LogDebug(Constants.Log_Tag, $"{nameof(PushOperationProcessorService)} created");
        }

        public override StartCommandResult OnStartCommand(Intent intent, StartCommandFlags flags, int startId)
        {
            lock (this)
            {
                ++_runCount;
                if (_runCount > 1)
                {
                    _logger.LogDebug(Constants.Log_Tag,
                        $"{nameof(PushOperationProcessorService)}::OnStartCommand intent already running, _runCount: {_runCount}");
                    return StartCommandResult.NotSticky;
                }
            }

            // Start the service as a foreground service with a notification.
            StartForeground(1001, NotificationBuilder.CreateNotification("NCX", "Processing push operations", NotificationBuilder.DebugChannel));

            // Run the cache processor in the background.
            Task.Run(() => RunCacheProcessor());

            return StartCommandResult.Sticky;
        }

        public override IBinder OnBind(Intent intent) => null;

        void RunCacheProcessor()
        {
            try
            {
                _logger.LogDebug(Constants.Log_Tag, $"{nameof(PushOperationProcessorService)}::RunCacheProcessor");
                // Resolve and run the cache processor helper
                bool shouldReschedule = MauiApplication.Current.Services
                    .GetService<CacheProcessorHelper>()?.RunCacheProcessor() ?? false;

                if (shouldReschedule)
                    PushOperationProcessorScheduledTaskService.ScheduleRun();
                else
                    PushOperationProcessorScheduledTaskService.CancelScheduleRun();

                _logger.LogDebug(Constants.Log_Tag, $"{nameof(PushOperationProcessorService)}::RunCacheProcessor FINISHED");
            }
            catch (Exception ex)
            {
                // Use AppCenter Crashes (or replace with your crash reporter)
                Crashes.TrackError(ex);
                _logger.LogWarn(Constants.Log_Tag,
                    $"{nameof(PushOperationProcessorService)}::RunCacheProcessor error thrown", ex);
            }

            bool shouldRunAgain = false;
            lock (this)
            {
                --_runCount;
                if (_runCount > 0)
                    shouldRunAgain = true;
                else
                    StopForeground(true);
            }
            if (shouldRunAgain)
                RunCacheProcessor();
        }
    }
}
