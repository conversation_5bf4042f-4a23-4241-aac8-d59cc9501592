using System;
using System.Threading.Tasks;
using Android.App;
using Android.Content;
using Firebase.Crashlytics;
using NCX.Logging.Interfaces;
using NCX.Mobile.Services;
using Java.Lang; // For Java.Lang.Class and Exception
// Alias Android.App.Application to avoid ambiguity with Microsoft.Maui.Controls.Application
using AndroidApp = Android.App.Application;

namespace NCX.Mobile.Droid.Services
{
    public class PushOperationProcessorService : IPushOperationProcessorService
    {
        private bool isStarted = false;
        private readonly ILogService _logService;

        public PushOperationProcessorService(ILogService logService)
        {
            _logService = logService;
        }

        public Task StartAsync()
        {
            if (isStarted)
                return Task.CompletedTask;

            try
            {
                // Convert the type to a Java.Lang.Class
                var intent = new Intent(AndroidApp.Context, Class.FromType(typeof(PushOperationProcessorMessagingService)));
                AndroidApp.Context.StartService(intent);
                isStarted = true;
            }
            catch (System.Exception ex)
            {
                FirebaseCrashlytics.Instance.RecordException(new Java.Lang.Exception(ex.Message));
                _logService.LogCritical("NCX_LOG", $"{nameof(PushOperationProcessorService)} Start failed", ex);
                throw;
            }

            return Task.CompletedTask;
        }

        public Task StopAsync() => Task.CompletedTask;
    }
}
