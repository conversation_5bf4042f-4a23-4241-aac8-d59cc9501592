﻿using NCX.Logging.Enums;
using NCX.Logging.Interfaces;
using NCX.Logging.Models;

namespace NCX.Mobile.Droid.Services
{
    class LogToServerLogCommand : ILogCommand
    {
        public bool CanExecute(LogParameter parameter)
        {
            return true;
        }

        public void Execute(LogParameter parameter)
        {
            switch (parameter.Type)
            {
                case LogType.Track:
                    Track(parameter);
                    break;
                default:
                    Log(parameter);
                    break;
            }
        }

        void Log(LogParameter parameter)
        {
            // TODO: Update Bugfender use to use App Center
            //switch (parameter.Priority)
            //{
            //    case LogPriority.Low:
            //        Bugfender.D(parameter.Source, GetLogMessage(parameter));
            //        break;
            //    case LogPriority.Medium:
            //        Bugfender.D(parameter.Source, GetLogMessage(parameter));
            //        break;
            //    case LogPriority.High:
            //        Bugfender.W(parameter.Source, GetLogMessage(parameter));
            //        break;
            //    case LogPriority.Critical:
            //        Bugfender.E(parameter.Source, GetLogMessage(parameter));
            //        break;
            //}
        }

        void Track(LogParameter parameter)
        {
            // Set BugFender associated device information

            if (!TrySetDeviceString(
                parameter,
                new string[] { "server", "userid", "username", "fullname" }))
            {
                // Otherwise, use usual log
                Log(parameter);
            }
        }

        static bool TrySetDeviceString(LogParameter parameter, string[] keys)
        {
            bool hasDeviceAssocInfo = false;

            foreach (string key in keys)
            {
                hasDeviceAssocInfo = hasDeviceAssocInfo || TrySetDeviceString(parameter, key);
            }

            return hasDeviceAssocInfo;
        }

        static bool TrySetDeviceString(LogParameter parameter, string key)
        {
            if (parameter.Data.TryGetValue(key, out string keyValue))
            {
                // TODO: Update Bugfender use to AppCenter or other
                //Bugfender.SetDeviceString(key, keyValue);

                return true;
            }

            return false;
        }

        static string GetLogMessage(LogParameter parameter)
        {
            string s = string.Empty;

            if (parameter.Message != null)
            {
                s += parameter.Message;
            }

            if (parameter.Exception != null)
            {
                s += parameter.Exception.ToString();
            }

            return s;
        }
    }
}