﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Maui;
using NCX.Logging.Interfaces;
using System;

namespace NCX.Mobile.Platforms.Android.Services
{
    public static class PushOperationProcessorScheduledTaskService
    {
        public static void ScheduleRun()
        {
            // In an FCM-based solution, scheduling is done on the backend.
            // Here we log that scheduling was requested.
            var logger = MauiApplication.Current.Services.GetService<ILogService>();
            logger?.LogDebug("NCX", "ScheduleRun called – no local scheduling needed in FCM migration.");
        }

        public static void CancelScheduleRun()
        {
            // Similarly, canceling local scheduling isn’t needed.
            var logger = MauiApplication.Current.Services.GetService<ILogService>();
            logger?.LogDebug("NCX", "CancelScheduleRun called – no local scheduling needed in FCM migration.");
        }
    }
}
