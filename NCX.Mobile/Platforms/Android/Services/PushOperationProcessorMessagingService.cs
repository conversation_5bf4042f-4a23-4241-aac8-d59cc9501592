﻿using Android.App;
using Android.Content;
using Firebase.Messaging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Maui;
using NCX.Logging.Interfaces;
using NCX.Mobile.Services;
using System;
using Firebase.Crashlytics;
using Java.Lang;  // For Java.Lang.Exception alias

namespace NCX.Mobile.Droid.Services
{
    [Service(Exported = true)]
    [IntentFilter(new[] { "com.google.firebase.MESSAGING_EVENT" })]
    public class PushOperationProcessorMessagingService : FirebaseMessagingService
    {
        private ILogService _logger;
        private ICacheProcessorHelper _cacheProcessorHelper; // Ensure this is implemented and registered

        public override void OnCreate()
        {
            base.OnCreate();
            var provider = MauiApplication.Current.Services;
            _logger = provider.GetService<ILogService>();
            _cacheProcessorHelper = provider.GetService<ICacheProcessorHelper>();
            _logger?.LogDebug("PushProcessor", "PushOperationProcessorMessagingService created");
        }

        public override void OnMessageReceived(RemoteMessage message)
        {
            _logger?.LogDebug("PushProcessor", "FCM message received");

            try
            {
                // Check if the message contains a data payload with key "runCacheProcessor" set to "true"
                if (message.Data != null &&
                    message.Data.TryGetValue("runCacheProcessor", out string runFlag) &&
                    runFlag.Equals("true", StringComparison.OrdinalIgnoreCase))
                {
                    RunCacheProcessor();
                }
            }
            catch (System.Exception ex)
            {
                FirebaseCrashlytics.Instance.RecordException(new Java.Lang.Exception(ex.Message));
                _logger?.LogHigh("PushProcessor", "Error processing FCM message", ex);
            }
        }

        private void RunCacheProcessor()
        {
            try
            {
                _logger?.LogDebug("PushProcessor", "Running cache processor");
                bool shouldReschedule = _cacheProcessorHelper.RunCacheProcessor();
                _logger?.LogDebug("PushProcessor", "Cache processor finished, reschedule: " + shouldReschedule);
                // Optionally, notify your backend if rescheduling is needed.
            }
            catch (System.Exception ex)
            {
                FirebaseCrashlytics.Instance.RecordException(new Java.Lang.Exception(ex.Message));
                _logger?.LogHigh("PushProcessor", "Error running cache processor", ex);
            }
        }
    }
}
