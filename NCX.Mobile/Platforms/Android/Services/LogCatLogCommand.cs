﻿using Android.Util;
using NCX.Logging.Interfaces;
using NCX.Logging.Models;

namespace NCX.Mobile.Droid.Services
{
    // This class implements a LogProcessor Log Command 
    // That uses Android's LogCat 
    class LogCatLogCommand : ILogCommand
    {
        public bool CanExecute(LogParameter parameter)
        {
            return true;
        }

        public void Execute(LogParameter parameter)
        {
            switch (parameter.Priority)
            {
                case Logging.Enums.LogPriority.Low:
                    Log.Debug(parameter.Source, GetLogMessage(parameter));
                    break;
                case Logging.Enums.LogPriority.Medium:
                    Log.Info(parameter.Source, GetLogMessage(parameter));
                    break;
                case Logging.Enums.LogPriority.High:
                    Log.Warn(parameter.Source, GetLogMessage(parameter));
                    break;
                case Logging.Enums.LogPriority.Critical:
                    Log.Error(parameter.Source, GetLogMessage(parameter));
                    break;
            }
        }

        static string GetLogMessage(LogParameter parameter)
        {
            string s = string.Empty;

            if (parameter.Message != null)
            {
                s += parameter.Message;
            }

            if (parameter.Exception != null)
            {
                s += parameter.Exception.ToString();
            }

            return s;
        }
    }
}