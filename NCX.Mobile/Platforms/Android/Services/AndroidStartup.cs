﻿using Microsoft.Extensions.DependencyInjection;
using NCX.Mobile.Droid.Helpers;
using NCX.Mobile.Platforms.Android.Helpers;
using NCX.Mobile.Services;
using Shiny.Notifications;

namespace NCX.Mobile.Droid.Services
{
    public static class AndroidStartup
    {
        public static void ConfigureAndroidServices(IServiceCollection services)
        {
            // Register your Android-specific services
            services.AddSingleton<IPushOperationProcessorService, PushOperationProcessorService>();
            services.AddSingleton<IPlatformTools, PlatformTools>();

            // Register the notification channel using Shiny's Channel model
            services.AddSingleton(new Channel
            {
                Identifier = NotificationBuilder.UrgentChannel,
                Description = "Urgent",
                Importance = ChannelImportance.High
            });
        }
    }
}
