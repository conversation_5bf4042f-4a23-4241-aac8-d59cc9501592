﻿using System;
using Android.App;
using Firebase.Iid;
using NCX.Mobile.Droid.Helpers;
using NCX.Mobile.Services;
using AndroidApp = Android.App.Application;
using NCX.Mobile.Helpers; // Ensure this contains your Constants class

namespace NCX.Mobile.Droid.Services
{
    class PlatformTools : IPlatformTools
    {
        public TargetPlatform CurrentPlatform => TargetPlatform.Android;

        public Version AppVersion => Version.Parse(AndroidApp.Context.PackageManager.GetPackageInfo(AndroidApp.Context.PackageName, 0).VersionName);

        public Uri AppStoreUri => new Uri(Constants.AppStoreUriAndroid);

        public void InvokeOnMainThread(Action a)
        {
            ThreadingTools.BeginInvokeOnMainThread(a);
        }

        public string PushNotificationToken => FirebaseInstanceId.Instance.Token;
    }
}
