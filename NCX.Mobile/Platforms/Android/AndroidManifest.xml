﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" package="com.ncx.mobile">
	<!-- Permissions -->
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
	<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
	<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
	<uses-permission android:name="android.permission.CAMERA" />
	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
	<uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
	<uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
	<uses-permission android:name="android.permission.WAKE_LOCK" />
	<!-- Add these permissions for Android 11+ -->
	<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
	<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
	<uses-feature android:name="android.hardware.camera" android:required="false" />
	<uses-feature android:name="android.hardware.location.gps" android:required="false" />

	<queries>
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE" />
        </intent>
        <intent>
            <action android:name="android.intent.action.GET_CONTENT" />
        </intent>
        <intent>
            <action android:name="android.intent.action.PICK" />
        </intent>
    </queries>

	<application android:name="ncx.mobile.MainApplication" android:label="National Carrier Exchange" android:allowBackup="false" android:networkSecurityConfig="@xml/network_security_config">
		<meta-data android:name="com.google.android.geo.API_KEY" android:value="AIzaSyAD3uV17U8JbcQ4L9rCxt6Z3MbSZ0EErCE" />
		<!-- FileProvider using AndroidX -->
		<provider android:name="androidx.core.content.FileProvider" android:authorities="com.ncx.mobile.fileprovider" android:exported="false" android:grantUriPermissions="true">
			<meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/file_paths" />
		</provider>
		<meta-data android:name="com.google.android.gms.version" android:value="@integer/google_play_services_version" />
		<!-- Shiny Broadcast Receiver (if required) -->
		<receiver android:name="com.shiny.locations.MotionActivityBroadcastReceiver" android:exported="true" />
		<!-- Default Notification Icon for FCM -->
		<meta-data android:name="com.google.firebase.messaging.default_notification_icon" android:resource="@drawable/notification_icon" />
	</application>
</manifest>




