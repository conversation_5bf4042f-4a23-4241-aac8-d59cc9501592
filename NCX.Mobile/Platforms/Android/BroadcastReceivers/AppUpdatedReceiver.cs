﻿//using System;
//using Android.App;
//using Android.Content;
//using NCX.Logging.Interfaces;
//using NCX.Mobile.Droid.Services.Android;
//using NCX.Mobile.Helpers;
//using NCX.Mobile.Services;
//using Prism.Ioc;

//namespace NCX.Mobile.Droid.BroadcastReceivers
//{
//    [BroadcastReceiver]
//    [IntentFilter(new[] { Intent.ActionMyPackageReplaced })]
//    class AppUpdatedReceiver : BroadcastReceiver
//    {
//        public override void OnReceive(Context context, Intent intent)
//        {
//            ILogService logger = ServiceStack.Instance.Container.Resolve<ILogService>();

//            logger.LogDebug(Constants.Log_Tag, "ActionMyPackageReplaced received");

//            try
//            {
//                ServiceStack.Instance.Container.Resolve<IAppController>().OnStart();
//            }
//            catch (Exception ex)
//            {
//                logger.LogHigh(Constants.Log_Tag, "Error in AppUpdatedReceiver:OnReceive", ex);
//            }
//        }
//    }
//}