//using Android.App;
//using Android.Content;
//using Prism.Ioc;
//using NCX.Logging.Interfaces;
//using NCX.Mobile.Helpers;
//using NCX.Mobile.Services;

//namespace NCX.Mobile.Droid.BroadcastReceivers
//{
//    [BroadcastReceiver]
//    [IntentFilter(new[] { Intent.ActionBootCompleted }, Priority = (int)IntentFilterPriority.LowPriority)]
//    class BootCompletedReceiver : BroadcastReceiver
//    {
//        public override void OnReceive(Context context, Intent intent)
//        {
//            ILogService logger = ServiceStack.Instance.Container.Resolve<ILogService>();

//            logger.LogDebug(Constants.Log_Tag, "Boot completed received");

//            ServiceStack.Instance.Container.Resolve<IAppController>().OnStart();
//        }
//    }
//}