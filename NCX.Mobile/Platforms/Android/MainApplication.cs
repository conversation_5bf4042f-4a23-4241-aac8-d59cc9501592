﻿using System;
using System.Threading.Tasks;
using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Util;
using Java.Interop;
using Microsoft.Maui;
using Microsoft.Maui.Hosting;

namespace NCX.Mobile
{
#if DEBUG
    [Application(Debuggable = true)]
#else
    [Application(Debuggable = false)]
#endif
    public class MainApplication : MauiApplication
    {
        public MainApplication(IntPtr handle, JniHandleOwnership transfer)
            : base(handle, transfer)
        {
        }

        public override void OnCreate()
        {
            base.OnCreate();

            // Subscribe to unhandled exception events
            AndroidEnvironment.UnhandledExceptionRaiser += AndroidEnvironment_UnhandledException;
            AppDomain.CurrentDomain.UnhandledException += AppDomain_UnhandledException;
            TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;

            Log.Info("NCX", "Application started");
        }

        protected override MauiApp CreateMauiApp() => MauiProgram.CreateMauiApp();

        private void AndroidEnvironment_UnhandledException(object sender, RaiseThrowableEventArgs args) =>
            HandleException(args.Exception, nameof(AndroidEnvironment_UnhandledException));

        private void AppDomain_UnhandledException(object sender, UnhandledExceptionEventArgs args) =>
            HandleException(args.ExceptionObject as Exception, nameof(AppDomain_UnhandledException));

        private void TaskScheduler_UnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs args) =>
            HandleException(args.Exception, nameof(TaskScheduler_UnobservedTaskException));

        private void HandleException(Exception ex, string fromHandler)
        {
            try
            {
#if DEBUG
                // Optionally break for debugging:
                // System.Diagnostics.Debugger.Break();
                var toString = ex.ToString();
#endif
                if (ex is AggregateException ae)
                {
                    ex = ae.InnerExceptions[0];
                }
                Console.WriteLine($"[{fromHandler}] {ex}");
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }
    }
}
