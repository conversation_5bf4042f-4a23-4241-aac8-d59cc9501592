﻿using Foundation;
using Microsoft.AppCenter.Crashes;
using System;
using System.Collections.Generic;
using UIKit;
using UserNotifications;

namespace NCX.Mobile
{
    [Register("AppDelegate")]
    public class AppDelegate : MauiUIApplicationDelegate, IUNUserNotificationCenterDelegate
    {
        protected override MauiApp CreateMauiApp() => MauiProgram.CreateMauiApp();

        public override bool FinishedLaunching(UIApplication uiApplication, NSDictionary launchOptions)
        {
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            
            // Base implementation must be called first
            var result = base.FinishedLaunching(uiApplication, launchOptions);

            // Handle notifications from launch options
            HandleLaunchOptions(launchOptions);
            
            // Register for notifications using the modern UNUserNotificationCenter
            RegisterForModernNotifications(uiApplication);

            return result;
        }

        private void HandleLaunchOptions(NSDictionary launchOptions)
        {
            if (launchOptions == null) return;

            // Check for remote notification
            if (launchOptions.ContainsKey(UIApplication.LaunchOptionsRemoteNotificationKey))
            {
                NSDictionary remoteNotification = launchOptions[UIApplication.LaunchOptionsRemoteNotificationKey] as NSDictionary;
                // Process the notification payload
            }
        }

        private void RegisterForModernNotifications(UIApplication uiApplication)
        {
            // Request authorization for notifications
            UNUserNotificationCenter.Current.RequestAuthorization(
                UNAuthorizationOptions.Alert | UNAuthorizationOptions.Badge | UNAuthorizationOptions.Sound,
                (approved, error) =>
                {
                    if (approved)
                    {
                        // Set the delegate to handle notifications
                        UNUserNotificationCenter.Current.Delegate = this;
                        
                        // Register for remote notifications
                        InvokeOnMainThread(() => {
                            uiApplication.RegisterForRemoteNotifications();
                        });
                    }
                });
        }

        // Handle device token registration
        [Export("application:didRegisterForRemoteNotificationsWithDeviceToken:")]
        public void DidRegisterForRemoteNotifications(UIApplication application, NSData deviceToken)
        {
            var token = BitConverter.ToString(deviceToken.ToArray())
                .Replace("-", "")
                .ToLowerInvariant();

            // Save token to NSUserDefaults
            NSUserDefaults.StandardUserDefaults.SetString(token, "PushDeviceToken");
            
            // Here you would send the token to your server
            // Implement your service call here
        }

        // Handle registration failure
        [Export("application:didFailToRegisterForRemoteNotificationsWithError:")]
        public void DidFailToRegisterForRemoteNotifications(UIApplication application, NSError error)
        {
            Console.WriteLine($"Failed to register for remote notifications: {error.LocalizedDescription}");
        }

        // Handle remote notification reception
        [Export("application:didReceiveRemoteNotification:fetchCompletionHandler:")]
        public void DidReceiveRemoteNotification(UIApplication application, NSDictionary userInfo, Action<UIBackgroundFetchResult> completionHandler)
        {
            // Process the notification when the app is in the foreground
            
            completionHandler(UIBackgroundFetchResult.NewData);
        }

        // UNUserNotificationCenterDelegate methods
        [Export("userNotificationCenter:willPresentNotification:withCompletionHandler:")]
        public void WillPresentNotification(UNUserNotificationCenter center, UNNotification notification, Action<UNNotificationPresentationOptions> completionHandler)
        {
            // Show the notification even when the app is in the foreground
            completionHandler(UNNotificationPresentationOptions.Banner | 
                             UNNotificationPresentationOptions.Sound | 
                             UNNotificationPresentationOptions.Badge);
        }

        [Export("userNotificationCenter:didReceiveNotificationResponse:withCompletionHandler:")]
        public void DidReceiveNotificationResponse(UNUserNotificationCenter center, UNNotificationResponse response, Action completionHandler)
        {
            // Handle notification response when user taps on notification
            var userInfo = response.Notification.Request.Content.UserInfo;
            
            // Process the notification data
            
            completionHandler();
        }

        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception ex)
            {
                Crashes.TrackError(ex, new Dictionary<string, string> { { "action", "UnobservedException" } });
            }
        }
    }
}
