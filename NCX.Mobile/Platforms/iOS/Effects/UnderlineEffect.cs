using Foundation;
using Microsoft.Maui.Controls.Platform;
using Microsoft.Maui.Platform;
using System;
using UIKit;
using Microsoft.Maui.Controls;

[assembly: ResolutionGroupName(NCX.Mobile.Effects.UnderlineEffect.EffectNamespace)]
[assembly: ExportEffect(typeof(NCX.Mobile.Platforms.iOS.Effects.UnderlineEffect), "UnderlineEffect")]
namespace NCX.Mobile.Platforms.iOS.Effects
{
    public class UnderlineEffect : PlatformEffect
    {
        protected override void OnAttached()
        {
            SetUnderline(true);
        }

        protected override void OnDetached()
        {
            SetUnderline(false);
        }

        protected override void OnElementPropertyChanged(System.ComponentModel.PropertyChangedEventArgs args)
        {
            base.OnElementPropertyChanged(args);

            if (args.PropertyName == Label.TextProperty.PropertyName || args.PropertyName == Label.FormattedTextProperty.PropertyName)
            {
                SetUnderline(true);
            }
        }

        private void SetUnderline(bool underlined)
        {
            try
            {
                if (Control is UILabel label && label.AttributedText != null)
                {
                    var text = new NSMutableAttributedString(label.AttributedText);
                    var range = new NSRange(0, text.Length);

                    if (underlined)
                    {
                        text.AddAttribute(UIStringAttributeKey.UnderlineStyle, NSNumber.FromInt32((int)NSUnderlineStyle.Single), range);
                    }
                    else
                    {
                        text.RemoveAttribute(UIStringAttributeKey.UnderlineStyle, range);
                    }
                    
                    label.AttributedText = text;
                }
            }
            catch (Exception ex)
            {
                Microsoft.AppCenter.Crashes.Crashes.TrackError(ex);
                Console.WriteLine("Cannot underline Label. Error: " + ex.Message);
            }
        }
    }
}
