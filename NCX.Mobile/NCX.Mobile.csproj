﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<!-- <TargetFrameworks>net8.0-ios;net8.0-maccatalyst;net8.0-android34.0</TargetFrameworks> -->
		<!-- Temporarily removing maccatalyst due to Plugin.Firebase not supporting it -->
		<TargetFrameworks>net8.0-ios</TargetFrameworks>
		<!-- <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net8.0-windows10.0.19041.0</TargetFrameworks> -->
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net8.0-tizen</TargetFrameworks> -->

		<!-- Note for MacCatalyst:
		The default runtime is maccatalyst-x64, except in Release config, in which case the default is maccatalyst-x64;maccatalyst-arm64.
		When specifying both architectures, use the plural <RuntimeIdentifiers> instead of the singular <RuntimeIdentifier>.
		The Mac App Store will NOT accept apps with ONLY maccatalyst-arm64 indicated;
		either BOTH runtimes must be indicated or ONLY macatalyst-x64. -->
		<!-- For example: <RuntimeIdentifiers>maccatalyst-x64;maccatalyst-arm64</RuntimeIdentifiers> -->

		<OutputType>Exe</OutputType>
		<RootNamespace>NCX.Mobile</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<!-- Define conditional compilation symbols -->
		<DefineConstants Condition="$(TargetFramework.Contains('-android'))">$(DefineConstants);ANDROID</DefineConstants>
		<DefineConstants Condition="$(TargetFramework.Contains('-ios'))">$(DefineConstants);IOS</DefineConstants>
		<DefineConstants Condition="$(TargetFramework.Contains('-maccatalyst'))">$(DefineConstants);MACCATALYST</DefineConstants>
		<DefineConstants Condition="$(TargetFramework.Contains('-windows'))">$(DefineConstants);WINDOWS</DefineConstants>

		<!-- Display name -->
		<ApplicationTitle>NCX.Mobile</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.ncx.mobile</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>3.0.4</ApplicationDisplayVersion>
		<ApplicationVersion>305</ApplicationVersion>

		<NullabilityInfoContextSupport>true</NullabilityInfoContextSupport>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">11.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">13.1</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
		<!-- <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion> -->
		<!-- <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion> -->
		<!-- <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion> -->
	</PropertyGroup>

	<PropertyGroup Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">
		<!-- Debug configuration for simulator -->
		<CodesignKey>-</CodesignKey>
		<CodesignProvision>-</CodesignProvision>
		<CodesignEntitlements>Platforms/iOS/Entitlements.plist</CodesignEntitlements>
		<MtouchLink>None</MtouchLink>
		<MtouchDebug>true</MtouchDebug>
		<MtouchEnableSGenConc>true</MtouchEnableSGenConc>
		<MtouchHttpClientHandler>NSUrlSessionHandler</MtouchHttpClientHandler>
		<CreatePackage>false</CreatePackage>
		
		<!-- Use this for simulator -->
		<RuntimeIdentifier>iossimulator-x64</RuntimeIdentifier>
		
		<!-- Define constants similar to your old project -->
		<DefineConstants Condition="'$(Configuration)' == 'Debug'">$(DefineConstants);ENABLE_TEST_CLOUD</DefineConstants>
	</PropertyGroup>

	<ItemGroup Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">
		<GoogleServicesJson Include="Platforms\Android\google-services.json" />
		<MauiFont Include="Resources\Fonts\icons.ttf" />
		<PackageReference Include="Plugin.Firebase" Version="3.1.3" />
		<PackageReference Include="Plugin.Firebase.Crashlytics" Version="3.1.1" />
	</ItemGroup>

	<ItemGroup Condition="'$(TargetFramework)' == 'net8.0-ios'">
		<BundleResource Include="Platforms\iOS\GoogleService-Info.plist" />
		<PackageReference Include="Plugin.Firebase" Version="3.1.3" />
		<PackageReference Include="Plugin.Firebase.Crashlytics" Version="3.1.1" />
	</ItemGroup>

	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#323538" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.png" Color="#323538" BaseSize="2442,984" />
		
		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />
		<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="FFImageLoading.Maui" Version="1.2.7" />
		<PackageReference Include="Humanizer.Core" Version="2.14.1" />
		<PackageReference Include="Microsoft.AppCenter.Analytics" Version="5.0.6" />
		<PackageReference Include="Microsoft.AppCenter.Crashes" Version="5.0.6" />
		<PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.1" />
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.1" />
		<PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.1" />
		<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.1" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.1" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="8.0.100" />
		<PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="8.0.100" />
		<PackageReference Include="Microsoft.Maui.Controls.Maps" Version="8.0.100" />
		<PackageReference Include="Microsoft.Rest.ClientRuntime" Version="2.3.24" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Prism.Core" Version="9.0.537" />
		<PackageReference Include="Prism.DryIoc.Maui" Version="9.0.537" />
		<PackageReference Include="Prism.Maui" Version="9.0.537" />
		<PackageReference Include="ReactiveUI" Version="20.1.63" />
		<PackageReference Include="Refit" Version="8.0.0" />
		<PackageReference Include="Refractored.MvvmHelpers" Version="1.6.2" />
		<PackageReference Include="Shiny.Hosting.Maui" Version="3.3.4" />
		<PackageReference Include="Shiny.Jobs" Version="3.3.4" />
		<PackageReference Include="Shiny.Locations" Version="3.3.4" />
		<PackageReference Include="SixLabors.ImageSharp.Drawing" Version="2.1.5" />
		<PackageReference Include="sqlite-net-pcl" Version="1.9.172" />
		<PackageReference Include="System.Threading.Tasks.Extensions" Version="4.6.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\JsonApiNETPCL\JsonApiNETPCL.csproj" />
		<ProjectReference Include="..\NCX.Logging.AppCenter\NCX.Logging.AppCenter.csproj" />
		<ProjectReference Include="..\NCX.Logging\NCX.Logging.csproj" />
		<ProjectReference Include="..\NCX.Mobile.IFTA\NCX.Mobile.IFTA.csproj" />
		<ProjectReference Include="..\NCX.Mobile.Abstractions\NCX.Mobile.Abstractions.csproj" />
		<ProjectReference Include="..\NCX.Mobile.Forms\NCX.Mobile.Forms.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <Compile Update="Views\MainMasterDetailPage.xaml.cs">
	    <DependentUpon>MainMasterDetailPage.xaml</DependentUpon>
	  </Compile>
	</ItemGroup>

	<!-- Target needed until LinkWithSwiftSystemLibraries makes it into the SDK: https://github.com/xamarin/xamarin-macios/pull/20463 -->
    <Target Name="LinkWithSwift" DependsOnTargets="_ParseBundlerArguments;_DetectSdkLocations" BeforeTargets="_LinkNativeExecutable">
        <PropertyGroup>
            <_SwiftPlatform Condition="$(RuntimeIdentifier.StartsWith('iossimulator-'))">iphonesimulator</_SwiftPlatform>
            <_SwiftPlatform Condition="$(RuntimeIdentifier.StartsWith('ios-'))">iphoneos</_SwiftPlatform>
        </PropertyGroup>
        <ItemGroup>
            <_CustomLinkFlags Include="-L" />
            <_CustomLinkFlags Include="/usr/lib/swift" />
            <_CustomLinkFlags Include="-L" />
            <_CustomLinkFlags Include="$(_SdkDevPath)/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/$(_SwiftPlatform)" />
            <_CustomLinkFlags Include="-Wl,-rpath" />
            <_CustomLinkFlags Include="-Wl,/usr/lib/swift" />
        </ItemGroup>
    </Target>
</Project>
