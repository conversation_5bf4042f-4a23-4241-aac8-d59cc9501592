<?xml version = "1.0" encoding = "UTF-8" ?>
<Application xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             xmlns:converters="clr-namespace:NCX.Mobile.Converters;assembly=NCX.Mobile.Forms"
             xmlns:controls="clr-namespace:NCX.Mobile.Controls;assembly=NCX.Mobile.Forms"
             xmlns:helpers="clr-namespace:NCX.Mobile.Helpers;assembly=NCX.Mobile.Forms"
             x:Class="NCX.Mobile.App">
    <Application.Resources>
        <ResourceDictionary>
            <!-- Colors -->
            <Color x:Key="SecondaryColor">#D0021B</Color>
            <Color x:Key="RowAlternateColor">#FBFBFB</Color>
            <Color x:Key="SeparatorColor">#E0E0E0</Color>
            <Color x:Key="SeparatorColor2">#E9E9E9</Color>
            <Color x:Key="LightTextColor">#7E7E7E</Color>
            <Color x:Key="RowHeaderColor">#7E7E7E</Color>
            <Color x:Key="IconColor">#B1B1B1</Color>
            <Color x:Key="NavBarColor">#323538</Color>
            <Color x:Key="BtnFormColor">#81BE40</Color>
            <Color x:Key="BtnDisabledColor">#999999</Color>
            <Color x:Key="StatusPendingColor">#6C6C6C</Color>
            <Color x:Key="StatusEnRouteColor">#81BE40</Color>
            <Color x:Key="StatusCheckedInColor">#81BE40</Color>
            <Color x:Key="StatusCompletedColor">#81BE40</Color>
            <Color x:Key="HyperlinkColor">#01BAEF</Color>
            <Color x:Key="LoadOperationPickupStatusColor">#01BAEF</Color>
            <Color x:Key="LoadOperationDropoffStatusColor">#81BE40</Color>
            <Color x:Key="AccentColor2">#01BAEF</Color>
            <Color x:Key="FormBkgndLabelColor">#E9E9E9</Color>

            <OnPlatform x:Key="IconsFontFamily" x:TypeArguments="x:String">
                <On Platform="Android" Value="icons.ttf#Icons" />
                <On Platform="iOS" Value="FontAwesome" />
                <On Platform="WinUI" Value="Assets/Fonts/icons.ttf#Icons" />
            </OnPlatform>

            <!-- Common controls styles -->
            <Style TargetType="Grid">
                <Setter Property="Padding" Value="0" />
                <Setter Property="RowSpacing" Value="0" />
                <Setter Property="ColumnSpacing" Value="0" />
            </Style>
            <Style TargetType="StackLayout">
                <Setter Property="Padding" Value="0" />
                <Setter Property="Spacing" Value="0" />
            </Style>
            <Style TargetType="Frame">
                <Setter Property="Padding" Value="0" />
                <Setter Property="HasShadow" Value="False" />
            </Style>
            <Style TargetType="Entry">
                <Setter Property="TextColor" Value="#212121" />
            </Style>
            <Style TargetType="Label">
                <Setter Property="TextColor" Value="#212121" />
            </Style>
            <Style x:Key="BtnFormStyle" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{StaticResource BtnFormColor}" />
                <Setter Property="TextColor" Value="White" />
            </Style>
            <Style x:Key="BtnSecondaryFormStyle" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{StaticResource AccentColor2}" />
                <Setter Property="TextColor" Value="White" />
            </Style>
            <Style x:Key="BtnTransparentStyle" TargetType="Button">
                <Setter Property="BackgroundColor" Value="Transparent" />
                <Setter Property="BorderWidth" Value="0" />
                <Setter Property="CornerRadius" Value="0" />
            </Style>
            <Style x:Key="BtnIconStyle" TargetType="Button" BasedOn="{StaticResource BtnTransparentStyle}">
                <Setter Property="FontFamily" Value="{StaticResource IconsFontFamily}" />
                <Setter Property="FontSize" Value="24" />
                <Setter Property="TextColor" Value="#212121" />
                <Setter Property="WidthRequest" Value="45" />
                <Setter Property="HeightRequest" Value="45" />
            </Style>
            <Style x:Key="HeaderStyle" TargetType="Label">
                <Setter Property="TextColor" Value="#212121" />
                <Setter Property="FontSize" Value="Medium" />
            </Style>
            <Style x:Key="PageStyle" TargetType="Page">
                <Setter Property="BackgroundColor" Value="White" />
            </Style>
            <Style x:Key="SeparatorStyle" TargetType="BoxView">
                <Setter Property="HeightRequest" Value="1" />
                <Setter Property="BackgroundColor" Value="{StaticResource SeparatorColor}" />
            </Style>
            <Style x:Key="Separator2ColsStyle" TargetType="BoxView">
                <Setter Property="HeightRequest" Value="1" />
                <Setter Property="BackgroundColor" Value="{StaticResource SeparatorColor}" />
                <Setter Property="Grid.ColumnSpan" Value="2" />
            </Style>
            <Style x:Key="IconStyle" TargetType="Label">
                <Setter Property="FontFamily" Value="{StaticResource IconsFontFamily}" />
                <Setter Property="FontSize" Value="20" />
                <Setter Property="TextColor" Value="{StaticResource IconColor}" />
            </Style>
            <Style x:Key="PickupIconStyle" TargetType="Label" BasedOn="{StaticResource IconStyle}">
                <Setter Property="Text" Value="{x:Static helpers:FontIcons.UploadAlt}" />
            </Style>
            <Style x:Key="DropoffIconStyle" TargetType="Label" BasedOn="{StaticResource IconStyle}">
                <Setter Property="Text" Value="{x:Static helpers:FontIcons.DownloadAlt}" />
            </Style>
            <Style x:Key="ArrowIconStyle" TargetType="Label" BasedOn="{StaticResource IconStyle}">
                <Setter Property="Text" Value="{x:Static helpers:FontIcons.AngleRight}" />
                <Setter Property="FontSize" Value="30" />
                <Setter Property="Margin" Value="10, 0" />
                <Setter Property="VerticalOptions" Value="Center" />
                <Setter Property="HorizontalOptions" Value="EndAndExpand" />
            </Style>
            <Style x:Key="BusyIndicatorOverlayStyle" TargetType="BoxView">
                <Setter Property="Opacity" Value="0.7" />
                <Setter Property="Color" Value="White" />
            </Style>
            <Style x:Key="PopupOverlayStyle" TargetType="BoxView">
                <Setter Property="Opacity" Value="0.3" />
                <Setter Property="Color" Value="Black" />
            </Style>
            <Style x:Key="BtnReportDelayStyle" TargetType="Button" BasedOn="{StaticResource BtnFormStyle}">
                <Setter Property="BackgroundColor" Value="#FF8E01" />
            </Style>
            <Style x:Key="LabelHyperlinkStyle" TargetType="Label">
                <Setter Property="TextColor" Value="{StaticResource HyperlinkColor}" />
                <Setter Property="Margin" Value="0, 7" />
                <Setter Property="VerticalTextAlignment" Value="Center" />
            </Style>
            <Style x:Key="ButtonHyperlinkStyle" TargetType="Button">
                <Setter Property="TextColor" Value="{StaticResource HyperlinkColor}" />
                <Setter Property="BackgroundColor" Value="Transparent" />
                <!-- Attach property changed syntax for MAUI if needed -->
            </Style>
            <Style TargetType="controls:DropdownListViewItemView">
                <Setter Property="ControlTemplate">
                    <ControlTemplate>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <ContentPresenter HorizontalOptions="StartAndExpand" />
                            <Label x:Name="_arrowIcon"
                       Text="{x:Static helpers:FontIcons.AngleRight}"
                       Style="{StaticResource IconStyle}"
                       Grid.Column="1"
                       HorizontalOptions="End"
                       VerticalOptions="Center"
                       Margin="0, 0, 28, 0"
                       BackgroundColor="Transparent" />
                        </Grid>
                    </ControlTemplate>
                </Setter>
            </Style>

            <converters:ObjectToBoolConverter x:Key="ObjectToBoolConverter" />
            <converters:ObjectToBoolInverseConverter x:Key="ObjectToBoolInverseConverter" />
            <converters:OperationAddressConverter x:Key="OperationAddressConverter" />
            <converters:LoadStatusTextConverter x:Key="LoadStatusTextConverter" />
            <converters:LoadStatusColorConverter x:Key="LoadStatusColorConverter" />
            <converters:ListViewAlternateBkgndColorConverter x:Key="ListViewAlternateBkgndColorConverter" />
            <converters:AddressConverter x:Key="AddressConverter" />
            <converters:RequiredTemperatureConverter x:Key="RequiredTemperatureConverter" />
            <converters:LoadOperationTypeToIconConverter x:Key="LoadOperationTypeToIconConverter" />
            <converters:LoadOperationStatusTextConverter x:Key="LoadOperationStatusTextConverter" />
            <converters:LoadOperationStatusColorConverter x:Key="LoadOperationStatusColorConverter" />
            <converters:BackendDescriptionConverter x:Key="BackendDescriptionConverter" />
            <converters:LoadOperationStatusIconConverter x:Key="LoadOperationStatusIconConverter" />
            <converters:LoadOperationTypeToColorConverter x:Key="LoadOperationTypeToColorConverter" />
            <converters:BoolToTextConverter x:Key="BoolToTextConverter" />
            <converters:LoadOperationDetailsHeaderTextConverter x:Key="LoadOperationDetailsHeaderTextConverter" />
            <converters:LoadOperationDetailsTitleTextConverter x:Key="LoadOperationDetailsTitleTextConverter" />
            <converters:CargoItemWeightConverter x:Key="CargoItemWeightConverter" />
            <converters:LoadOperationOperatingHoursConverter x:Key="LoadOperationOperatingHoursConverter" />
            <converters:CargoItemHeaderTextConverter x:Key="CargoItemHeaderTextConverter" />
            <converters:ExpectedDateConverter x:Key="ExpectedDateConverter" />
            <converters:PhotoViewerStatusTextConverter x:Key="PhotoViewerStatusTextConverter" />
            <converters:LastReportedDelayTextConverter x:Key="LastReportedDelayTextConverter" />
            <converters:LoadOperationCountTextConverter x:Key="LoadOperationCountTextConverter" />
            <converters:EquipmentTypeConverter x:Key="EquipmentTypeConverter" />
            <converters:CargoUnitTypeConverter x:Key="CargoUnitTypeConverter" />
            <converters:LocationOperatingHoursDayConverter x:Key="LocationOperatingHoursDayConverter" />
            <converters:LoadGroupHeaderConverter x:Key="LoadGroupHeaderConverter" />
            <converters:LoadInfoStatusVisibilityConverter x:Key="LoadInfoStatusVisibilityConverter" />
            <converters:SelectLocationToUpdateHeaderConverter x:Key="SelectLocationToUpdateHeaderConverter" />
            <converters:LocationStatusBtnBkgndColorConverter x:Key="LocationStatusBtnBkgndColorConverter" />
            <converters:LoadLocationAddressToMapPositionConverter x:Key="LoadLocationAddressToMapPositionConverter" />
            <converters:LoadLocationAddressToMapPinsConverter x:Key="LoadLocationAddressToMapPinsConverter" />
            <converters:LocationItemHeaderTextConverter x:Key="LocationItemHeaderTextConverter" />
            <converters:MainMenuHeaderConverter x:Key="MainMenuHeaderConverter" />
            <converters:HumanizerConverter x:Key="HumanizerConverter" />
            <converters:ImageConverter x:Key="ImageConverter" />
        </ResourceDictionary>
    </Application.Resources>
</Application>
