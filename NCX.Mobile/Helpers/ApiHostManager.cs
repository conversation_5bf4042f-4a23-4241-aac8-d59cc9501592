﻿using NCX.Mobile.Data;
using NCX.Mobile.Models;
using NCX.Mobile.Services;
using NCX.Mobile.Services.Impl;
using System.Threading.Tasks;

namespace NCX.Mobile.Helpers
{
    class ApiHostManager
    {
        readonly IAppSettings _appSettingsService;
        readonly IHaulCache _haulCache;
        readonly INCXApi _ncxApi;
        readonly UrlResolver _urlResolver;

        public ApiHostManager(
            IAppSettings appSettingsService,
            INCXApi ncxApi,
            IHaulCache haulCache,
            UrlResolver urlResolver)
        {
            _appSettingsService = appSettingsService;
            _ncxApi = ncxApi;
            _haulCache = haulCache;
            _urlResolver = urlResolver;
        }

        public async Task SetApiHostAsync(string newApiHost)
        {
            _appSettingsService.ApiHost = ParseApiHost(newApiHost);

            await _haulCache.ClearAsync();

            _ncxApi.Client.BaseAddress = _urlResolver.BaseAddress;
        }

        static string ParseApiHost(string apiHost)
        {
            apiHost = apiHost.ToLowerInvariant();

            if (apiHost.Contains("://"))
            {
                return apiHost;
            }

            return $"https://{apiHost}.{Constants.Host}";
        }
    }
}
