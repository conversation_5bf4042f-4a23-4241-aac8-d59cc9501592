﻿using NCX.Mobile.IFTA.Views;
using NCX.Mobile.Views;

namespace NCX.Mobile.Helpers
{
    // Navigation keys used by INavigationService
    static class NavigationServiceKeys
    {
        public const string MasterDetail = nameof(MainMasterDetailPage);
        public const string NavigationPage = nameof(RootPage);
        public const string Main = nameof(MainPage);
        public const string Login = nameof(LoginPage);
        public const string LoadDetails = nameof(LoadDetailsPage);
        public const string SelectLoadLocationToUpdate = nameof(SelectLoadLocationToUpdatePage);
        public const string UpdateLoadLocationStatus = nameof(UpdateLoadLocationStatusPage);
        public const string UploadPOD = nameof(UploadPODPage);
        public const string ViewPODItem = nameof(ViewPODItemPage);
        public const string SelectApiHost = nameof(SelectApiHostPage);
        public const string ReportDelay = nameof(ReportDelayPage);
        public const string ResetUserPassword = nameof(ResetUserPasswordPage);
        public const string ChangeAppLanguage = nameof(LanguagePage);
        public const string UserProfile = nameof(UserProfilePage);
        public const string ChangeUserPassword = nameof(ChangeUserPasswordPage);
        public const string Settings = nameof(HelpPage);
        public const string PrivacyPolicy = nameof(WebViewPage);
        public const string TermsOfService = nameof(WebViewPage);
        public const string HelpFAQ = nameof(WebViewPage);
        public const string Tutorial = nameof(WebViewPage);
        public const string NewLoadPopup = nameof(NewLoadPopupPage);
        public const string RecordFuelStop = nameof(RecordFuelStopPage);

        public static readonly string MainUri = $"/{MasterDetail}/{NavigationPage}/{Main}";
    }
}
