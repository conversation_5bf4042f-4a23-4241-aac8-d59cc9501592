﻿using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using System.Threading.Tasks;

namespace NCX.Mobile.Helpers
{
    static class NCXApiServiceHelper
    {
        public static Task HandleExceptionAsync(ApiErrorException ex, IUserInteractionService userInteractionService)
        {
            if (ex.HttpStatusCode != null)
            {
                return userInteractionService.ShowAlertAsync(
                    Resources.ConnectionErrorMessageText     ,
                    Resources.ServerErrorTitleText           ,
                    Resources.CloseText);
            }
            else
            {
                return userInteractionService.ShowAlertAsync(
                    Resources.InternetNotAvailableMessageText,
                    Resources.ServerErrorTitleText           ,
                    Resources.CloseText);
            }
        }
    }
}
