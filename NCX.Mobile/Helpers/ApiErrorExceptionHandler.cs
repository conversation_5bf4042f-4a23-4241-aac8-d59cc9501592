﻿using Prism.Ioc;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using System;

namespace NCX.Mobile.Helpers
{
    public class ApiErrorActionHandler : ApiServiceExceptionHandler
    {
        private readonly Action _onApiError;
        private readonly Action _onApiCancelled;

        // Directly inject the needed services
        private readonly IUserServiceLogoutCommand _logoutCommand;
        private readonly IUserInteractionService _userInteractionService;

        public ApiErrorActionHandler(
            IUserServiceLogoutCommand logoutCommand,
            IUserInteractionService userInteractionService,
            Action onApiError = null,
            Action onApiCancelled = null)
        {
            _logoutCommand = logoutCommand;
            _userInteractionService = userInteractionService;
            _onApiError = onApiError;
            _onApiCancelled = onApiCancelled;
        }

        protected override void OnApiError(ApiErrorException ex)
        {
            _onApiError?.Invoke();
        }

        protected override void OnApiUnauthorizedException()
        {
            // Use the injected logout command
            _logoutCommand.Execute(true);
        }

        protected override async void OnApiConnectionError(ApiErrorException ex)
        {
            // Use the injected userInteractionService
            if (_userInteractionService == null)
                return;

            await _userInteractionService.ShowAlertAsync(
                Resources.InternetNotAvailableMessageText,
                Resources.ServerErrorTitleText,
                Resources.CloseText);
        }

        // If needed, override OnApiCancelled
        // protected override void OnApiCancelled(TaskCanceledException ex)
        // {
        //     _onApiCancelled?.Invoke();
        // }
    }
}
