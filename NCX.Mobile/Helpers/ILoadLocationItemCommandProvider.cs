﻿using System.Reactive;
using NCX.Mobile.Models;
using Prism.Navigation;
using ReactiveUI;

namespace NCX.Mobile.Helpers
{
    internal interface ILoadLocationItemCommandProvider : IDestructible
    {
        ReactiveCommand<LoadOperation, Unit> CallLocationContactCommand { get; }

        ReactiveCommand<LoadOperation, Unit> CallAppointmentContactCommand { get; }

        ReactiveCommand<LoadOperation, Unit> StartMapDirectionsTaskCommand { get; }
    }
}
