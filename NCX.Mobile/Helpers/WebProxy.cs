﻿using System;
using System.Net;

namespace NCX.Mobile.Helpers
{
    /// <summary>
    /// Used to setup a proxy to HttpClient
    /// </summary>
    public class WebProxy : IWebProxy
    {
        readonly Uri _proxyUri;

        public ICredentials Credentials
        {
            get;
            set;
        }

        public WebProxy(Uri proxyUri)
        {
            _proxyUri = proxyUri;
        }

        public Uri GetProxy(Uri destination)
        {
            return _proxyUri;
        }

        public bool IsBypassed(Uri host)
        {
            return false;
        }
    }
}
