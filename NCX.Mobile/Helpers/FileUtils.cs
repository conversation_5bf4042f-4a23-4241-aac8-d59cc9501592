﻿using NCX.Mobile.Models;
using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

namespace NCX.Mobile.Helpers
{
    static class FileUtils
    {
        public static async Task<string> GetBase64EncodedAsync(Stream stream)
        {
            var fileContent = new StreamContent(stream);
            return Base64UrlEncode(await fileContent.ReadAsByteArrayAsync());
        }

        public static string GetMimeTypeFromImageFilePath(string path)
        {
            string fileExtension = Path.GetExtension(path).ToLowerInvariant().Substring(1);

            if (fileExtension == "jpg")
            {
                return "image/jpeg";
            }
            else
            {
                return $"image/{fileExtension}";
            }
        }


        // RFC 4648 compliant Base64 encoding
        public static string Base64UrlEncode(byte[] bytes)
        {
            return Convert.ToBase64String(bytes) // Regular base64 encoder
                .Split('=')[0] // Remove any trailing '='s
                .Replace('+', '-') // 62nd char of encoding
                .Replace('/', '_'); // 63rd char of encoding
        }
    }
}
