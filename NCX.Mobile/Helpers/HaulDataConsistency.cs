﻿using NCX.Mobile.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace NCX.Mobile.Helpers
{
    static class HaulDataConsistency
    {
        public static void EnsureConsistency(IEnumerable<Haul> hauls)
        {
            Assert(hauls.Count(haul => haul.IsActive) <= 1, "There should be a maximum of one active haul");
            Assert(hauls.All(haul => haul.Loads.Count() == 1), "There should be one load per haul");
            Assert(hauls.All(haul => haul.Status != HaulStatus.Canceled && haul.Status != HaulStatus.Deleted), "One of the haul has an unsupported state: Canceled or Deleted");

            IEnumerable<Load> allLoads = hauls.SelectMany(haul => haul.Loads);
            Assert(allLoads.Count(load => load.IsActive) <= 1, "There should be a maximum of one active load");
            Assert(allLoads.All(load => load.Status != LoadStatus.Canceled && load.Status != LoadStatus.Deleted), "One of the loads has an unsupported state: Canceled or Deleted");

            IEnumerable<LoadOperation> allLoadOperations = hauls.SelectMany(haul => haul.Loads).SelectMany(load => load.Operations);
            Assert(allLoadOperations.Count(loadOp => loadOp.Status == LoadOperationStatus.En_Route || loadOp.Status == LoadOperationStatus.Checked_In) <= 1, "There should be a maximum of one active load operation");
        }

        static void Assert(bool condition, string message)
        {
            if (!condition)
            {
                throw new DataConsistencyException(message);
            }
        }
    }

    class DataConsistencyException : Exception
    {
        public DataConsistencyException(string msg) : base(msg)
        {
        }
    }
}
