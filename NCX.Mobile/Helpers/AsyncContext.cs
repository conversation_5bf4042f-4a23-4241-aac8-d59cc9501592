﻿using NCX.Logging.Interfaces;
using System;
using System.Threading.Tasks;

namespace NCX.Mobile.Helpers
{
    public class AsyncContext
    {
        private readonly AsyncLock _executeLock = new AsyncLock();
        private readonly ILogService _logService;

        // Use constructor injection for ILogService instead of referencing ServiceStack.Instance
        public AsyncContext(ILogService logService)
        {
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));
        }

        static int x = 0;

        public async Task<TResult> ExecuteAsync<TResult>(Func<Task<TResult>> taskAction)
        {
            _logService.LogDebug("NCX", $"entering...{++x}");

            try
            {
                using (await _executeLock.LockAsync())
                {
                    _logService.LogDebug("NCX", $"entered {x}");

                    var result = await taskAction();
                    return result;
                }
            }
            finally
            {
                _logService.LogDebug("NCX", $"leaving {--x}");
            }
        }

        public async Task ExecuteAsync(Func<Task> task)
        {
            _logService.LogDebug("NCX", $"entering...{++x}");

            using (await _executeLock.LockAsync())
            {
                _logService.LogDebug("NCX", $"entered {x}");
                await task();
            }

            _logService.LogDebug("NCX", $"leaving {--x}");
        }
    }
}
