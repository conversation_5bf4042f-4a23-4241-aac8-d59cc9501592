﻿using System.Linq;
using System.Reactive;
using System.Reactive.Linq;
using System.Threading.Tasks;
using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using Prism.Navigation;
using ReactiveUI;

namespace NCX.Mobile.Helpers
{
    internal class HaulCommandProvider : IHaulCommandProvider
    {
        private INavigationService _navigationService { get; }

        private IUserInteractionService _userInteractionService { get; }

        private IUserService _userService { get; }

        public HaulCommandProvider(INavigationService navigationService, IUserService userService, IUserInteractionService userInteractionService)
        {
            _navigationService = navigationService;
            _userInteractionService = userInteractionService;
            _userService = userService;

            ShowLoadDetailsCommand = ReactiveCommand.CreateFromTask<Haul>(ShowLoadDetails);
            UpdateLoadStatusCommand = ReactiveCommand.CreateFromTask<Haul>(UpdateLoadStatus);
        }

        public ReactiveCommand<Haul, Unit> ShowLoadDetailsCommand { get; private set; }

        public ReactiveCommand<Haul, Unit> UpdateLoadStatusCommand { get; private set; }

        async Task UpdateLoadStatus(Haul haul)
        {
            try
            {
                if (!await CanLoadBeUpdatedAsync(haul))
                {
                    return;
                }

                Load load = haul.Loads.Single();

                INavigationResult result = null;
                if (ShouldUserPickLoadToUpdate(haul) || !load.Pickups.Concat(load.Dropoffs).Any(l => l.IsActiveOrPending))
                {
                    // Load has active and\or several pending locations (depending which type is currently processed)
                    result = await _navigationService.NavigateAsync(NavigationServiceKeys.SelectLoadLocationToUpdate
                        , new NavigationParameters { { "haul", haul } });
                }
                else
                {
                    // Load has one single pickup or drop-off (depending which type is currently processed)
                    LoadOperation loadOperationToUpdate = load.GetActiveLoadOperation();

                    if (loadOperationToUpdate is null)
                    {
                        var pickups = load.Pickups;
                        var concat = pickups.Concat(load.Dropoffs);
                        loadOperationToUpdate = concat.First(l => l.IsActiveOrPending);
                    }

                    result = await _navigationService.NavigateAsync(NavigationServiceKeys.UpdateLoadLocationStatus,
                        new NavigationParameters {
                             { "haul", haul },
                             { "loadOperationId", loadOperationToUpdate.Id }
                        });
                }

                if (!result.Success)
                {
                    System.Console.WriteLine(result.Exception);
                    //TurnedOff dev debugger breaks! System.Diagnostics.Debugger.Break();
                    await _userInteractionService.ShowAlertAsync(result.Exception.Message, $"Error: {result.Exception.GetType().Name}");
                }
            }
            catch (System.Exception ex)
            {
                System.Console.WriteLine(ex);
                //TurnedOff dev debugger breaks! System.Diagnostics.Debugger.Break();
                await _userInteractionService.ShowAlertAsync(ex.Message, $"Error: {ex.GetType().Name}");
            }

        }

        async Task<bool> CanLoadBeUpdatedAsync(Haul haul)
        {
            if (_userService.ActiveLoadInfo != null && _userService.ActiveLoadInfo.HaulId != haul.Id)
            {
                await _userInteractionService.ShowAlertAsync(Resources.NotActiveLoadTitleText, Resources.NotActiveLoadMessageText);

                return false;
            }
            else
            {
                return true;
            }
        }

        bool ShouldUserPickLoadToUpdate(Haul haul)
        {
            Load load = haul.Loads.Single();

            //  If there is just one active or pending pickup or drop-off, navigate to the update location status page
            //  otherwise, navigate to location list to select location to update
            if (load.Pickups.Any(pickup => pickup.IsActiveOrPending))
            {
                return load.Pickups.Count(pickup => pickup.IsActiveOrPending) > 1;
            }
            else
            {
                return load.Dropoffs.Count(dropoff => dropoff.IsActiveOrPending) > 1;
            }
        }

        async Task ShowLoadDetails(Haul haul)
        {
            var result = await _navigationService.NavigateAsync(NavigationServiceKeys.LoadDetails,
                new NavigationParameters { { "haul", haul } });

            if (!result.Success)
            {
                System.Console.WriteLine(result.Exception);
                //TurnedOff dev debugger breaks! System.Diagnostics.Debugger.Break();
            }
        }

        public void Destroy()
        {
            ShowLoadDetailsCommand.Dispose();
            ShowLoadDetailsCommand = null;
            UpdateLoadStatusCommand.Dispose();
            UpdateLoadStatusCommand = null;
        }
    }
}
