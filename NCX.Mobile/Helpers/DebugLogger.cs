﻿using System;
using System.Diagnostics;
using System.Globalization;
using Microsoft.Extensions.Logging;

namespace NCX.Mobile.Helpers
{
    class DebugLogger : ILogger
    {
        private class NoopDisposable : IDisposable
        {
            public void Dispose() { }
        }

        public IDisposable BeginScope<TState>(TState state) => new NoopDisposable();

        public bool IsEnabled(LogLevel logLevel) => true;

        public void Log<TState>(
            LogLevel logLevel,
            EventId eventId,
            TState state,
            Exception exception,
            Func<TState, Exception, string> formatter)
        {
            Debug.WriteLine($"{logLevel.ToString().ToUpper()}: {formatter(state, exception)}");
        }
    }
}
