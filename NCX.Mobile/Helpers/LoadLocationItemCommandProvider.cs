﻿using System;
using System.Linq;
using System.Reactive;
using System.Reactive.Linq;
using System.Threading.Tasks;
using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using ReactiveUI;

namespace NCX.Mobile.Helpers
{
    internal class LoadLocationItemCommandProvider : ILoadLocationItemCommandProvider
    {
        private IMapDirectionsTask _mapDirectionsTask { get; }
        private IMessagingService _messagingService { get; }
        private IUserInteractionService _userInteractionService { get; }

        public LoadLocationItemCommandProvider(IUserInteractionService userInteractionService,
                                               IMessagingService messagingService,
                                               IMapDirectionsTask mapDirectionsTask)
        {
            _mapDirectionsTask = mapDirectionsTask;
            _messagingService = messagingService;
            _userInteractionService = userInteractionService;

            StartMapDirectionsTaskCommand = ReactiveCommand.CreateFromTask<LoadOperation>(StartMapDirectionsAsync);
            var canMakeCall = Observable.Interval(TimeSpan.FromSeconds(1))
                                        .Select(_ => _messagingService.CanMakePhoneCall);

            CallLocationContactCommand = ReactiveCommand.Create<LoadOperation>(OnCallLocationContactCommandExecuted, canMakeCall);
            CallAppointmentContactCommand = ReactiveCommand.Create<LoadOperation>(OnCallAppointmentContactCommandExecuted, canMakeCall);
        }

        public ReactiveCommand<LoadOperation, Unit> CallLocationContactCommand { get; private set; }

        public ReactiveCommand<LoadOperation, Unit> CallAppointmentContactCommand { get; private set; }

        public ReactiveCommand<LoadOperation, Unit> StartMapDirectionsTaskCommand { get; private set; }

        private void OnCallLocationContactCommandExecuted(LoadOperation loadOperation)
        {
            _messagingService.MakePhoneCall(loadOperation.Location.ContactNumber, loadOperation.Location.ContactName);
        }

        private void OnCallAppointmentContactCommandExecuted(LoadOperation loadOperation)
        {
            _messagingService.MakePhoneCall(loadOperation.Location.AppointmentContactNumber, loadOperation.Location.AppointmentContactName);
        }

        async Task StartMapDirectionsAsync(LoadOperation loadOperation)
        {
            if (!await _mapDirectionsTask.NavigateToAsync(
                loadOperation.Location.CompanyName,
                loadOperation.Location.Address.Latitude,
                loadOperation.Location.Address.Longitude))
            {
                await _userInteractionService.ShowAlertAsync(Resources.NoMapAppForDirectionsMessage, Resources.InforamationTitleText);
            }
        }

        public void Destroy()
        {
            StartMapDirectionsTaskCommand.Dispose();
            StartMapDirectionsTaskCommand = null;
            CallAppointmentContactCommand.Dispose();
            CallAppointmentContactCommand = null;
            CallLocationContactCommand.Dispose();
            CallLocationContactCommand = null;
        }
    }
}
