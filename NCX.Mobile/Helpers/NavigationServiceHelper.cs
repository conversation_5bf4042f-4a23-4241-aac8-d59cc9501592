﻿using Prism.Commands;
using Prism.Navigation;
using System;
using System.Net;
using System.Windows.Input;

namespace NCX.Mobile.Helpers
{
    class NavigationServiceHelper
    {
        public static ICommand CreateNavToWebPageCommand(INavigationService navigationService, string targetName, string title, string url)
        {
            return new DelegateCommand(() => navigationService.NavigateAsync(targetName, CreateWebViewPageParams(title, url)));
        }

        public static NavigationParameters CreateWebViewPageParams(string title, string url)
        {
            // Add some a random param just to ensure latest HTML is fetched
            url = $"{url}?r={Guid.NewGuid().ToString()}";

            return new NavigationParameters($"title={title}&webPageUri={WebUtility.UrlEncode(url)}");
        }
    }
}
