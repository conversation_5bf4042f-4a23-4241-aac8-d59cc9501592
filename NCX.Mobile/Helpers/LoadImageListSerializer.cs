﻿using NCX.Mobile.Models;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace NCX.Mobile.Helpers
{
    static class LoadImageListSerializer
    {
        public static string Serialize(IEnumerable<LoadImage> loadImageList)
        {
            return JsonConvert.SerializeObject(loadImageList);
        }

        public static IEnumerable<LoadImage> Deserialize(string loadImageListSerialized)
        {
            return JsonConvert.DeserializeObject<IEnumerable<LoadImage>>(loadImageListSerialized);
        }
    }
}
