﻿using LadyBug;
using Microsoft.AppCenter.Crashes;
using NCX.Logging.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace NCX.Mobile.Helpers
{
    public class HttpLoggingHandler : DelegatingHandler
    {
        readonly ILogService _logService;

        public HttpLoggingHandler(ILogService logService, HttpMessageHandler innerHandler = null)
            : base(innerHandler ?? new HttpClientHandler())
        {
            _logService = logService;
        }

        async protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            var DontLogThisOne = false;
            var ReqContentStr = "";
            string requestContentLogItem="";
            HttpRequestMessage req = request;
            string id = Guid.NewGuid().ToString();
            var msg = new StringBuilder($"[{id} -   Request]");

            msg.AppendLine($"========Start==========");
            msg.AppendLine($"{req.Method} {req.RequestUri.PathAndQuery} {req.RequestUri.Scheme}/{req.Version}");
            msg.AppendLine($"Host: {req.RequestUri.Scheme}://{req.RequestUri.Host}");

            foreach (var header in req.Headers)
            {
                msg.AppendLine($"{header.Key}: {string.Join(", ", header.Value)}");
            }
            
            if (req.Content != null)
            {
                foreach (var header in req.Content.Headers)
                {
                    msg.AppendLine($"{header.Key}: {string.Join(", ", header.Value)}");
                }

                requestContentLogItem = "";
                requestContentLogItem = request.ToString();
                if (req.Content is StringContent || IsTextBasedContentType(req.Headers) || IsTextBasedContentType(req.Content.Headers))
                {
                    ReqContentStr= await req.Content.ReadAsStringAsync();
                    if (ReqContentStr.Contains(@":{""latitude"":"))
                    {
                        DontLogThisOne = true;
                    }
                    msg.AppendLine($"Content:");
                    msg.AppendLine($"{string.Join("", requestContentLogItem.Cast<char>().Take(10000))}...");
                }
            }
            else
            {
                ReqContentStr = msg.ToString();
            }

            DateTime start = DateTime.Now;
            //API SENDS HAPPEN HERE!!!
            HttpResponseMessage response=null;


            try
            {
                response = await base.SendAsync(request, cancellationToken).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                var LogDictCrashed = new Dictionary<String, String>();
                LogDictCrashed.Add("REQ", requestContentLogItem);
                LogDictCrashed.Add("REQ2", ReqContentStr);
                LogDictCrashed.Add("MSG", msg.ToString());
                
                Crashes.TrackError(new SystemException("CrashRequest_HTTP",ex), LogDictCrashed);
                throw;
            }




            DateTime end = DateTime.Now;

            msg.AppendLine($"Duration: {end - start}");
            msg.AppendLine($"==========End==========");

            _logService.LogDebug(Constants.Log_Tag, msg.ToString());

            msg = new StringBuilder($"\n[{id} - Response]");
            msg.AppendLine($"=========Start=========");

            HttpResponseMessage resp = response;

            msg.AppendLine($"{req.RequestUri.Scheme.ToUpper()}/{resp.Version} {(int)resp.StatusCode} {resp.ReasonPhrase}");

            foreach (var header in resp.Headers)
            {
                msg.AppendLine($"{header.Key}: {string.Join(", ", header.Value)}");
            }

            if (resp.Content != null)
            {
                foreach (var header in resp.Content.Headers)
                {
                    msg.AppendLine($"{header.Key}: {string.Join(", ", header.Value)}");
                }

                if (resp.Content is StringContent || IsTextBasedContentType(resp.Headers) || IsTextBasedContentType(resp.Content.Headers))
                {
                    start = DateTime.Now;
                    string result = await resp.Content.ReadAsStringAsync();
                    end = DateTime.Now;

                    msg.AppendLine($"Content:");
                    msg.AppendLine($"{string.Join("", result.Cast<char>().Take(10000))}...");
                    msg.AppendLine($"Duration: {end - start}");
                }
            }

            msg.AppendLine($"==========End==========");

            var LogDict= new Dictionary<String, String>();
            LogDict.Add("REQ", requestContentLogItem);
            LogDict.Add("REQ2", ReqContentStr);
            LogDict.Add("MSG",msg.ToString());

            _logService.LogDebug(Constants.Log_Tag, msg.ToString());

            return response;
        }

        readonly string[] types = new[] { "html", "text", "xml", "json", "txt", "x-www-form-urlencoded" };

        bool IsTextBasedContentType(HttpHeaders headers)
        {
            IEnumerable<string> values;
            if (!headers.TryGetValues("Content-Type", out values))
                return false;
            var header = string.Join(" ", values).ToLowerInvariant();

            return types.Any(t => header.Contains(t));
        }
    }
}
