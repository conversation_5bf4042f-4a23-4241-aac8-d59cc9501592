﻿using NCX.Mobile.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Prism.Ioc;

namespace NCX.Mobile.Helpers
{
    static class SQLiteAsyncLock
    {
        static readonly AsyncLock _Mutex;

        static SQLiteAsyncLock()
        {
            _Mutex = new AsyncLock();
        }

        public static AsyncLock Inst
        {
            get { return _Mutex; }
        }
    }
}
