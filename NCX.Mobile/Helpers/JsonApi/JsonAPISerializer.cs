﻿using Humanizer;
using JsonApiNet.Attributes;
using JsonApiNet.Components;
using JsonApiNet.Resolvers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace NCX.Mobile.Helpers.JsonApi
{
    public static class JsonAPISerializer
    {
        // Creates a JSON API document with given resource and returns a JSON string serialization of the created JSON API document 
        public static string ResourceToJson(object resource, string resourceType = null, bool dontUseAttributesProperty = false)
        {
            return JsonConvert.SerializeObject(CreateRootJObject(resource, resourceType, dontUseAttributesProperty));
        }

        // Create a JObject for the JSON API document root node
        static JObject CreateRootJObject(object resource, string resourceType = null, bool dontUseAttributesProperty = false)
        {
            var resourceObject = new JObject
            {
                ["data"] = CreateDataObject(resource, resourceType, dontUseAttributesProperty)
            };

            return resourceObject;
        }

        static JToken CreateDataObject(object resource, string resourceType = null, bool dontUseAttributesProperty = false)
        {
            if (resource is IEnumerable)
            {
                var resObjectList = new List<JToken>();

                foreach (object res in resource as IEnumerable)
                {
                    JsonApiResource jsonApiResource = SerializeResource(res);

                    resObjectList.Add(CreateResourceJObject(jsonApiResource, resourceType, dontUseAttributesProperty));
                }

                return JArray.FromObject(resObjectList);
            }
            else
            {
                JsonApiResource jsonApiResource = SerializeResource(resource);

                return CreateResourceJObject(jsonApiResource, resourceType, dontUseAttributesProperty);
            }
        }

        // Create the JObject for the JSON API resource
        static JObject CreateResourceJObject(JsonApiResource jsonApiResource, string resourceType = null, bool dontUseAttributesProperty = false)
        {
            // Create JSON object for JSON API resource data
            var resObject = new JObject
            {
                // Set type
                ["type"] = resourceType ?? jsonApiResource.ResourceIdentifier.Type
            };

            // Set id
            if (!string.IsNullOrWhiteSpace(jsonApiResource.Id))
            {
                resObject["id"] = jsonApiResource.Id;
            }

            // Set attributes
            if (!dontUseAttributesProperty)
            {
                resObject["attributes"] = CreateAttributesObject(jsonApiResource);
            }
            else
            {
                JObject attributesObjects = CreateAttributesObject(jsonApiResource);

                foreach (JProperty jproperty in attributesObjects.Properties())
                {
                    resObject.Add(jproperty.Name, jproperty.Value);
                }
            }

            // Set relationships
            JObject relationshipsObject = CreateRelationshipsJObject(jsonApiResource);

            if (relationshipsObject.Count > 0)
            {
                resObject["relationships"] = relationshipsObject;
            }

            return resObject;
        }

        static JObject CreateRelationshipsJObject(JsonApiResource jsonApiResource)
        {
            // Create JSON object for the JSON API resource relationships
            var relationshipsObject = new JObject();

            foreach (KeyValuePair<string, JsonApiRelationship> kvp in jsonApiResource.Relationships)
            {
                JsonApiResourceIdentifier jsonApiResourceIdentifier = kvp.Value.Data.ResourceIdentifiers.First();

                var data = new JObject
                {
                    ["id"] = jsonApiResourceIdentifier.Id,
                    ["type"] = jsonApiResourceIdentifier.Type
                };

                var relationshipObject = new JObject
                {
                    ["data"] = data
                };
                relationshipsObject[kvp.Key] = relationshipObject;
            }

            return relationshipsObject;
        }

        static JObject CreateAttributesObject(JsonApiResource jsonApiResource, string resourceType = null)
        {
            // Create JSON object for the JSON API resource attributes
            var attributesObject = new JObject();

            foreach (KeyValuePair<string, object> kvp in jsonApiResource.Attributes)
            {
                attributesObject[kvp.Key] = kvp.Value?.ToString();
            }


            return attributesObject;
        }

        static JsonApiResource SerializeResource(object resource)
        {

            PropertyInfo piJsonApiId = new JsonApiPropertyResolver().ResolveJsonApiId(resource.GetType());

            var jsonApiResource = new JsonApiResource(
                id: piJsonApiId?.GetValue(resource).ToString(),
                type: ConvertCLRTypeToJsonApiTypeName(resource.GetType()))
            {
                Attributes = GetAttributes(resource),
                Relationships = GetRelationships(resource)
            };

            return jsonApiResource;
        }

        static JsonApiAttributes GetAttributes(object resource)
        {
            PropertyInfo idPropertyInfo = new JsonApiPropertyResolver().ResolveJsonApiId(resource.GetType());
            var jsonApiAttr = new JsonApiAttributes();

            foreach (PropertyInfo pi in resource.GetType()
                .GetRuntimeProperties()
                .Where(pi1 => (idPropertyInfo == null || pi1.Name.CompareTo(idPropertyInfo.Name) != 0) && PrimitiveTypes.IsSimpleType(pi1.PropertyType)))
            {
                jsonApiAttr.Add(GetJsonApiAttributePropertyName(pi), ConvertValueToJsonApiAttributePropertyValue(pi.GetValue(resource)));
            }

            return jsonApiAttr;
        }

        static string GetJsonApiAttributePropertyName(PropertyInfo pi)
        {
            JsonApiAttributeAttribute jsonApiAttr = pi.GetCustomAttribute<JsonApiAttributeAttribute>();

            if (jsonApiAttr != null)
            {
                return jsonApiAttr.JsonApiAttributeName;
            }
            else
            {
                return ConvertNameToJsonApiAttributePropertyName(pi.Name);
            }
        }

        // Converts "WhatANerd" class name to "what-a-nerd"
        static string ConvertNameToJsonApiAttributePropertyName(string typeName)
        {
            return typeName.Titleize().Replace(" ", "-").ToLowerInvariant();
        }

        static object ConvertValueToJsonApiAttributePropertyValue(object value)
        {
            if (value is Enum)
            {
                return value.ToString().ToLowerInvariant();
            }
            else if (value is DateTime)
            {
                return ((DateTime)value).ToString("yyyy'-'MM'-'dd'T'HH':'mm':'ss'.'fff'Z'");
            }
            else
            {
                return value;
            }
        }

        static string ConvertCLRTypeToJsonApiTypeName(Type type)
        {
            return type.Name.Titleize().Replace(" ", "-").ToLowerInvariant().Pluralize();
        }

        static JsonApiRelationships GetRelationships(object resource)
        {
            // Get property info of the Id property
            PropertyInfo idPropertyInfo = new JsonApiPropertyResolver().ResolveJsonApiId(resource.GetType());

            var jsonApiRelationships = new JsonApiRelationships();

            // Get all properties which have a class as return type, except Id property
            foreach (PropertyInfo pi in resource.GetType()
                .GetRuntimeProperties()
                 .Where(pi1 => (idPropertyInfo == null || pi1.Name.CompareTo(idPropertyInfo.Name) != 0) && !PrimitiveTypes.IsSimpleType(pi1.PropertyType)))
            {
                // create JSON API relationship object
                jsonApiRelationships.Add(
                    pi.Name.ToLowerInvariant(),
                    new JsonApiRelationship()
                    {
                        Data = new JsonApiResourceLinkage()
                        {
                            IsSingleResourceIdentifier = true,
                            ResourceIdentifiers = new List<JsonApiResourceIdentifier>(new[] {
                                new JsonApiResourceIdentifier(
                                    id: GetIdOfRelationshipObject(resource, pi), // get the related object id
                                    type: ConvertCLRTypeToJsonApiTypeName(pi.PropertyType))
                            })
                        }
                    });
            }

            return jsonApiRelationships;
        }

        private static string GetIdOfRelationshipObject(object resource, PropertyInfo pi)
        {
            if (resource == null || pi == null)
            {
                return null;
            }

            try
            {
                var value = pi.GetValue(resource);
                if (value == null)
                {
                    return null;
                }

                var idProperty = value.GetType().GetProperty("Id");
                if (idProperty == null)
                {
                    return null;
                }

                return idProperty.GetValue(value)?.ToString();
            }
            catch
            {
                return null;
            }
        }

        //public static bool IsSimpleType(Type type)
        //{
        //    TypeInfo typeInfo = type.GetTypeInfo();

        //    if (typeInfo.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
        //    {
        //        return IsSimpleType(typeInfo.GenericTypeArguments[0]);
        //    }
        //    else
        //    {
        //        return typeInfo.IsPrimitive ||
        //            typeInfo.IsEnum ||
        //            type.Equals(typeof(string)) ||
        //            type.Equals(typeof(Decimal)) ||
        //            type.Equals(typeof(DateTime)) ||
        //            type.Equals(typeof(DateTimeOffset)) ||
        //            type.Equals(typeof(TimeSpan)) ||
        //            type.Equals(typeof(Guid));
        //    }
        //}

        static class PrimitiveTypes
        {
            public static readonly Type[] List;

            static PrimitiveTypes()
            {
                var types = new[] {
                    typeof (Enum)          ,
                    typeof (String)        ,
                    typeof (Char)          ,
                    typeof (Guid)          ,

                    typeof (Boolean)       ,
                    typeof (Byte)          ,
                    typeof (Int16)         ,
                    typeof (Int32)         ,
                    typeof (Int64)         ,
                    typeof (Single)        ,
                    typeof (Double)        ,
                    typeof (Decimal)       ,

                    typeof (SByte)         ,
                    typeof (UInt16)        ,
                    typeof (UInt32)        ,
                    typeof (UInt64)        ,

                    typeof (DateTime)      ,
                    typeof (DateTimeOffset),
                    typeof (TimeSpan)      ,
                };

                IEnumerable<Type> nullTypes = from t in types
                                              where t.GetTypeInfo().IsValueType
                                              select typeof(Nullable<>).MakeGenericType(t);

                List = types.Concat(nullTypes).ToArray();
            }

            public static bool IsSimpleType(Type type)
            {
                if (List.Any(t => t.GetTypeInfo().IsAssignableFrom(type.GetTypeInfo())))
                {
                    return true;
                }

                Type nut = Nullable.GetUnderlyingType(type);

                return nut != null && nut.GetTypeInfo().IsEnum;
            }
        }
    }
}

