﻿using NCX.Mobile.Services;
using Prism.Navigation;
using System;

namespace NCX.Mobile.Helpers
{
    public class DeepLinkHandler
    {
        readonly IUserService _userServices;

        public DeepLinkHandler(IUserService userServices)
        {
            _userServices = userServices;
        }

        public bool TryHandle(Uri uri)
        {
            if (uri != null && uri.Scheme == Constants.DeepLinkScheme)
            {
                return TryHandleLoginDeepLink(uri);
            }

            return false;
        }

        public bool TryHandleLoginDeepLink(Uri uri)
        {
            if (uri.Host == Constants.DeepLinkHostLogin)
            {
                HandleLoginDeepLink(uri);

                return true;
            }

            return false;
        }

        void HandleLoginDeepLink(Uri uri)
        {
            if (_userServices.IsLoggedIn)
            {
                return;
            }

            if (!string.IsNullOrWhiteSpace(uri.Query))
            {
                throw new NotSupportedException("This operation is not supported at this time");
                //_navigationServiceFactory().NavigateAsync($"/{NavigationServiceKeys.Login}", new NavigationParameters(uri.Query));
            }
        }

        //internal static Dictionary<string, string> ParseQueryString(Uri uri)
        //{
        //    var query = uri.Query.Substring(uri.Query.IndexOf('?') + 1); // +1 for skipping '?'
        //    return ParseQueryString(query);
        //}

        //internal static Dictionary<string, string> ParseQueryString(string queryString)
        //{
        //    var pairs = queryString.Split('&');
        //    return pairs
        //        .Select(o => o.Split('='))
        //        .Where(items => items.Count() == 2)
        //        .ToDictionary(pair => Uri.UnescapeDataString(pair[0]),
        //            pair => Uri.UnescapeDataString(pair[1]));
        //}
    }
}
