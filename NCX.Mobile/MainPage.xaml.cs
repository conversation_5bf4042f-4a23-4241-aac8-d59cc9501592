﻿using NCX.Mobile.ViewModels;
using Prism;
using Microsoft.Maui.Devices;

namespace NCX.Mobile
{
    public partial class MainPage : ContentPage
    {
        public MainPage()
        {
            InitializeComponent();
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();

            // Keep screen on when this page is visible
#if ANDROID
            if (DeviceInfo.Platform == DevicePlatform.Android)
            {
                Microsoft.Maui.ApplicationModel.Platform.CurrentActivity.Window.AddFlags(Android.Views.WindowManagerFlags.KeepScreenOn);
            }
#elif IOS
            // iOS equivalent to keep screen on
            DeviceDisplay.KeepScreenOn = true;
#endif
        }

        protected override void OnDisappearing()
        {
            // Remove the keep screen on flag when page disappears
#if ANDROID
            if (DeviceInfo.Platform == DevicePlatform.Android)
            {
                Microsoft.Maui.ApplicationModel.Platform.CurrentActivity.Window.ClearFlags(Android.Views.WindowManagerFlags.KeepScreenOn);
            }
#elif IOS
            // iOS equivalent to allow screen to turn off
            DeviceDisplay.KeepScreenOn = false;
#endif

            base.OnDisappearing();
        }
    }

}
