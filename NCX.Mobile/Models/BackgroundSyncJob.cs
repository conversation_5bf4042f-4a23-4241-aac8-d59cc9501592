﻿using NCX.Mobile.Services;
using Shiny.Jobs;

namespace NCX.Mobile.Models
{
    public record BackgroundSyncJobInfo : JobInfo
    {
        public const string Name = nameof(BackgroundSyncJobInfo);

        public BackgroundSyncJobInfo()
            : base("BackgroundSync", typeof(ApiCacheSyncJob))
        {
            BatteryNotLow = true;
            RequiredInternetAccess = InternetAccess.Any;
        }
    }
}
