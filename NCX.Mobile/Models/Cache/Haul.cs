﻿using SQLite;
using System;

namespace NCX.Mobile.Models.Cache
{
    class Haul
    {
        [PrimaryKey]
        public int Id { get; set; }

        [Indexed]
        public DateTime CreatedAt { get; set; }

        public string Data { get; set; }

        public Haul()
        {
        }

        public Haul(int id, DateTime createdAt, string data)
        {
            Id = id;
            CreatedAt = createdAt;
            Data = data;
        }
    }

    class TemporaryHaul : Haul
    {
        public TemporaryHaul()
        {
        }

        public TemporaryHaul(int id, DateTime createdAt, string data) : base(id, createdAt, data)
        {
        }
    }
}
