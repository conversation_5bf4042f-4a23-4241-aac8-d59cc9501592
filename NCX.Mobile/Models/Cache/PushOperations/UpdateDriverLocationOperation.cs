﻿using NCX.Mobile.Models.Cache.PushOperations;

namespace NCX.Mobile.Models.Cache
{
    public class UpdateDriverLocationOperation : PushOperation
    {
        public double Longitude { get; set; }

        public double Latitude { get; set; }

        public int? PowerUnitId { get; set; }

        public UpdateDriverLocationOperation() : base(PushOperationType.UpdateDriverLocation)
        {
        }

        public UpdateDriverLocationOperation(GeoPosition geoPosition) : this()
        {
            Longitude = geoPosition.Longitude;
            Latitude = geoPosition.Latitude;
        }
    }
}
