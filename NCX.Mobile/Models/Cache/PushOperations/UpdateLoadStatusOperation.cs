﻿namespace NCX.Mobile.Models.Cache.PushOperations
{
    class UpdateLoadStatusOperation : PushOperation
    {
        public int HaulId { get; set; }
        public int LoadId { get; set; }
        public LoadStatus LoadStatus { get; set; }

        public UpdateLoadStatusOperation() : base(PushOperationType.UpdateLoadStatus)
        {
        }

        public UpdateLoadStatusOperation(int haulId, int loadId, LoadStatus newStatus) : this()
        {
            HaulId = haulId;
            LoadId = loadId;
            LoadStatus = newStatus;
        }
    }
}
