﻿namespace NCX.Mobile.Models.Cache.PushOperations
{
    class UpdateLoadOperationStatusOperation : PushOperation
    {
        public int HaulId { get; set; }
        public int LoadId { get; set; }
        public int LoadOperationId { get; set; }
        public LoadOperationStatus LoadOperationStatus { get; set; }

        public UpdateLoadOperationStatusOperation() : base(PushOperationType.UpdateLoadOperationStatus)
        {
        }

        public UpdateLoadOperationStatusOperation(int haulId, int loadId, int loadOperationId, LoadOperationStatus newStatus) : this()
        {
            HaulId = haulId;
            LoadId = loadId;
            LoadOperationId = loadOperationId;
            LoadOperationStatus = newStatus;
        }
    }
}
