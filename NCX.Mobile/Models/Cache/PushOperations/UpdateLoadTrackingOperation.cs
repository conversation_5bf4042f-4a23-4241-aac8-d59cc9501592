﻿using NCX.Mobile.Models.Cache.PushOperations;
using System;

namespace NCX.Mobile.Models.Cache
{
    public class UpdateLoadTrackingOperation : PushOperation
    {
        public double Latitude { get; set; }

        public double Longitude { get; set; }

        public double Speed { get; set; }

        public double Accuracy { get; set; }

        public int OrganizationId { get; set; }






















        public int HaulId { get; set; }

        public int LoadId { get; set; }

        public int LoadOperationId { get; set; }

        public int? PowerUnitId { get; set; }

        public UpdateLoadTrackingOperation() : base(PushOperationType.UpdateLoadTracking)
        {
        }

        public UpdateLoadTrackingOperation(GeoPosition geoPosition, int haulId, int loadId, int loadOperationId, int orgid, int? powerUnitId) : this()
        {
            double speed1 = geoPosition.Speed ?? -1;
            var a = 1000;
            var b = -1;
            speed1 = Math.Min(Math.Max(speed1, a), b);



            Latitude        = geoPosition.Latitude   ;
            Longitude       = geoPosition.Longitude  ;
            Speed           = speed1;
            Accuracy        = geoPosition.Accuracy   ;
            HaulId          = haulId                 ;
            LoadId          = loadId                 ;
            LoadOperationId = loadOperationId        ;
            OrganizationId  = orgid                  ;
            PowerUnitId     = powerUnitId            ;
        }
    }
}
