﻿using SQLite;
using System;

namespace NCX.Mobile.Models.Cache.PushOperations
{
    public abstract class PushOperation : IPushOperation
    {
        [PrimaryKey, AutoIncrement]
        public int Id { get; set; }

        public PushOperationType PushOperationType { get; set; }

        public DateTime CreatedOn { get; set; }

        public PushOperation(PushOperationType pushOperationType)
        {
            PushOperationType = pushOperationType;
            CreatedOn = DateTime.Now.ToUniversalTime();
        }
    }
}
