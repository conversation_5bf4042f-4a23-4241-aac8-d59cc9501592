﻿namespace NCX.Mobile.Models.Cache.PushOperations
{
    public class CreateLoadDelayOperation : PushOperation
    {
        public double? Latitude { get; set; }

        public double? Longitude { get; set; }

        public LoadDelayReason Reason { get; set; }

        public int HaulId { get; set; }

        public int LoadId { get; set; }

        public int OrganizationId { get; set; }

        public CreateLoadDelayOperation() : base(PushOperationType.CreateLoadDelay)
        {
        }

        public CreateLoadDelayOperation(GeoPosition geoPosition, LoadDelayReason reason, int haulId, int loadId, int organizationId) : this()
        {
            Latitude = geoPosition?.Latitude;
            Longitude = geoPosition?.Longitude;
            Reason = reason;
            HaulId = haulId;
            LoadId = loadId;
            OrganizationId = organizationId;
        }
    }
}
