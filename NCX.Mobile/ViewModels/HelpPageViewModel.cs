﻿using NCX.Mobile.Helpers;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Navigation;
using System.Windows.Input;

namespace NCX.Mobile.ViewModels
{
    class HelpPageViewModel : BindableBase
    {
        readonly INavigationService _navigationService;
        readonly IMessagingService _messagingService;
        readonly ILocalize _localizeService;

        public ICommand ShowHelpFAQCommand =>
            NavigationServiceHelper.CreateNavToWebPageCommand(_navigationService, NavigationServiceKeys.HelpFAQ, Resources.HelpFAQ, Constants.HelpFAQUrl);

        public ICommand ShowPrivacyPolicyCommand =>
            NavigationServiceHelper.CreateNavToWebPageCommand(_navigationService, NavigationServiceKeys.PrivacyPolicy, Resources.PrivacyPolicyLabel, Constants.PrivacyUrl);

        public ICommand ShowTermsOfServiceCommand =>
            NavigationServiceHelper.CreateNavToWebPageCommand(_navigationService, NavigationServiceKeys.TermsOfService, Resources.TermsOfServiceLabel, Constants.TermsOfServiceUrl);

        public ICommand StartComposeSupportEmailCommand =>
            new DelegateCommand(() => _messagingService.StartComposeEmail(Constants.SupportEmail));

        public ICommand StartSupportCallCommand =>
            new DelegateCommand(() => _messagingService.MakePhoneCall(Constants.SupportPhone));

        public ICommand ShowTutorialCommand =>
            NavigationServiceHelper.CreateNavToWebPageCommand(
                _navigationService,
                NavigationServiceKeys.Tutorial,
                Resources.TutorialPageTitle,
                string.Format(Constants.TutorialFormatUrl, _localizeService.CurrentLanguageCode));

        public HelpPageViewModel(
            INavigationService navigationService,
            IMessagingService messagingService,
            ILocalize localizeService)
        {
            _navigationService = navigationService;
            _messagingService = messagingService;
            _localizeService = localizeService;
        }
    }
}