﻿using NCX.Logging.Interfaces;
using NCX.Mobile.Models;
using NCX.Mobile.Services;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Navigation;
using ReactiveUI;
using System.Threading.Tasks;
using System.Windows.Input;

namespace NCX.Mobile.ViewModels
{
    class NewLoadPopupPageViewModel : BindableBase, INavigatedAware, IConfirmNavigation
    {
        readonly IHaulService _haulService;
        readonly INavigationService _navigationService;

        private LoadItemViewModel _loadItem;
        public LoadItemViewModel LoadItem
        {
            get => _loadItem;
            set => SetProperty(ref _loadItem, value);
        }

        public ICommand UpdateStatusToViewedCommand { get; private set; }

        public NewLoadPopupPageViewModel(
            IHaulService haulService,
            INavigationService navigationService)
        {
            _haulService = haulService;
            _navigationService = navigationService;

            UpdateStatusToViewedCommand = ReactiveCommand.CreateFromTask(UpdateStatusToViewed);
        }

        public void OnNavigatedTo(INavigationParameters parameters)
        {
            if (parameters.GetNavigationMode() == Prism.Navigation.NavigationMode.New)
            {
                LoadItem = new LoadItemViewModel(parameters.GetValue<Haul>("haul"));
            }
        }

        public void OnNavigatedFrom(INavigationParameters parameters)
        {
        }

        public bool CanNavigate(INavigationParameters parameters)
        {
            return LoadItem.Haul.Status == HaulStatus.Viewed;
        }

        private async Task UpdateStatusToViewed()
        {
            if (LoadItem.Haul.Status == HaulStatus.Pending)
            {
                await _haulService.UpdateLoadToViewedStatusAsync(LoadItem.Haul);
            }

            await _navigationService.GoBackAsync(null);
        }
    }
}
