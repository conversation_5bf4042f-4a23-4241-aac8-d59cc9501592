﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Reactive;
using System.Reactive.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using NCX.Mobile.Converters;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Navigation;
using ReactiveUI;

namespace NCX.Mobile.ViewModels
{
    class UpdateLoadLocationStatusPageViewModel : BindableBase, INavigatedAware, IDestructible
    {
        private INavigationService _navigationService { get; }
        private IUserInteractionService _userInteractionService { get; }
        private IHaulService _haulService { get; }
        private ILoadLocationItemCommandProvider _loadLocationItemCommandProvider { get; }


        public UpdateLoadLocationStatusPageViewModel(INavigationService navigationService,
                                                     IUserInteractionService userInteractionService,
                                                     IHaulService haulService,
                                                     ILoadLocationItemCommandProvider loadLocationItemCommandProvider)
        {
            _navigationService = navigationService;
            _userInteractionService = userInteractionService;
            _loadLocationItemCommandProvider = loadLocationItemCommandProvider;
            _haulService = haulService;

            UpdateStatusCommand = new DelegateCommand<LoadOperationStatus?>((s) => UpdateStatus(s.Value), CanUpdateStatus)
                .ObservesProperty(() => Load)
                .ObservesProperty(() => NextLoadOperationStatus);

            ReportDelayCommand = ReactiveCommand.CreateFromTask(NavigateToReportDelay, 
                this.WhenAnyObservable(x => x.ReportDelayCommand.IsExecuting)
                    .Select(x => !x)
                    .StartWith(true));
        }

        LoadOperation Location => LoadLocationItem?.LoadOperation;

        private Haul _haul;
        public Haul Haul
        {
            get => _haul;
            set => SetProperty(ref _haul, value, onChanged: () =>
             {
                 if (value != null) return;

                 var stack = new StackTrace().ToString();
                 Console.WriteLine(stack);
                 Debugger.Break();
             });
        }

        private Load _load;
        public Load Load
        {
            get => _load;
            set => SetProperty(ref _load, value, () => CanReportDelay = Load?.CanReportDelay ?? false);
        }

        // Needed because apparently Xamarin Forms resolves expression 'Load.CanReportDelay' to true when Load is null
        private bool _canReportDelay;
        public bool CanReportDelay
        {
            get => _canReportDelay;
            set => SetProperty(ref _canReportDelay, value);
        }

        private LoadOperationStatus _nextLoadOperationStatus;
        public LoadOperationStatus NextLoadOperationStatus
        {
            get => _nextLoadOperationStatus;
            set => SetProperty(ref _nextLoadOperationStatus, value);
        }

        private LoadLocationItemViewModel _loadLocationItem;
        public LoadLocationItemViewModel LoadLocationItem
        {
            get => _loadLocationItem;
            set => SetProperty(ref _loadLocationItem, value, () => RaisePropertyChanged(nameof(Location)));
        }

        public ICommand UpdateStatusCommand { get; }
        public ReactiveCommand<Unit, Unit> ReportDelayCommand { get; private set; }

        public ReactiveCommand<LoadOperation, Unit> CallLocationContactCommand =>
            _loadLocationItemCommandProvider.CallLocationContactCommand;

        public ReactiveCommand<LoadOperation, Unit> CallAppointmentContactCommand =>
            _loadLocationItemCommandProvider.CallAppointmentContactCommand;

        public ReactiveCommand<LoadOperation, Unit> StartMapDirectionsTaskCommand =>
            _loadLocationItemCommandProvider.StartMapDirectionsTaskCommand;

        public async void OnNavigatedTo(INavigationParameters parameters)
        {
            if (parameters.ContainsKey("haul") 
                && parameters.TryGetValue<Haul>("haul", out var haul) 
                && parameters.TryGetValue<int>("loadOperationId", out var loadOperationId))
            {
                Initialize(haul, loadOperationId);
            }
            else if (parameters.IsNavigationBack())
            {
                Initialize(LoadLocationItem.LoadOperation.Id);
            }
            else
            {
                await _userInteractionService.ShowAlertAsync("Error", "No Haul or Load Operation Id was provided");
                await _navigationService.GoBackToRootAsync();
                //TurnedOff dev debugger breaks! System.Diagnostics.Debugger.Break();
            }


        }

        public void OnNavigatedFrom(INavigationParameters parameters)
        {
        }

        public void Destroy()
        {
            ReportDelayCommand.Dispose();
            ReportDelayCommand = null;
            _loadLocationItemCommandProvider.Destroy();
        }

        void Initialize(Haul haul, int loadOperationId)
        {
            if(haul is null)
            {
                var stack = new StackTrace().ToString();
                Console.WriteLine(stack);
                //TurnedOff dev debugger breaks! System.Diagnostics.Debugger.Break();
                return;
            }

            Haul = haul;
            Initialize(loadOperationId);
        }

        void Initialize(int loadOperationId)
        {
            try
            {
                Load = Haul.Loads.Single();

                LoadLocationItem = new LoadLocationItemViewModel(Haul, loadOperationId);

                NextLoadOperationStatus = (LoadOperationStatus)(1 + Location.Status);
            }
            catch (Exception ex)
            {
                Microsoft.AppCenter.Crashes.Crashes.TrackError(ex);
                //TurnedOff dev debugger breaks! System.Diagnostics.Debugger.Break();
                Console.WriteLine(ex);
            }
        }

        async void UpdateStatus(LoadOperationStatus newLoadOperationStatus)
        {
            if (await GetStatusChangeUserConfirmationAsync(newLoadOperationStatus))
            {
                _userInteractionService.ShowLoading();

                bool success = await UpdateLoadOperationStatusAsync(newLoadOperationStatus);

                _userInteractionService.HideLoading();

                if (success)
                {
                    // Navigate back to previous page instead of reinitializing
                    await _navigationService.GoBackAsync();
                }
                else
                {
                    // Only reinitialize if we need to stay on the page (like for POD upload)
                    Initialize(Location.Id);
                }
            }
        }

        async Task<bool> GetStatusChangeUserConfirmationAsync(LoadOperationStatus newLoadOperationStatus)
        {
            if (!Location.IsPickup && newLoadOperationStatus == LoadOperationStatus.Completed)
            {
                return true;
            }

            // TODO: Remove this break of the MVVM Pattern
            string statusText = (string)(new LoadOperationStatusTextConverter()).Convert(newLoadOperationStatus, null, null, CultureInfo.CurrentCulture);

            return await _userInteractionService.ShowConfirmationAsync(
                string.Format(Resources.UpdateLoadOperationStatusMessageFormatText, statusText),
                Resources.ConfirmationText);
        }

        bool CanUpdateStatus(LoadOperationStatus? newLoadOperationStatus)
        {
            return Location != null && newLoadOperationStatus.Value == NextLoadOperationStatus;
        }

        async Task<bool> UpdateLoadOperationStatusAsync(LoadOperationStatus newLoadOperationStatus)
        {
            if (newLoadOperationStatus == LoadOperationStatus.Completed && Location.SourceType == LoadOperationType.Dropoff)
            {
                await NavigateToUploadPODAsync();

                NextLoadOperationStatus = (1 + Location.Status);
                return false;
            }
            else
            {
                await _haulService.UpdateLoadOperationStatusAsync(Haul, Load.Id, Location.Id, newLoadOperationStatus);

                // Force update so have status change reflected
                RaisePropertyChanged(nameof(LoadOperation));

                // If a pickup was completed, either automatically update single remaining pickup or dropoff to EnRoute or  
                //  ask user to select next pickup or drop-off to update status to EnRoute
                if (Location.Status == LoadOperationStatus.Completed && Location.SourceType == LoadOperationType.Pickup)
                {
                    await UpdateNextOperationToEnrouteAsync();
                }
            }

            NextLoadOperationStatus = (1 + Location.Status);
            return true;
        }

        async Task UpdateNextOperationToEnrouteAsync()
        {
            IEnumerable<LoadOperation> nextLogicalPendingLocations = GetNextLogicalPendingLoadOperations();

            LoadOperation loadOperationToEnroute;

            if (nextLogicalPendingLocations.Count() == 1)
            {
                // There's a single remaining pickup or drop-off. Update its status to EnRoute
                loadOperationToEnroute = nextLogicalPendingLocations.Single();
            }
            else
            {
                // Let user choose which next pickup/drop-off to EnRoute
                loadOperationToEnroute = await _userInteractionService.ShowPopupAsync<SelectLoadLocationToEnRoutePopupViewModel, LoadOperation>(
                new NavigationParameters
                {
                    { "haul", Haul },
                    { "locations", nextLogicalPendingLocations }
                });
            }

            await _haulService.UpdateLoadOperationStatusAsync(Haul, Load.Id, loadOperationToEnroute.Id, LoadOperationStatus.En_Route);

            await _navigationService.NavigateAsync(NavigationServiceKeys.MainUri);
        }

        IEnumerable<LoadOperation> GetNextLogicalPendingLoadOperations()
        {
            IEnumerable<LoadOperation> nextLogicalLoadOperations = Load.Pickups.Where(p => p.Status == LoadOperationStatus.Pending).ToList();

            if (!nextLogicalLoadOperations.Any())
            {
                // No pending pickups, get the pending dropoffs
                nextLogicalLoadOperations = Load.Dropoffs.Where(p => p.Status == LoadOperationStatus.Pending).ToList();
            }

            return nextLogicalLoadOperations;
        }

        async Task NavigateToUploadPODAsync()
        {
            var result = await _navigationService.NavigateAsync(NavigationServiceKeys.UploadPOD,
                new NavigationParameters
                {
                    { "haul",  Haul },
                    { "loadId", Load.Id },
                    { "loadOperationId", Location.Id }
                });

            if (!result.Success)
            {
                _userInteractionService.ShowAlertAsync("Error", "Failed to navigate to Upload POD page");
                Microsoft.AppCenter.Crashes.Crashes.TrackError(result.Exception);
                Console.WriteLine($"Navigation failed: {result.Exception}");
            }
        }

        async Task NavigateToReportDelay()
        {
            _userInteractionService.ShowLoading();

            var result = await _navigationService.NavigateAsync(NavigationServiceKeys.ReportDelay,
                            new NavigationParameters
                            {
                                { "haul", Haul },
                                { "loadId", Load.Id },
                                { "organizationId", Haul.Organization.Id }
                            });

            if(!result.Success)
            {
                //TurnedOff dev debugger breaks! System.Diagnostics.Debugger.Break();
                Console.WriteLine($"Exception encountered while attempting to Report a Delay for load: {Load.Id}");
                Console.WriteLine(result.Exception);
            }

            _userInteractionService.HideLoading();
        }

    }
}
