﻿using NCX.Logging.Interfaces;
using NCX.Mobile.Converters;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using NCX.Mobile.Models.Cache.PushOperations;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Navigation;
using System;
using System.Globalization;
using System.Threading.Tasks;
using System.Windows.Input;

namespace NCX.Mobile.ViewModels
{
    class ReportDelayPageViewModel : BindableBase, INavigatedAware
    {
        private IPushOperationService _pushOperationService { get; }
        private ILogService _logService { get; }
        private INavigationService _navigationService { get; }
        private NCX.Mobile.Services.IGeolocation _geolocation { get; }
        private IUserInteractionService _userInteractionService { get; }
        private IHaulService _haulService { get; }

        bool _isBusy;
        Haul _haul;
        int _loadId;
        int _organizationId;

        public bool IsBusy
        {
            get { return _isBusy; }
            set { SetProperty(ref _isBusy, value, nameof(IsBusy)); }
        }

        public ICommand ReportDelayCommand { get; private set; }

        public ReportDelayPageViewModel(
            IPushOperationService pushOperationService,
            ILogService logService,
            INavigationService navigationService,
            NCX.Mobile.Services.IGeolocation geolocation,
            IUserInteractionService userInteractionService,
            IHaulService haulService)
        {
            _pushOperationService = pushOperationService;
            _logService = logService;
            _navigationService = navigationService;
            _geolocation = geolocation;
            _userInteractionService = userInteractionService;
            _haulService = haulService;

            ReportDelayCommand = new DelegateCommand<LoadDelayReason?>(ExecuteReportDelayWithConfirmation);
        }

        public void OnNavigatedFrom(INavigationParameters parameters)
        {
        }

        public void OnNavigatedTo(INavigationParameters parameters)
        {
            _haul = (Haul)parameters["haul"];
            _loadId = (int)parameters["loadId"];
            _organizationId = (int)parameters["organizationId"];
        }

        async void ExecuteReportDelayWithConfirmation(LoadDelayReason? selectedDelayReason)
        {
            if (await _userInteractionService.ShowConfirmationAsync(
                // TODO: Remove this break of the MVVM Pattern
                string.Format(Resources.ReportDelayConfirmationText, (new LoadDelayReasonToTextConverter()).Convert(selectedDelayReason.Value, null, null, CultureInfo.CurrentCulture)),
                Resources.ConfirmationText))
            {
                ExecuteReportDelay(selectedDelayReason.Value);
            }
        }

        async void ExecuteReportDelay(LoadDelayReason selectedDelayReason)
        {
            try
            {
                IsBusy = true;
                _userInteractionService.ShowLoading();

                await AddLoadDelayAsync(selectedDelayReason);

                IsBusy = false;
                _userInteractionService.HideLoading();

                await _navigationService.GoBackAsync();
            }
            catch (Exception ex)
            {
                Microsoft.AppCenter.Crashes.Crashes.TrackError(ex);
                _logService.LogCritical(Constants.Log_Tag, "Failed to add 'report delay' operation", ex);

                throw ex;
            }
        }

        async Task AddLoadDelayAsync(LoadDelayReason delayReason)
        {
            var currentGeoPosition = _geolocation.GetLocation();

            await _pushOperationService.AddAndRunAsync(
                new CreateLoadDelayOperation(
                    currentGeoPosition,
                    delayReason,
                    _haul.Id,
                    _loadId,
                    _organizationId));

            await _haulService.AddLoadDelayAsync(_haul, _loadId, new LoadDelay()
            {
                Latitude = currentGeoPosition?.Latitude,
                Longitude = currentGeoPosition?.Longitude,
                Reason = delayReason
            });
        }
    }
}