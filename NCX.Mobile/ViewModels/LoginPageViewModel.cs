using LadyBug;
using Microsoft.AppCenter.Crashes;
using NCX.Logging.Interfaces;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using NCX.Mobile.Services.Impl;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Navigation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Windows.Input;

namespace NCX.Mobile.ViewModels
{
    class LoginPageViewModel : BindableBase, INavigatedAware
    {
        readonly ILogService _logService;
        readonly INavigationService _navigationService;
        readonly IUserService _userService;
        readonly IUserInteractionService _userInteractionService;
        readonly IAppSettings _appSettingsService;
        readonly ILocalize _localizeService;
        readonly Func<ApiHostManager> _apiHostManagerFactory;

        string _username, _password;

        public string Backend { get; private set; }

        public string Username
        {
            get { return _username; }
            set { SetProperty(ref _username, value); }
        }

        public string Password
        {
            get { return _password; }
            set { SetProperty(ref _password, value); }
        }

        public IEnumerable<Language> Languages { get; private set; }

        public ICommand LoginCommand { get; private set; }
        public ICommand ShowForgotPasswordCommand { get; private set; }
        public ICommand ShowEnterSecretCodeCommand { get; private set; }
        public ICommand ChangeLanguageCommand { get; private set; }
        public ICommand ShowTutorialCommand { get; private set; }

        public ICommand ShowTermsOfServiceCommand { get; private set; }
        public ICommand ShowPrivacyPolicyCommand { get; private set; }

        public LoginPageViewModel(
            ILogService logService,
            IUserInteractionService userInteractionService,
            INavigationService navigationService,
            IUserService userService,
            IAppSettings appSettingsService,
            UrlResolver urlResolver,
            ILocalize localizeService,
            Func<ApiHostManager> apiHostManagerFactory,
            IUserSessionService userSessionService)
        {
            _logService = logService;
            _userInteractionService = userInteractionService;
            _navigationService = navigationService;
            _userService = userService;
            _appSettingsService = appSettingsService;
            _localizeService = localizeService;
            _apiHostManagerFactory = apiHostManagerFactory;

            LoginCommand = new DelegateCommand(Login, CanExecuteLogin)
                .ObservesProperty(() => Username)
                .ObservesProperty(() => Password);

            ShowForgotPasswordCommand = new DelegateCommand(() => _navigationService.NavigateAsync($"/{NavigationServiceKeys.ResetUserPassword}"));
            ShowEnterSecretCodeCommand = new DelegateCommand(ShowEnterSecretCode);
            ChangeLanguageCommand = new DelegateCommand<Language>(ChangeLanguage);

            ShowTutorialCommand = NavigationServiceHelper.CreateNavToWebPageCommand(
                _navigationService,
                NavigationServiceKeys.Tutorial,
                Resources.TutorialPageTitle,
                string.Format(Constants.TutorialFormatUrl, _localizeService.CurrentLanguageCode));

            ShowTermsOfServiceCommand = new DelegateCommand(() => _userInteractionService.NavigateTo(new Uri(Constants.TermsOfServiceWebpageUri)));
            ShowPrivacyPolicyCommand = new DelegateCommand(() => _userInteractionService.NavigateTo(new Uri(Constants.PrivacyWebpageUri)));

            Languages = GetSupportedLanguages();
            Backend = _appSettingsService.ApiHost;
        }

        public void OnNavigatedTo(INavigationParameters parameters)
        {
            LoginByNavParameters(parameters);
        }

        public void OnNavigatedFrom(INavigationParameters parameters)
        {
        }

        async void Login()
        {
            _userInteractionService.ShowLoading();

            bool? wasLoginSuccessful = null;

            try
            {
                wasLoginSuccessful = await _userService.LoginAsync(Username, Password);
            }
            catch (ApiErrorException ex)
            {
                await NCXApiServiceHelper.HandleExceptionAsync(ex, _userInteractionService);
            }
            finally
            {
                _userInteractionService.HideLoading();
            }

            if (wasLoginSuccessful == true)
            {
                _logService.LogDebug(Constants.Log_Tag, $"Successful Login for {Username}");

                var result = await _navigationService.NavigateAsync(NavigationServiceKeys.MainUri, new NavigationParameters("?loadHaulsFromOnline=true"));
                if(!result.Success)
                {
                    //TurnedOff dev debugger breaks! System.Diagnostics.Debugger.Break();
                }
            }
            else if (wasLoginSuccessful == false)
            {
                _logService.LogHigh(Constants.Log_Tag, $"Login failed for {Username}");

                await _userInteractionService.ShowAlertAsync(Resources.UserLoginErrorMessageText, Resources.UserLoginErrorTitleText, Resources.CloseText);
            }
        }

        bool CanExecuteLogin()
        {
            return !string.IsNullOrWhiteSpace(Username) && !string.IsNullOrWhiteSpace(Password);
        }

        async void ShowEnterSecretCode()
        {
            if (await _userInteractionService.ShowPopupAsync<EnterSecretCodeViewModel, bool?>() == true)
            {
                await _navigationService.NavigateAsync($"/{NavigationServiceKeys.SelectApiHost}");
            }
        }

        async void ChangeLanguage(Language selectedLanguage)
        {
            _localizeService.SetLanguage(selectedLanguage.Code);

            await _navigationService.NavigateAsync($"/{NavigationServiceKeys.Login}");
        }

        IEnumerable<Language> GetSupportedLanguages()
        {
            return _localizeService.GetAllSupportedLanguages().Where(
                (supportedLanguage) => supportedLanguage.Code != _localizeService.CurrentLanguageCode);
        }

        // Navigate to login based on navigation parameters dictionary 
        async void LoginByNavParameters(INavigationParameters navParams)
        {
            if (navParams.IsNavigationBack())
            {
                return;
            }

            string host = WebUtility.UrlDecode((string)navParams["h"]);

            if (!string.IsNullOrWhiteSpace(host))
            {
                await _apiHostManagerFactory().SetApiHostAsync(host);

                Backend = _appSettingsService.ApiHost;
                RaisePropertyChanged(nameof(Backend));
            }

            Username = WebUtility.UrlDecode((string)navParams["u"]);
            Password = WebUtility.UrlDecode((string)navParams["p"]);

            if (LoginCommand.CanExecute(null))
            {
                LoginCommand.Execute(null);
            }
        }
    }
}
