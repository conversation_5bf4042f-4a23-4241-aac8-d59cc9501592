﻿// Remove IAutoInitialize. The attribute alone is enough in newer Prism versions.
// For example:
using System;
using Prism.Mvvm;
using Prism.Navigation;
using NCX.Mobile.Models;
using Prism.AppModel;

namespace NCX.Mobile.ViewModels
{
    public class ViewPODItemPageViewModel : BindableBase
    {
        private PODItem podItem;

        public PODItem PODItem
        {
            get => podItem;
            set => SetProperty(ref podItem, value);
        }
    }
}
