﻿using MvvmHelpers;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using Prism.AppModel;
using Prism.Mvvm;
using Prism.Navigation;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;

namespace NCX.Mobile.ViewModels
{
    class LoadDetailsPageViewModel : BindableBase, IInitialize, IPageLifecycleAware, IDestructible
    {
        private INavigationService _navigationService { get; }
        private IHaulCommandProvider _haulCommandProvider { get; }
        private ILoadLocationItemCommandProvider _loadLocationItemCommandProvider { get; }

        public LoadDetailsPageViewModel(INavigationService navigationService, IHaulCommandProvider haulCommandProvider, ILoadLocationItemCommandProvider loadLocationItemCommandProvider)
        {
            _navigationService = navigationService;
            _haulCommandProvider = haulCommandProvider;
            _loadLocationItemCommandProvider = loadLocationItemCommandProvider;
        }

        private Haul _haul;
        private Load _load;
        private bool _isBusy = true;

        public bool IsBusy
        {
            get => _isBusy;
            private set => SetProperty(ref _isBusy, value);
        }

        public Haul Haul
        {
            get => _haul;
            private set => SetProperty(ref _haul, value);
        }

        public Load Load
        {
            get => _load;
            private set => SetProperty(ref _load, value);
        }

        private IEnumerable<CargoItemViewModel> _cargoItems;
        public IEnumerable<CargoItemViewModel> CargoItems
        {
            get => _cargoItems;
            set => SetProperty(ref _cargoItems, value);
        }

        private IEnumerable<LoadLocationItemViewModel> _locationItems;
        public IEnumerable<LoadLocationItemViewModel> LocationItems
        {
            get => _locationItems;
            set => SetProperty(ref _locationItems, value);
        }

        public ICommand UpdateLoadStatusCommand => _haulCommandProvider.UpdateLoadStatusCommand;

        public ICommand CallAppointmentContactCommand => _loadLocationItemCommandProvider.CallAppointmentContactCommand;

        public ICommand CallLocationContactCommand => _loadLocationItemCommandProvider.CallLocationContactCommand;

        public ICommand StartMapDirectionsTaskCommand => _loadLocationItemCommandProvider.StartMapDirectionsTaskCommand;

        public void Initialize(INavigationParameters parameters)
        {
            Haul = parameters.GetValue<Haul>("haul");

            Initialize();

            firstLoad = true;
        }

        public void OnAppearing()
        {
            if (firstLoad)
            {
                firstLoad = false;
                return;
            }

            Initialize();
        }

        public void OnDisappearing()
        {
        }

        private bool firstLoad = false;
        private void Initialize()
        {
            IsBusy = true;

            Load = Haul.Loads.Single();
            RaisePropertyChanged(nameof(Load)); // force notify property changed to reflect sload changes

            CargoItems = CargoItemViewModel.GetLoadCargoItems(Load);

            LocationItems = Load.Operations.Where(loadOp => loadOp.IsActiveOrPending || loadOp.Status == LoadOperationStatus.Completed)
                                           .GroupBy(loadOp => loadOp.SourceType)
                                           .OrderBy(g => g.Key == LoadOperationType.Pickup ? 0 : 1)
                                           .Select(g => g.OrderBy(loadOp => loadOp.SourceType == LoadOperationType.Pickup ? 0 : 1)
                                                         .ThenBy(loadOp => loadOp.ExpectedDate)
                                                         .Select((loadOp, index) => new LoadLocationItemViewModel(_haul, loadOp.Id, index, g.Count())))
                                           .SelectMany(g => g);

            IsBusy = false;
        }

        public void Destroy()
        {
            _haulCommandProvider.Destroy();
            _loadLocationItemCommandProvider.Destroy();
        }
    }
}
