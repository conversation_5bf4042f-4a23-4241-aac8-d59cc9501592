﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reactive;
using System.Reactive.Linq;
using System.Threading;
using System.Threading.Tasks;
using NCX.Logging.Interfaces;
using NCX.Mobile.Events;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using NCX.Mobile.Services.Impl;
using Prism.AppModel;
using Prism.Events;
using Prism.Navigation;
using Prism.Services;
using ReactiveUI;
using Microsoft.Maui.Networking;
using System.ComponentModel; // MAUI Connectivity API

namespace NCX.Mobile.ViewModels
{
    public class MainPageViewModel : ReactiveObject, INavigatedAware, IApplicationLifecycleAware, IPageLifecycleAware, IDestructible
    {
        // Fields and Dependencies
        private readonly ILogService _logger;
        private readonly IUserService _userService;
        private readonly IUserInteractionService _userInteractionService;
        private readonly IHaulService _haulService;
        private readonly IHaulCommandProvider _haulCommandProvider;
        private readonly UserLogoutCommand _userLogoutCommand;
        private readonly IAppSettings _appSettings;
        private readonly IEventAggregator _eventAggregator;
        private readonly IPageDialogService _pageDialogService;
        private readonly ShowPopupOnNewLoadsService _showPopupOnNewLoadsService;
        private readonly INCXApiService _apiService;

        // MAUI Connectivity (static, no injection needed)
        private readonly IConnectivity _connectivity = Connectivity.Current;

        // State Fields
        private bool _isLoadingHauls;
        private IEnumerable<LoadItemGroup> _loadItemGroups;
        private int _loadCount;

        // Constructor
        public MainPageViewModel(
            ILogService logger,
            IUserService userService,
            IUserInteractionService userInteractionService,
            IHaulService haulService,
            IHaulCommandProvider haulCommandProvider,
            IEventAggregator eventAggregator,
            IPageDialogService pageDialogService,
            UserLogoutCommand userLogoutCommand,
            IAppSettings appSettings,
            INCXApiService apiService,
            ShowPopupOnNewLoadsService showPopupOnNewLoadsService)
        {
            _logger = logger;
            _userService = userService;
            _userInteractionService = userInteractionService;
            _haulService = haulService;
            _haulCommandProvider = haulCommandProvider;
            _userLogoutCommand = userLogoutCommand;
            _appSettings = appSettings;
            _eventAggregator = eventAggregator;
            _pageDialogService = pageDialogService;
            _showPopupOnNewLoadsService = showPopupOnNewLoadsService;
            _apiService = apiService;

            // Reactive Commands
            RefreshCommand = ReactiveCommand.CreateFromTask(ForceLoadHauls);
            LoadHaulsCommand = ReactiveCommand.CreateFromTask<bool>(LoadHaulsAsync);
            ToggleLoadingCommand = ReactiveCommand.Create<bool>(OnToggleLoadingCommandExecuted);
            ToggleTravelCommand = ReactiveCommand.CreateFromTask(OnToggleTravelCommandExecuted);

            // Busy State Observables
            _isBusyHelper = this
                .WhenAnyObservable(x => x.RefreshCommand.IsExecuting, x => x.LoadHaulsCommand.IsExecuting, (refresh, loadHauls) => !refresh && !loadHauls)
                .ToProperty(this, x => x.IsBusy, initialValue: false);

            // MAUI Connectivity Observable
            _isNetworkConnectivityDown = Observable.FromEventPattern<ConnectivityChangedEventArgs>(
                h => _connectivity.ConnectivityChanged += h,
                h => _connectivity.ConnectivityChanged -= h)
                .Select(e => e.EventArgs.NetworkAccess != NetworkAccess.Internet)
                .StartWith(_connectivity.NetworkAccess != NetworkAccess.Internet) // Initial state
                .ToProperty(this, nameof(IsNetworkConnectivityDown));

            // Log initial state for debugging
            _logger?.LogDebug(Constants.Log_Tag, $"Initial MAUI Network Access: {_connectivity.NetworkAccess}");

            // Force initial UI refresh
            Task.Run(async () =>
            {
                await Task.Delay(100);
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(IsNetworkConnectivityDown)));
            });

            // Observing IsActive from UserService
            _isActiveHelper = _userService.WhenIsActiveChanged()
                .ToProperty(this, x => x.IsActive, initialValue: false);

            // Set Current User
            User = _userService.CurrentSession?.User;
        }

        // Observables
        private ObservableAsPropertyHelper<bool> _isActiveHelper;
        public bool IsActive => _isActiveHelper?.Value ?? false;

        private ObservableAsPropertyHelper<bool> _isBusyHelper;
        public bool IsBusy => _isBusyHelper?.Value ?? false;

        private ObservableAsPropertyHelper<bool> _isNetworkConnectivityDown;
        public bool IsNetworkConnectivityDown => _isNetworkConnectivityDown?.Value ?? false;

        public event PropertyChangedEventHandler PropertyChanged;

        // IFTA Enabled
        private bool _isIftaEnabled;
        public bool IsIftaEnabled
        {
            get => _isIftaEnabled;
            set => this.RaiseAndSetIfChanged(ref _isIftaEnabled, value);
        }

        // Load Items
        public IEnumerable<LoadItemGroup> LoadItemGroups
        {
            get => _loadItemGroups;
            private set => this.RaiseAndSetIfChanged(ref _loadItemGroups, value);
        }

        public int LoadCount
        {
            get => _loadCount;
            set => this.RaiseAndSetIfChanged(ref _loadCount, value);
        }

        public User User { get; }

        // Reactive Commands
        public ReactiveCommand<Unit, Unit> ToggleTravelCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> RefreshCommand { get; private set; }
        private ReactiveCommand<bool, Unit> LoadHaulsCommand { get; set; }
        private ReactiveCommand<bool, Unit> ToggleLoadingCommand { get; set; }

        // Command Providers
        public ReactiveCommand<Haul, Unit> ShowLoadDetailsCommand => _haulCommandProvider.ShowLoadDetailsCommand;
        public ReactiveCommand<Haul, Unit> UpdateLoadStatusCommand => _haulCommandProvider.UpdateLoadStatusCommand;

        // Prism Navigation - INavigatedAware
        public async void OnNavigatedTo(INavigationParameters parameters)
        {
            var refresh = parameters.ContainsKey("loadHaulsFromOnline") || LoadsNeedRefresh();
            await LoadHaulsCommand.Execute(refresh);

            _showPopupOnNewLoadsService.OnMainPageNavigated();
        }

        public void OnNavigatedFrom(INavigationParameters parameters)
        {
            // Called when navigating away
        }

        // Prism Page Lifecycle - IPageLifecycleAware
        public void OnAppearing()
        {
            _eventAggregator.GetEvent<HaulsFetchedFromOnline>().Subscribe(OnHaulsFetchedFromOnline);
            _eventAggregator.GetEvent<HaulStateChangedFromViewedToPending>().Subscribe(OnHaulStateChangedFromViewedToPending);
        }

        public void OnDisappearing()
        {
            _eventAggregator.GetEvent<HaulsFetchedFromOnline>().Unsubscribe(OnHaulsFetchedFromOnline);
            _eventAggregator.GetEvent<HaulStateChangedFromViewedToPending>().Unsubscribe(OnHaulStateChangedFromViewedToPending);
        }

        // Prism App Lifecycle - IApplicationLifecycleAware
        public void OnResume()
        {
            LoadHaulsCommand.Execute(false);
        }

        public void OnSleep()
        {
            // Called when app goes to background
        }

        // Toggle Loading
        private void OnToggleLoadingCommandExecuted(bool isBusy)
        {
            if (isBusy)
            {
                _userInteractionService.ShowLoading();
            }
            else
            {
                _userInteractionService.HideLoading();
            }
        }

        // Toggle Travel
        private async Task OnToggleTravelCommandExecuted()
        {
            if (IsActive)
            {
                await _userService.StopTravelAsync();
            }
            else
            {
                await _userService.StartTravelAsync();
            }
        }

        private async Task<bool> GetPowerUnit()
        {
            // TODO
            return true;

            if (_userService.PowerUnitId is null)
            {
                _userInteractionService.ShowLoading();
                try
                {
                    var units = await _apiService.GetPowerUnits(_userService.CurrentSession.AuthToken);
                    
                    // Check if units is empty or null
                    if (units == null || !units.Any())
                    {
                        await _pageDialogService.DisplayAlertAsync(Resources.Error, 
                            "No power units available. Please contact support.", 
                            Resources.OKButtonText);
                        return false;
                    }

                    var btns = new List<IActionSheetButton>
                    {
                        ActionSheetButton.CreateCancelButton(Resources.CancelButtonText)
                    };

                    btns.AddRange(units.Select(u =>
                        ActionSheetButton.CreateButton(
                            string.IsNullOrEmpty(u.Nickname) ? u.Number : u.Nickname,
                            () => _userService.PowerUnitId = u.Id)));

                    _userInteractionService.HideLoading();

                    await _pageDialogService.DisplayActionSheetAsync("Select Power Unit", btns.ToArray());
                }
                catch (Exception ex)
                {
                    _userInteractionService.HideLoading();
                    await _pageDialogService.DisplayAlertAsync(Resources.Error, 
                        "Failed to retrieve power units. Please check your connection and try again.", 
                        Resources.OKButtonText);
                    _logger.LogCritical(Constants.Log_Tag, "GetPowerUnits", ex);
                    return false;
                }
            }

            return _userService.PowerUnitId != null;
        }

        // Force Load
        private Task ForceLoadHauls(CancellationToken cancellationToken)
            => LoadHaulsAsync(true, cancellationToken);

        // Load Hauls
        private async Task LoadHaulsAsync(bool loadHaulsFromOnline = false, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug(Constants.Log_Tag, $"LoadHaulsAsync - Starting with loadHaulsFromOnline={loadHaulsFromOnline}");
                _userInteractionService.ShowLoading();
                _isLoadingHauls = true;
                
                var hauls = await GetDriverHaulsAsync(loadHaulsFromOnline, cancellationToken);
                _logger.LogDebug(Constants.Log_Tag, $"LoadHaulsAsync - GetDriverHaulsAsync returned {hauls?.Count() ?? 0} hauls");
                
                if (hauls == null)
                {
                    _logger.LogDebug(Constants.Log_Tag, "LoadHaulsAsync - GetDriverHaulsAsync returned null");
                    hauls = Enumerable.Empty<Haul>();
                }
                
                foreach (var haul in hauls)
                {
                    _logger.LogDebug(Constants.Log_Tag, $"Haul {haul.Id}: Status={haul.Status}");
                }
                
                CreateLoadItems(hauls);
            }
            catch (ApiUnauthorizedException)
            {
                _logger.LogDebug(Constants.Log_Tag, "LoadHaulsAsync - ApiUnauthorizedException");
                _userLogoutCommand.Execute(true);
            }
            catch (DataConsistencyException ex)
            {
                await _userInteractionService.ShowAlertAsync(
                    string.Format(Resources.InvalidDataFromBackendMessageText, ex.Message),
                    Resources.InvalidDataFromBackendTitleText);
            }
            catch (SQLite.SQLiteException sqlEx)
            {
                _logger.LogCritical(Constants.Log_Tag, "SQLite", sqlEx);
                await _userInteractionService.ShowAlertAsync(
                    "We ran into a problem refreshing your Load data. Please try again.", "Data Sync Error");
            }
            catch (Exception ex)
            {
                _logger.LogHigh(Constants.Log_Tag, "LoadHaulsAsync - Unexpected error", ex);
                await _userInteractionService.ShowAlertAsync(
                    "Error loading hauls. Please try again.",
                    "Error");
            }
            finally
            {
                _isLoadingHauls = false;
                _userInteractionService.HideLoading();
            }
        }

        // Alternative load approach
        private async Task LoadHaulsNEWAsync(bool loadHaulsFromOnline = false, CancellationToken cancellationToken = default)
        {
            try
            {
                _userInteractionService.ShowLoading();
                _isLoadingHauls = true;

                IEnumerable<Haul> hauls;
                if (loadHaulsFromOnline)
                {
                    using (await UserSessionSyncContext.AsyncLock.LockAsync())
                    {
                        hauls = await _haulService.GetDriverHaulsAsync(false);
                    }
                }
                else
                {
                    hauls = await _haulService.GetDriverHaulsAsync(true);
                }

                LoadItemGroups = hauls.GroupBy(haul => haul.Status)
                   .OrderBy(g => g.Key == HaulStatus.En_Route || g.Key == HaulStatus.Checked_In ? 0 : 1)
                   .Select(g => new LoadItemGroup(g.OrderBy(haul => haul.CreatedAt)
                   .Select(haul => new LoadItemViewModel(haul))));

                LoadCount = hauls.Count();
                var firstHaul = hauls.FirstOrDefault();
                IsIftaEnabled = firstHaul?.Organization.IftaReporting ?? false;
            }
            catch (ApiUnauthorizedException)
            {
                _userLogoutCommand.Execute(true);
            }
            catch (DataConsistencyException ex)
            {
                await _userInteractionService.ShowAlertAsync(
                    string.Format(Resources.InvalidDataFromBackendMessageText, ex.Message),
                    Resources.InvalidDataFromBackendTitleText);
            }
            catch (SQLite.SQLiteException sqlEx)
            {
                _logger.LogCritical(Constants.Log_Tag, "SQLite", sqlEx);
                await _userInteractionService.ShowAlertAsync(
                    "We ran into a problem refreshing your Load data. Please try again.", "Data Sync Error");
            }
            catch (Exception ex)
            {
                Microsoft.AppCenter.Crashes.Crashes.TrackError(ex);
                _logger.LogCritical(Constants.Log_Tag, string.Empty, ex);
            }
            finally
            {
                _isLoadingHauls = false;
                _userInteractionService.HideLoading();
            }
        }

        private async Task<IEnumerable<Haul>> GetDriverHaulsAsync(bool loadHaulsFromOnline, CancellationToken cancellationToken)
        {
            if (loadHaulsFromOnline)
            {
                using (await UserSessionSyncContext.AsyncLock.LockAsync())
                {
                    return await _haulService.GetDriverHaulsAsync(false);
                }
            }
            else
            {
                return await _haulService.GetDriverHaulsAsync(true);
            }
        }

        private void CreateLoadItems(IEnumerable<Haul> hauls)
        {
            _logger.LogDebug(Constants.Log_Tag, $"CreateLoadItems - Input hauls count: {hauls.Count()}");
            
            var groups = hauls.GroupBy(haul => haul.Status)
                .OrderBy(g => g.Key == HaulStatus.En_Route || g.Key == HaulStatus.Checked_In ? 0 : 1)
                .Select(g => new LoadItemGroup(g.OrderBy(haul => haul.CreatedAt)
                .Select(haul => new LoadItemViewModel(haul)))).ToList();
                
            _logger.LogDebug(Constants.Log_Tag, $"CreateLoadItems - Created groups count: {groups.Count}");
            
            LoadItemGroups = groups;
            LoadCount = hauls.Count();
            
            _logger.LogDebug(Constants.Log_Tag, $"CreateLoadItems - LoadCount set to: {LoadCount}");
        }

        private bool LoadsNeedRefresh()
        {
            if (_appSettings.LoadsNeedRefresh == true)
            {
                _appSettings.LoadsNeedRefresh = null;
                return true;
            }
            else
            {
                return false;
            }
        }

        // INavigatedAware / IDestructible
        public void Destroy()
        {
            _isActiveHelper?.Dispose();
            _isBusyHelper?.Dispose();
            _isNetworkConnectivityDown?.Dispose();

            LoadHaulsCommand?.Dispose();
            RefreshCommand?.Dispose();
            ToggleLoadingCommand?.Dispose();

            _haulCommandProvider?.Destroy();
        }

        // Event Handlers
        private void OnHaulsFetchedFromOnline(IEnumerable<Haul> hauls)
        {
            if (!_isLoadingHauls)
            {
                CreateLoadItems(hauls);
            }
        }

        private void OnHaulStateChangedFromViewedToPending()
        {
            LoadHaulsCommand.Execute(false);
        }
    }
}
