﻿using System.Windows.Input;
using NCX.Mobile.Helpers;
using NCX.Mobile.Services;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Navigation;

namespace NCX.Mobile.ViewModels
{
    // Base popup view-model
    // Derived classes are not able to use INavigationService depedency-injected in constructor
    abstract class PopupBaseViewModel<TResult> : BindableBase, INavigationResultProducer<TResult>, IInitialize, IDestructible
    {
        private ILoadLocationItemCommandProvider _loadLocationItemCommandProvider { get; }
        private IHaulCommandProvider _haulCommandProvider { get; }

        public PopupBaseViewModel(ILoadLocationItemCommandProvider loadLocationItemCommandProvider, IHaulCommandProvider haulCommandProvider)
        {
            _loadLocationItemCommandProvider = loadLocationItemCommandProvider;
            _haulCommandProvider = haulCommandProvider;
        }

        public DelegateCommand<TResult> SetResultCommand { get; set; }

        public ICommand CallLocationContactCommand => _loadLocationItemCommandProvider.CallLocationContactCommand;

        public ICommand CallAppointmentContactCommand => _loadLocationItemCommandProvider.CallAppointmentContactCommand;

        public ICommand StartMapDirectionsTaskCommand => _loadLocationItemCommandProvider.StartMapDirectionsTaskCommand;

        public virtual void Initialize(INavigationParameters parameters)
        {
        }

        protected virtual void Destroy()
        {
        }

        void IDestructible.Destroy()
        {
            Destroy();
            _loadLocationItemCommandProvider.Destroy();
            _haulCommandProvider.Destroy();
        }
    }
}
