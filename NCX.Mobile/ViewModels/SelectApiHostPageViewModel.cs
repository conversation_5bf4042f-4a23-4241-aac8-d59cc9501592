﻿using System;
using NCX.Mobile.Helpers;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Navigation;

namespace NCX.Mobile.ViewModels
{
    class SelectApiHostPageViewModel : BindableBase
    {
        readonly INavigationService _navigationService;
        readonly IUserInteractionService _userInteractionService;
        readonly ApiHostManager _apiHostManager;

        public DelegateCommand<string> SetApiHostCommand { get; private set; }

        public SelectApiHostPageViewModel(
            INavigationService navigationService,
            IUserInteractionService userInteractionService,
            ApiHostManager apiHostManager)
        {
            _navigationService = navigationService;
            _userInteractionService = userInteractionService;
            _apiHostManager = apiHostManager;

            SetApiHostCommand = new DelegateCommand<string>(SetApiHost);
        }

        async void SetApiHost(string apiHost)
        {
            apiHost = apiHost.Trim();
            if (string.IsNullOrWhiteSpace(apiHost) || Uri.TryCreate(apiHost, UriKind.Absolute, out _))
            {
                await _apiHostManager.SetApiHostAsync(apiHost);

                await _userInteractionService.ShowAlertAsync($"{Resources.APIHostChangedMessageText}\n{apiHost}", Resources.SuccessTitleText);

                await _navigationService.NavigateAsync($"/{NavigationServiceKeys.Login}");
            }
            else
            {
                await _userInteractionService.ShowAlertAsync(Resources.MalformedUrlMessageText, Resources.MalformedUrlTitleText);
            }
        }
    }
}
