﻿using NCX.Mobile.Helpers;
using Prism.Commands;
using System;
using System.Windows.Input;

namespace NCX.Mobile.ViewModels
{
    class EnterSecretCodeViewModel : PopupBaseViewModel<bool?>
    {
        string _secretCodeText;

        public string SecretCodeText
        {
            get { return _secretCodeText; }
            set { SetProperty(ref _secretCodeText, value); }
        }

        public ICommand EnterSecretCodeCommand { get; private set; }

        public EnterSecretCodeViewModel(ILoadLocationItemCommandProvider loadLocationItemCommandProvider, IHaulCommandProvider haulCommandProvider) : base(loadLocationItemCommandProvider, haulCommandProvider)
        {
            EnterSecretCodeCommand = new DelegateCommand(EnterSecretCode, CanExecuteEnterSecretCode)
                   .ObservesProperty(() => SecretCodeText);
        }

        void EnterSecretCode()
        {
            SetResultCommand.Execute(IsSecretCodeCorrect());
        }

        bool CanExecuteEnterSecretCode()
        {
            return !string.IsNullOrWhiteSpace(SecretCodeText);
        }

        bool IsSecretCodeCorrect()
        {
#if DEBUG
            return true;
#else
            return SecretCodeText == $"{(DateTime.Now.Month * 4).ToString("00")}{(DateTime.Now.Day * 3).ToString("00")}";
#endif
        }
    }
}
