﻿using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using NCX.Mobile.Services.Impl;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Navigation;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Input;

namespace NCX.Mobile.ViewModels
{
    class MainMasterDetailPageViewModel : BindableBase, INavigatedAware
    {
        private readonly INavigationService _navigationService;
        private readonly IUserService _userService;

        public IEnumerable<MainMenuItem> MenuItems { get; set; }
        public User User { get; private set; }
        public Uri UserAvatarUri { get; private set; }
        public Version AppVersion { get; private set; }
        public ICommand ExecuteMainMenuItemCommand { get; private set; }

        public MainMasterDetailPageViewModel(INavigationService navigationService,
                                             IUserSessionService userSessionService,
                                             UserLogoutCommand userLogoutCommand,
                                             UrlResolver urlResolver,
                                             IPlatformTools platformTools,
                                             IUserService userService)
        {
            _navigationService = navigationService;
            _userService = userService;

            User = userSessionService.CurrentSession.User;
            UserAvatarUri = User.AvatarUri != null ? urlResolver.GetUrl(User.AvatarUri) : null;

            MenuItems = CreateMenuItems(userLogoutCommand);
            ExecuteMainMenuItemCommand = new DelegateCommand<MainMenuItem>((item) => item.Command?.Execute(null));

            AppVersion = platformTools.AppVersion;
        }

        public void OnNavigatedTo(INavigationParameters parameters)
        {
            // Handle navigation parameters and initialize MainPage
            if (Application.Current?.MainPage is FlyoutPage flyoutPage && flyoutPage.Detail is NavigationPage navPage)
            {
                if (navPage.CurrentPage is MainPage mainPage)
                {
                    var mainPageVm = mainPage.BindingContext as MainPageViewModel;
                    mainPageVm?.OnNavigatedTo(parameters); // Pass parameters to MainPageViewModel
                }
            }
        }

        public void OnNavigatedFrom(INavigationParameters parameters) { }

        private IEnumerable<MainMenuItem> CreateMenuItems(UserLogoutCommand userLogoutCommand)
        {
            var menuItems = new List<MainMenuItem>
            {
                new MainMenuItem(FontIcons.Dashboard, Resources.Dashboard, CreateNavigateCommand(
                    string.Format("{0}?loadHaulsFromOnline=true", NavigationServiceKeys.Main)))
            };

            if (User.IftaEnabled)
            {
                menuItems.Add(new MainMenuItem(FontIcons.Truck, Resources.RecordFuelStop, CreateNavigateCommand(NavigationServiceKeys.RecordFuelStop)));
            }

            menuItems.AddRange(new[]
            {
                new MainMenuItem(FontIcons.Cog, Resources.Settings)
                {
                    new MainMenuItem(FontIcons.User, Resources.YourProfile, CreateNavigateCommand(NavigationServiceKeys.UserProfile), true),
                    new MainMenuItem(FontIcons.Language, Resources.ChangeLanguageText, CreateNavigateCommand(NavigationServiceKeys.ChangeAppLanguage), true),
                },
                new MainMenuItem(FontIcons.Help, Resources.Help, CreateNavigateCommand(NavigationServiceKeys.Settings)),
                new MainMenuItem(FontIcons.Off, Resources.SignOut, userLogoutCommand),
            });

            return menuItems;
        }

        private ICommand CreateNavigateCommand(string navigationUri)
        {
            return new DelegateCommand(async () => await HandleNavigation($"{NavigationServiceKeys.NavigationPage}/{navigationUri}"));
        }

        private async Task HandleNavigation(string uri)
        {
            // Handle navigation within the Detail's NavigationPage
            var result = await _navigationService.NavigateAsync(uri);
            if (!result.Success)
            {
                // Handle error (e.g., log the exception)
                System.Diagnostics.Debug.WriteLine($"Navigation failed: {result.Exception}");
            }
        }
    }
}