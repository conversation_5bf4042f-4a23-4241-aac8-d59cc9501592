﻿using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Navigation;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;

namespace NCX.Mobile.ViewModels
{
    class LanguagePageViewModel : BindableBase
    {
        readonly INavigationService _navigationService;
        readonly IUserInteractionService _userInteractionService;
        readonly ILocalize _localizeService;

        public IEnumerable<Language> Languages { get; private set; }

        public ICommand LanguageSelectionCommand { get; private set; }

        public LanguagePageViewModel(INavigationService navigationService,
                                     IUserInteractionService userInteractionService,
                                     ILocalize localizeService)
        {
            _navigationService = navigationService;
            _userInteractionService = userInteractionService;
            _localizeService = localizeService;

            LanguageSelectionCommand = new DelegateCommand<Language>(DoLanguageSelectionCommand);

            Languages = localizeService.GetAllSupportedLanguages();
        }

        async void DoLanguageSelectionCommand(Language selectedLanguage)
        {
            if (await _userInteractionService.ShowConfirmationAsync(string.Format(Resources.LanguageChangeConfirmation, selectedLanguage.DisplayName), Resources.ConfirmationText))
            {
                _localizeService.SetLanguage(selectedLanguage.Code);

                //Reset navigation to Main page. Assumed that change language option would be after login.
                await _navigationService.NavigateAsync(NavigationServiceKeys.MainUri);
            }
        }
    }
}
