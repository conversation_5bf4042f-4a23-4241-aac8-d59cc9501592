﻿using NCX.Mobile.Helpers;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using Prism.Commands;
using Prism.Ioc;
using Prism.Mvvm;
using Prism.Navigation;
using System.Windows.Input;

namespace NCX.Mobile.ViewModels
{
    class ResetUserPasswordPageViewModel : BindableBase
    {
        readonly INCXApiService _apiService;
        readonly IUserInteractionService _userInteractionService;
        readonly INavigationService _navigationService;
        private readonly IContainerProvider _containerProvider;

        string _email;

        public string Email
        {
            get { return _email; }
            set { SetProperty(ref _email, value); }
        }

        public ICommand ResetPasswordCommand { get; private set; }
        public ICommand NavigateToLoginCommand { get; private set; }

        public ResetUserPasswordPageViewModel(
            INCXApiService apiService,
            IUserInteractionService userInteractionService,
            INavigationService navigationService,
            IContainerProvider containerProvider)
        {
            _apiService = apiService;
            _userInteractionService = userInteractionService;
            _navigationService = navigationService;

            ResetPasswordCommand = new DelegateCommand(ResetPassword, () => !string.IsNullOrWhiteSpace(Email)).ObservesProperty(() => Email);
            NavigateToLoginCommand = new DelegateCommand(async () => await _navigationService.NavigateAsync($"/{NavigationServiceKeys.Login}"));
            _containerProvider = containerProvider;
        }

        async void ResetPassword()
        {
            _userInteractionService.ShowLoading();

            try
            {
                await _apiService.ResetUserPasswordAsync(Email);

                _userInteractionService.HideLoading();

                await _userInteractionService.ShowAlertAsync(
                    Resources.ResetPasswordSuccessfulMessageText,
                    Resources.ResetPasswordSuccessfulTitleText
                );

                NavigateToLoginCommand.Execute(null);
            }
            catch (ApiErrorException ex)
            {
                _userInteractionService.HideLoading();

                // Resolve required dependencies for ApiErrorActionHandler
                var logoutCommand = _containerProvider.Resolve<IUserServiceLogoutCommand>();
                var userInteractionService = _containerProvider.Resolve<IUserInteractionService>();

                // Pass your custom callback (OnResetPasswordFailed) to handle the error
                var handler = new ApiErrorActionHandler(logoutCommand, userInteractionService, OnResetPasswordFailed);
                handler.HandleException(ex);
            }
        }


        async void OnResetPasswordFailed()
        {
            await _userInteractionService.ShowAlertAsync(Resources.ResetPasswordErrorMessageText, Resources.ResetPasswordErrorMessageTitle);
        }
    }
}
