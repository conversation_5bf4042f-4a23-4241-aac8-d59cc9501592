﻿using NCX.Mobile.Helpers;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using System.Windows.Input;

namespace NCX.Mobile.ViewModels
{
    class ChangeUserPasswordPageViewModel : BindableBase
    {
        readonly IUserInteractionService _userInteractionService;
        readonly INavigationService _navigationService;
        readonly IUserService _userService;
        private readonly IContainerProvider _containerProvider;

        string _currentPassword, _newPassword, _newPasswordConfirmation;

        public string CurrentPassword
        {
            get { return _currentPassword; }
            set { SetProperty(ref _currentPassword, value); }
        }

        public string NewPassword
        {
            get { return _newPassword; }
            set { SetProperty(ref _newPassword, value); }
        }

        public string NewPasswordConfirmation
        {
            get { return _newPasswordConfirmation; }
            set { SetProperty(ref _newPasswordConfirmation, value); }
        }

        public ICommand UpdatePasswordCommand { get; private set; }

        public ChangeUserPasswordPageViewModel(IUserInteractionService userInteractionService,
                                               INavigationService navigationService,
                                               IUserService userService,
                                               IContainerProvider containerProvider)
        {
            _userInteractionService = userInteractionService;
            _navigationService = navigationService;
            _userService = userService;

            UpdatePasswordCommand = new DelegateCommand(UpdatePassword, CanExecuteUpdatePassword)
                                        .ObservesProperty(() => CurrentPassword)
                                        .ObservesProperty(() => NewPassword)
                                        .ObservesProperty(() => NewPasswordConfirmation);
            _containerProvider = containerProvider;
        }

        bool CanExecuteUpdatePassword()
        {
            return !string.IsNullOrWhiteSpace(CurrentPassword) &&
                   !string.IsNullOrWhiteSpace(NewPassword) &&
                   !string.IsNullOrWhiteSpace(NewPasswordConfirmation);
        }

        async void UpdatePassword()
        {
            if (!await ValidateAsync())
            {
                return;
            }

            _userInteractionService.ShowLoading();

            // Resolve needed dependencies for ApiErrorActionHandler 
            var logoutCommand = _containerProvider.Resolve<IUserServiceLogoutCommand>();
            var userInteractionService = _containerProvider.Resolve<IUserInteractionService>();

            var handler = new ApiErrorActionHandler(logoutCommand, userInteractionService, OnChangePasswordError);

            if (true == await _userService.UpdateUserPasswordAsync(CurrentPassword, NewPassword, handler))
            {
                _userInteractionService.HideLoading();
                await _userInteractionService.ShowAlertAsync(
                    Resources.PasswordChangedSuccessfullyMessage,
                    Resources.InforamationTitleText
                );
                await _navigationService.GoBackAsync();
            }
            else
            {
                _userInteractionService.HideLoading();
            }
        }



        async void OnChangePasswordError()
        {
            _userInteractionService.HideLoading();

            await _userInteractionService.ShowAlertAsync(Resources.UpdatePasswordErrorMessage, Resources.UpdatePasswordErrorTitle);
        }

        async Task<bool> ValidateAsync()
        {
            if (NewPassword.Length < 8)
            {
                await _userInteractionService.ShowAlertAsync(Resources.UpdatePasswordTooSmallErrorMessage, Resources.UpdatePasswordErrorTitle);

                return false;
            }

            if (NewPassword != NewPasswordConfirmation)
            {
                await _userInteractionService.ShowAlertAsync(Resources.UpdatePasswordNewPasswordDiffersMessage, Resources.UpdatePasswordErrorTitle);

                return false;
            }

            return true;
        }
    }
}