﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Reactive;
using System.Reactive.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using NCX.Logging.Interfaces;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using NCX.Mobile.Models.Cache.PushOperations;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using Prism.Commands;
using Prism.Navigation;
using ReactiveUI;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.ViewModels
{
    class UploadPODPageViewModel : ReactiveObject, INavigatedAware, IDestructible
    {
        private IUserService _userService { get; }
        private INavigationService _navigationService { get; }
        private IPushOperationService _pushOperationService { get; }
        private IUserInteractionService _userInteractionService { get; }
        private IHaulService _haulService { get; }
        private ILogService _logService { get; }
        private IAppPermissionsService _appPermissionsService { get; }
        private IDocumentScanner _documentScanner { get; }

        public UploadPODPageViewModel(
            INavigationService navigationService,
            IPushOperationService pushOperationService,
            IUserInteractionService userInteractionService,
            IHaulService haulService,
            ILogService logService,
            IDocumentScanner documentScanner,
            IUserService userService,
            IAppPermissionsService appPermissionsService)
        {
            _navigationService = navigationService;
            _pushOperationService = pushOperationService;
            _userInteractionService = userInteractionService;
            _haulService = haulService;
            _logService = logService;
            _documentScanner = documentScanner;
            _userService = userService;
            _appPermissionsService = appPermissionsService;

            PODItems = new ObservableCollection<PODItem>();
            PhotoViewerViewModel = new BrowsableItemCollectionViewModel<PODItem>(PODItems);

            TakePhotoCommand = ReactiveCommand.CreateFromTask(AddPODItemByTakingPhotoAsync);
            ChoosePhotoFromLibraryCommand = ReactiveCommand.CreateFromTask(AddPODItemFromPhotoInLibrary);

            RemovePODItemCommand = new DelegateCommand<PODItem>(RemovePODItem, (item) => PODItems.Count() > 0)
                .ObservesProperty(() => PhotoViewerViewModel.ItemCount);

            var canSendPOD = this.WhenAnyValue(x => x.IsBusy).Select(x => !x);
            SendAllPODItemsCommand = ReactiveCommand.CreateFromTask(SendAllPODItems, canSendPOD);
        }

        private Haul _currentHaul;
        private int _currentLoadId;
        private int _currentLoadOperationId;
        private bool _isBusy;

        public ObservableCollection<PODItem> PODItems { get; private set; }

        public BrowsableItemCollectionViewModel<PODItem> PhotoViewerViewModel { get; private set; }

        public bool IsBusy
        {
            get => _isBusy;
            set => this.RaiseAndSetIfChanged(ref _isBusy, value);
        }

        public ReactiveCommand<Unit, Unit> TakePhotoCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> ChoosePhotoFromLibraryCommand { get; private set; }

        public ReactiveCommand<Unit, Unit> SendAllPODItemsCommand { get; private set; }
        public ICommand RemovePODItemCommand { get; private set; }
        public ICommand ShowPreviousPhotoCommand => PhotoViewerViewModel.SetPreviousItemCurrentCommand;
        public ICommand ShowNextPhotoCommand => PhotoViewerViewModel.SetNextItemCurrentCommand;

        public void OnNavigatedTo(INavigationParameters parameters)
        {
            if (!parameters.IsNavigationBack())
            {
                _currentHaul = parameters.GetValue<Haul>("haul");
                _currentLoadId = parameters.GetValue<int>("loadId");
                _currentLoadOperationId = parameters.GetValue<int>("loadOperationId");
            }
        }

        public void OnNavigatedFrom(INavigationParameters parameters)
        {
        }

        public void Destroy()
        {
            TakePhotoCommand.Dispose();
            TakePhotoCommand = null;

            ChoosePhotoFromLibraryCommand.Dispose();
            ChoosePhotoFromLibraryCommand = null;
        }

        async Task AddPODItemByTakingPhotoAsync()
        {
            IsBusy = true;
            _userInteractionService.ShowLoading();

            try
            {
                var encodedImage = await _documentScanner.ScanDocumentAsync();

                if (!string.IsNullOrEmpty(encodedImage))
                {
                    AddPODItem(new PODItem(encodedImage));
                }
            }
            catch (Exception ex)
            {
                Microsoft.AppCenter.Crashes.Crashes.TrackError(ex);
                Console.WriteLine(ex);
            }
            finally
            {
                _userInteractionService.HideLoading();
                IsBusy = false;
            }
        }

        async Task AddPODItemFromPhotoInLibrary()
        {
            IsBusy = true;
            _userInteractionService.ShowLoading();

            var encodedImage = await _documentScanner.PickDocumentAsync();

            if (!string.IsNullOrEmpty(encodedImage))
            {
                AddPODItem(new PODItem(encodedImage));
            }

            _userInteractionService.HideLoading();
            IsBusy = false;
        }

        async Task SendAllPODItems()
        {
            if (PODItems.Count < 1)
            {
                await _userInteractionService.ShowAlertAsync("Warning", "You need to add Proof of Delivery images before continuing");
                return;
            }

            if (!await _userInteractionService.ShowConfirmationAsync(Resources.SendPODConfirmationMessage, Resources.ConfirmationText))
            {
                return;
            }

            try
            {
                IsBusy = true;
                _userInteractionService.ShowLoading();

                await AddPODUploadOperationToCacheAsync();

                await _haulService.UpdateLoadOperationStatusAsync(_currentHaul, _currentLoadId, _currentLoadOperationId, LoadOperationStatus.Completed);

                if (_currentHaul.Status == HaulStatus.Completed)
                {
                    await _navigationService.NavigateAsync(NavigationServiceKeys.MainUri);
                }
                else
                {
                    await UpdateNextDropoffToEnrouteAsync();
                }
            }
            catch (Exception ex)
            {
                Microsoft.AppCenter.Crashes.Crashes.TrackError(ex);
                _logService.LogCritical(Constants.Log_Tag, "Sending POD failed", ex);
                throw;
            }
            finally
            {
                _userInteractionService.HideLoading();
                IsBusy = false;
            }
        }

        async void RemovePODItem(PODItem podItem)
        {
            if (await _userInteractionService.ShowConfirmationAsync(Resources.RemovePhotoConfirmationText, Resources.ConfirmationText))
            {
                PODItems.Remove(podItem);
            }
        }

        async Task AddPODUploadOperationToCacheAsync()
        {
            await _pushOperationService.AddAndRunAsync(CreatePODUploadOperation(PODItems));
        }

        SendPODOperation CreatePODUploadOperation(IEnumerable<PODItem> podItems)
        {
            var imageList = new List<LoadImage>();

            foreach (PODItem podItem in podItems)
            {
                imageList.Add(CreatePODImage(podItem));
            }

            return new SendPODOperation()
            {
                LoadOperationId = _currentLoadOperationId,
                LoadImageListSerialized = LoadImageListSerializer.Serialize(imageList)
            };
        }

        LoadImage CreatePODImage(PODItem podItem)
        {
            return new LoadImage()
            {
                Data = podItem.EncodedImage,
                MimeType = FileUtils.GetMimeTypeFromImageFilePath("image.png")
            };
        }

        void AddPODItem(PODItem podItem)
        {
            PODItems.Add(podItem);
            PhotoViewerViewModel.CurrentPosition = PODItems.Count - 1;
        }

        async Task UpdateNextDropoffToEnrouteAsync()
        {
            LoadOperation loadOperationToEnroute;

            Load currentLoad = _currentHaul.Loads.Single();
            IEnumerable<LoadOperation> pendingDropoffs =
                currentLoad.Dropoffs.Where(d => d.Status == LoadOperationStatus.Pending);

            if (pendingDropoffs.Count() == 1)
            {
                loadOperationToEnroute = pendingDropoffs.Single();
            }
            else
            {
                loadOperationToEnroute = await _userInteractionService.ShowPopupAsync<SelectLoadLocationToEnRoutePopupViewModel, LoadOperation>(
                    new NavigationParameters()
                    {
                        {"haul", _currentHaul },
                        { "locations", pendingDropoffs }
                    });
            }

            await _haulService.UpdateLoadOperationStatusAsync(_currentHaul, _currentLoadId, loadOperationToEnroute.Id, LoadOperationStatus.En_Route);

            await _navigationService.NavigateAsync(NavigationServiceKeys.MainUri);
        }
    }
}
