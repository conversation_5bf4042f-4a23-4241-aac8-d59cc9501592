﻿using MvvmHelpers;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using Prism.Commands;
using Prism.Navigation;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;

namespace NCX.Mobile.ViewModels
{
    class SelectLoadLocationToEnRoutePopupViewModel : PopupBaseViewModel<LoadOperation>
    {
        Haul _haul;

        public ICommand UpdateLocationStatusCommand { get; }

        public ObservableRangeCollection<LoadLocationItemViewModel> Locations { get; }

        public SelectLoadLocationToEnRoutePopupViewModel(ILoadLocationItemCommandProvider loadLocationItemCommandProvider, IHaulCommandProvider haulCommandProvider)
            : base(loadLocationItemCommandProvider, haulCommandProvider)
        {
            Locations = new ObservableRangeCollection<LoadLocationItemViewModel>();

            UpdateLocationStatusCommand = new DelegateCommand<LoadOperation>(UpdateLocationStatus);
        }

        public override void Initialize(INavigationParameters parameters)
        {
            _haul = parameters.GetValue<Haul>("haul");
            try
            {
                var locations = parameters.GetValue<IEnumerable<LoadOperation>>("location");
                var pendingLocations = locations.Where(p => p.Status == LoadOperationStatus.Pending);
                Locations.ReplaceRange(pendingLocations.Select(l => new LoadLocationItemViewModel(_haul, l.Id)));

                RaisePropertyChanged(nameof(Locations));
            }
            catch (System.Exception ex)
            {
                //TurnedOff dev debugger breaks! System.Diagnostics.Debugger.Break();
                System.Console.WriteLine(ex);
            }
        }

        void UpdateLocationStatus(LoadOperation locationToUpdate)
        {
            SetResultCommand.Execute(locationToUpdate);
        }
    }
}
