﻿using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using NCX.Mobile.Services.Impl;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Navigation;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Windows.Input;
using System.Collections;

namespace NCX.Mobile.ViewModels
{
    class UserProfilePageViewModel : BindableBase, INavigatedAware
    {
        readonly IUserService _userService;
        readonly UrlResolver _urlResolver;
        readonly IUserInteractionService _userInteractionService;

        bool _isBusy;
        User _user;
        IEnumerable _driverInfoItemList;
        Uri _userAvatarUri;

        public bool IsBusy
        {
            get => _isBusy;
            private set => SetProperty(ref _isBusy, value);
        }

        public User User
        {
            get => _user;
            private set => SetProperty(ref _user, value);
        }

        public Uri UserAvatarUri
        {
            get => _userAvatarUri;
            private set => SetProperty(ref _userAvatarUri, value);
        }

        public IEnumerable DriverInfoItemList
        {
            get => _driverInfoItemList;
            private set => SetProperty(ref _driverInfoItemList, value);
        }

        public ICommand ShowChangePasswordCommand { get; private set; }

        public UserProfilePageViewModel(IUserService userService,
                                        UrlResolver urlResolver,
                                        IUserInteractionService userInteractionService,
                                        INavigationService navigationService)
        {
            _userService = userService;
            _urlResolver = urlResolver;
            _userInteractionService = userInteractionService;

            ShowChangePasswordCommand = new DelegateCommand(() => navigationService.NavigateAsync(NavigationServiceKeys.ChangeUserPassword));

            IsBusy = true;
        }

        public void OnNavigatedFrom(INavigationParameters parameters)
        {
        }

        public void OnNavigatedTo(INavigationParameters parameters)
        {
            if (!parameters.IsNavigationBack())
            {
                GetDriverInfoAsync();
            }
        }

        async void GetDriverInfoAsync()
        {
            _userInteractionService.ShowLoading();

            IsBusy = true;

            User = await _userService.GetDriverProfileAsync(CancellationToken.None);

            _userInteractionService.HideLoading();

            Initialize(User, _urlResolver);

            IsBusy = false;
        }

        void Initialize(User user, UrlResolver urlResolver)
        {
            User = user;
            UserAvatarUri = User.AvatarUri != null ? urlResolver.GetUrl(User.AvatarUri) : null;
            DriverInfoItemList = CreateDriverInfoItems(User);
        }

        static IEnumerable<TreeNodeltem> CreateDriverInfoItems(User driver)
        {
            return new[] {
                new TreeNodeltem(Resources.License)
                {
                    new TreeNodeltem(Resources.DriverLicenseState, driver.DriverLicenseState),
                    new TreeNodeltem(Resources.LicenseNumber, driver.DriverLicenseNumber),
                    new TreeNodeltem(Resources.LicenseExpirationDate, driver.DriverLicenseExpirationDate?.ToString("MMM d, yyyy"))
                },
                new TreeNodeltem(Resources.MedicalCertificate)
                {
                    new TreeNodeltem(Resources.MedicalCertificateNationalRegistrationNumber, driver.MedicalCertificateNationalRegistrationNumber),
                    new TreeNodeltem(Resources.MedicalCertificateExpirationDate, driver.MedicalCertificateExpirationDate?.ToString("MMM d, yyyy"))
                }
            };
        }
    }
}
