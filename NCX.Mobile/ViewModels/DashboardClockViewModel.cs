﻿using System;
using System.Reactive;
using System.Reactive.Linq;
using Prism.Navigation;
using ReactiveUI;

namespace NCX.Mobile.ViewModels
{
    class DashboardClockViewModel : ReactiveObject, IDestructible
    {
        private IDisposable _timer;

        public DashboardClockViewModel()
        {
            UpdateTimeCommand = ReactiveCommand.Create(OnUpdateTimeCommandExecuted);
            _timer = Observable.Interval(TimeSpan.FromSeconds(1), RxApp.MainThreadScheduler)
                .Select(_ => Unit.Default)
                .InvokeCommand(UpdateTimeCommand);
        }
        private DateTime _currentDateTime;
        public DateTime CurrentDateTime
        {
            get => _currentDateTime;
            set => this.RaiseAndSetIfChanged(ref _currentDateTime, value);
        }

        private ReactiveCommand<Unit, Unit> UpdateTimeCommand { get; }

        private void OnUpdateTimeCommandExecuted() => CurrentDateTime = DateTime.Now;

        public void Destroy()
        {
            _timer.Dispose();
            _timer = null;
        }
    }
}
