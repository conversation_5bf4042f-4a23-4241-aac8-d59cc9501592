﻿using NCX.Mobile.Helpers;
using Prism.Mvvm;
using Prism.Navigation;
using System;
using System.Net;

namespace NCX.Mobile.ViewModels
{
    public class WebViewPageViewModel : BindableBase, INavigatedAware
    {
        string _title;
        bool _isBusy;
        Uri _webPageUri;

        public bool IsBusy
        {
            set { SetProperty(ref _isBusy, value); }
            get { return _isBusy; }
        }

        public string Title
        {
            set { SetProperty(ref _title, value); }
            get { return _title; }
        }

        public Uri WebPageUri
        {
            set { SetProperty(ref _webPageUri, value); }
            get { return _webPageUri; }
        }

        public void OnNavigatedTo(INavigationParameters parameters)
        {
            if (!parameters.IsNavigationBack())
            {
                Title = (string)parameters["title"];
                WebPageUri = new Uri(WebUtility.UrlDecode((string)parameters["webPageUri"]));
            }
        }

        public void OnNavigatedFrom(INavigationParameters parameters)
        {
        }
    }
}
