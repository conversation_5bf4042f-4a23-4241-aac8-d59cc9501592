﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reactive;
using System.Reactive.Linq;
using System.Threading.Tasks;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using NCX.Mobile.Properties;
using NCX.Mobile.Services;
using Prism.Navigation;
using ReactiveUI;

namespace NCX.Mobile.ViewModels
{
    class SelectLoadLocationToUpdatePageViewModel : ReactiveObject, INavigatedAware, IDestructible
    {
        private INavigationService _navigationService { get; }
        private IUserInteractionService _userInteractionService { get; }
        private ILoadLocationItemCommandProvider _loadLocationItemCommandProvider { get; }

        private Haul _haul;

        public SelectLoadLocationToUpdatePageViewModel(INavigationService navigationService, IUserInteractionService userInteractionService, ILoadLocationItemCommandProvider loadLocationItemCommandProvider)
        {
            _navigationService = navigationService;
            _userInteractionService = userInteractionService;
            _loadLocationItemCommandProvider = loadLocationItemCommandProvider;

            UpdateLocationStatusCommand = ReactiveCommand.CreateFromTask<LoadOperation>(
                UpdateLocationStatus,
                this.WhenAnyObservable(x => x.UpdateLocationStatusCommand.IsExecuting)
                    .Select(x => !x)
                    .StartWith(true));
        }

        private Load _load;

        public Load Load
        {
            get => _load;
            set => this.RaiseAndSetIfChanged(ref _load, value);
        }

        private IEnumerable<LoadLocationItemViewModel> _locations;
        public IEnumerable<LoadLocationItemViewModel> Locations
        {
            get => _locations;
            set => this.RaiseAndSetIfChanged(ref _locations, value);
        }

        public ReactiveCommand<LoadOperation, Unit> UpdateLocationStatusCommand { get; private set; }

        public ReactiveCommand<LoadOperation, Unit> CallLocationContactCommand =>
            _loadLocationItemCommandProvider.CallLocationContactCommand;

        public ReactiveCommand<LoadOperation, Unit> CallAppointmentContactCommand =>
            _loadLocationItemCommandProvider.CallAppointmentContactCommand;

        public ReactiveCommand<LoadOperation, Unit> StartMapDirectionsTaskCommand =>
            _loadLocationItemCommandProvider.StartMapDirectionsTaskCommand;

        public void OnNavigatedTo(INavigationParameters parameters)
        {
            if (!parameters.IsNavigationBack() && !parameters.TryGetValue("haul", out _haul))
            {
                _userInteractionService.ShowAlertAsync("Error", "No Haul was provided");
                return;
            }

            Load = _haul.Loads.Single();

            var availableLocations = new List<LoadOperation>();

            LoadOperation activeLoadOperation = Load.GetActiveLoadOperation();

            // Active location should be the first one
            if (activeLoadOperation != null)
            {
                availableLocations.Add(activeLoadOperation);
            }

            LoadOperationType locationTypeToSelect = Load.HasActiveOrPendingPickups() ? LoadOperationType.Pickup : LoadOperationType.Dropoff;

            // Order locations by type and by expected date
            IEnumerable<LoadOperation> operationsToSelect = Load.GetOrderedLoadOperations(locationTypeToSelect, LoadOperationStatus.Pending);

            if (activeLoadOperation != null)
            {
                availableLocations.AddRange(operationsToSelect.Except(new LoadOperation[] { activeLoadOperation }).ToList());
            }
            else
            {
                availableLocations.AddRange(operationsToSelect.ToList());
            }

            Locations = availableLocations.Select(loadOp => new LoadLocationItemViewModel(_haul, loadOp.Id));
        }

        public void OnNavigatedFrom(INavigationParameters parameters)
        {
        }

        public void Destroy()
        {
            UpdateLocationStatusCommand.Dispose();
            UpdateLocationStatusCommand = null;
            _loadLocationItemCommandProvider.Destroy();
        }

        async Task UpdateLocationStatus(LoadOperation locationToUpdate)
        {
            _userInteractionService.ShowLoading();

            if (!await CanUpdateLocationStatusAsync(locationToUpdate))
            {
                _userInteractionService.HideLoading();
                return;
            }

            var result = await _navigationService.NavigateAsync(NavigationServiceKeys.UpdateLoadLocationStatus,
                new NavigationParameters
                {
                    { "haul", _haul },
                    { "loadOperationId", locationToUpdate.Id }
                });

            if(!result.Success)
            {
                //TurnedOff dev debugger breaks! System.Diagnostics.Debugger.Break();
            }

            _userInteractionService.HideLoading();
        }

        async Task<bool> CanUpdateLocationStatusAsync(LoadOperation locationToCheck)
        {
            try
            {
                int? activeloadOperationId = _load.GetActiveLoadOperation()?.Id;

                if (activeloadOperationId != null && activeloadOperationId != locationToCheck.Id)
                {
                    await _userInteractionService.ShowAlertAsync(Resources.CanUpdateActivePickupOrDropOffMessageText, Resources.CanUpdateActivePickupOrDropOffTitleText);

                    return false;
                }
                else
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                Microsoft.AppCenter.Crashes.Crashes.TrackError(ex);
                Console.WriteLine($"{nameof(SelectLoadLocationToUpdatePageViewModel)}.{nameof(CanUpdateLocationStatusAsync)} Unexpected exception:");
                Console.WriteLine(ex);
                //TurnedOff dev debugger breaks! System.Diagnostics.Debugger.Break();
                throw;
            }
            
        }
    }
}
