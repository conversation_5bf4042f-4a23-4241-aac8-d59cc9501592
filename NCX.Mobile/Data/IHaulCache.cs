﻿using NCX.Mobile.Models;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace NCX.Mobile.Data
{
    internal interface IHaulCache
    {
        Task<IEnumerable<Haul>> GetAllHaulsAsync(CancellationToken ct = default);
        Task<IEnumerable<Haul>> GetActiveHaulsAsync(CancellationToken ct = default);
        Task<DateTime?> GetDateOfMostRecentCreatedHaulAsync();
        Task AddOrUpdateHaulsAsync(IEnumerable<Haul> hauls, CancellationToken ct = default);
        Task ClearAsync();
        Task<Haul> GetHaulAsync(int haulId);
        Task DeleteHaulAsync(Haul haul, CancellationToken ct = default);
    }

    internal static class IHaulCacheExtensions
    {
        public static Task AddOrUpdateHaulsAsync(this IHaulCache haulCache, params Haul[] hauls) =>
            haulCache.AddOrUpdateHaulsAsync(hauls, CancellationToken.None);
    }
}
