﻿using Microsoft.Extensions.Logging;
using NCX.Logging.Interfaces;
using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using Newtonsoft.Json;
using SQLite;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ModelsCache = NCX.Mobile.Models.Cache;
using SQLiteAsyncConnection = SQLite.SQLiteAsyncConnection;

namespace NCX.Mobile.Data.Impl
{
    class HaulCache : IHaulCache
    {
        ILogService _logger;
        static readonly AsyncLock _Mutex;
        readonly SQLiteAsyncConnection _sqlConnection;

        static HaulCache()
        {
            _Mutex = SQLiteAsyncLock.Inst;
        }

        public HaulCache(Func<SQLiteAsyncConnection> sqlConnectionFactory, ILogService logger)
        {
            _logger = logger;
            _sqlConnection = sqlConnectionFactory();

            CreateDatabase();
        }

        public async Task<IEnumerable<Haul>> GetActiveHaulsAsync(CancellationToken ct = default)
        {
            var entities = await GetHaulEntitiesAsync(ct);
            _logger.LogDebug(Constants.Log_Tag, $"GetActiveHaulsAsync - Raw entities count: {entities.Count()}");
            
            var hauls = await CreateHaulsFromEntitiesAsync(entities, ct);
            _logger.LogDebug(Constants.Log_Tag, $"GetActiveHaulsAsync - Created hauls count: {hauls.Count()}");
            
            // Add status logging
            foreach (var haul in hauls)
            {
                _logger.LogDebug(Constants.Log_Tag, $"GetActiveHaulsAsync - Haul {haul.Id} has status: {haul.Status}");
            }
            
            var activeHauls = hauls.Where(haul => 
                // TODO haul.Status != HaulStatus.Setup &&
                haul.Status != HaulStatus.Completed &&
                haul.Status != HaulStatus.Canceled &&
                haul.Status != HaulStatus.Deleted);
                
            _logger.LogDebug(Constants.Log_Tag, $"GetActiveHaulsAsync - Active hauls count: {activeHauls.Count()}");
            return activeHauls;
        }

        public async Task<IEnumerable<Haul>> GetAllHaulsAsync(CancellationToken ct = default)
        {
            return (await CreateHaulsFromEntitiesAsync(await GetHaulEntitiesAsync(ct), ct));
        }

        public async Task AddOrUpdateHaulsAsync(IEnumerable<Haul> hauls, CancellationToken ct = default)
        {
            await AddOrUpdateHaulEntitiesAsync(await CreateEntitiesFromHaulsAsync(hauls, ct), ct);
        }

        public async Task DeleteHaulAsync(Haul haul, CancellationToken ct = default)
        {
            using (await _Mutex.LockAsync().ConfigureAwait(false))
            {
                await _sqlConnection.DeleteAsync(CreatEntityFromHaul(haul));
            }
        }

        public async Task ClearAsync()
        {
            using (await _Mutex.LockAsync().ConfigureAwait(false))
            {
                await _sqlConnection.DeleteAllAsync<ModelsCache.Haul>();
            }
        }

        public async Task<DateTime?> GetDateOfMostRecentCreatedHaulAsync()
        {
            using (await _Mutex.LockAsync().ConfigureAwait(false))
            {
                ModelsCache.Haul haul = await _sqlConnection.Table<ModelsCache.Haul>().OrderByDescending(h => h.CreatedAt).FirstOrDefaultAsync();

                return haul != null ? (DateTime?)haul.CreatedAt : null;
            }
        }

        public async Task<Haul> GetHaulAsync(int haulId)
        {
            ModelsCache.Haul cachedHaul = null;

            using (await _Mutex.LockAsync().ConfigureAwait(false))
            {
                cachedHaul = await _sqlConnection.Table<ModelsCache.Haul>().Where(h => h.Id == haulId).FirstAsync();
            }

            return CreateHaulFromEntity(cachedHaul);
        }

        async Task<IEnumerable<ModelsCache.Haul>> GetHaulEntitiesAsync(CancellationToken ct = default)
        {
            using (await _Mutex.LockAsync().ConfigureAwait(false))
            {
                return (await _sqlConnection.Table<ModelsCache.Haul>().ToListAsync());
            }
        }

        async Task AddOrUpdateHaulEntitiesAsync(IEnumerable<ModelsCache.Haul> hauls, CancellationToken ct = default)
        {
            if (hauls.Count() == 0)
            {
                return;
            }

            using (await _Mutex.LockAsync().ConfigureAwait(false))
            {
                foreach(object obj in hauls)
                {
                    await _sqlConnection.InsertOrReplaceAsync(obj);
                }
            }
        }

        static Task<IEnumerable<ModelsCache.Haul>> CreateEntitiesFromHaulsAsync(IEnumerable<Haul> hauls, CancellationToken ct = default)
        {
            if (hauls.Count() == 0)
            {
                return Task.FromResult(Enumerable.Empty<ModelsCache.Haul>());
            }

            return Task.Run(() => hauls.Select(haul => CreatEntityFromHaul(haul)), ct);
        }

        static Task<IEnumerable<Haul>> CreateHaulsFromEntitiesAsync(IEnumerable<ModelsCache.Haul> hauls, CancellationToken ct = default)
        {
            if (hauls.Count() == 0)
            {
                return Task.FromResult(Enumerable.Empty<Haul>());
            }

            return Task.Run(() => hauls.Select(cachedHaul => CreateHaulFromEntity(cachedHaul)), ct);
        }

        static Haul CreateHaulFromEntity(ModelsCache.Haul cachedHaul)
        {
            return JsonConvert.DeserializeObject<Haul>(cachedHaul.Data);
        }

        static ModelsCache.Haul CreatEntityFromHaul(Haul haul)
        {
            var model = new ModelsCache.Haul(haul.Id, haul.CreatedAt, JsonConvert.SerializeObject(haul));

            return model;
        }

        async void CreateDatabase()
        {
            using (await _Mutex.LockAsync().ConfigureAwait(false))
            {
                await _sqlConnection.CreateTableAsync<ModelsCache.Haul>();
            }
        }
    }
}
