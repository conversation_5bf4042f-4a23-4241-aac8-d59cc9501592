﻿using NCX.Mobile.Models.Cache;
using NCX.Mobile.Models.Cache.PushOperations;
using System;
using SQLiteAsyncConnection = SQLite.SQLiteAsyncConnection;

namespace NCX.Mobile.Data.Impl
{
    class PushOperationCacheFactory : IPushOperationCacheFactory
    {
        readonly Func<SQLiteAsyncConnection> _sqlConnectionFactory;

        public PushOperationCacheFactory(Func<SQLiteAsyncConnection> sqlConnectionFactory)
        {
            _sqlConnectionFactory = sqlConnectionFactory;
        }

        public IPushOperationCache Create(PushOperationType pushOperationType)
        {
            switch (pushOperationType)
            {
                case PushOperationType.UpdateLoadTracking:
                    return new PushOperationCache<UpdateLoadTrackingOperation>(_sqlConnectionFactory);
                case PushOperationType.UpdateDriverLocation:
                    return new PushOperationCache<UpdateDriverLocationOperation>(_sqlConnectionFactory);
                case PushOperationType.UpdateLoadOperationStatus:
                    return new PushOperationCache<UpdateLoadOperationStatusOperation>(_sqlConnectionFactory);
                case PushOperationType.SendPOD:
                    return new PushOperationCache<SendPODOperation>(_sqlConnectionFactory);
                case PushOperationType.UpdateLoadStatus:
                    return new PushOperationCache<UpdateLoadStatusOperation>(_sqlConnectionFactory);
                case PushOperationType.CreateLoadDelay:
                    return new PushOperationCache<CreateLoadDelayOperation>(_sqlConnectionFactory);
                default:
                    throw new Exception($"Unrecognized pushOperationType {pushOperationType}");
            }
        }
    }
}
