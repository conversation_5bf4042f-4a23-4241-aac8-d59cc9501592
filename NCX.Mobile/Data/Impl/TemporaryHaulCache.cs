﻿using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ModelsCache = NCX.Mobile.Models.Cache;
using SQLiteAsyncConnection = SQLite.SQLiteAsyncConnection;

namespace NCX.Mobile.Data.Impl
{
    class TemporaryHaulCache : ITemporaryHaulCache
    {
        static readonly AsyncLock _Mutex;
        readonly SQLiteAsyncConnection _sqlConnection;

        static TemporaryHaulCache()
        {
            _Mutex = SQLiteAsyncLock.Inst;
        }

        public TemporaryHaulCache(Func<SQLiteAsyncConnection> sqlConnectionFactory)
        {
            _sqlConnection = sqlConnectionFactory();

            CreateDatabase();
        }

        public async Task AddHaulsAsync(IEnumerable<Haul> hauls, CancellationToken ct = default(CancellationToken))
        {
            if (hauls.Count() == 0)
            {
                return;
            }

            using (await _Mutex.LockAsync().ConfigureAwait(false))
            {
                foreach (object obj in await CreateEntitiesFromHaulsAsync(hauls))
                {
                    await _sqlConnection.InsertOrReplaceAsync(obj);
                }
            }
        }

        public async Task<IEnumerable<Haul>> GetHaulsAsync(CancellationToken ct = default(CancellationToken))
        {
            return (await CreateHaulsFromEntitiesAsync(await GetHaulEntitiesAsync(ct), ct));
        }

        public async Task ClearAsync()
        {
            using (await _Mutex.LockAsync().ConfigureAwait(false))
            {
                await _sqlConnection.DeleteAllAsync<ModelsCache.TemporaryHaul>();
            }
        }

        async Task<IEnumerable<ModelsCache.TemporaryHaul>> GetHaulEntitiesAsync(CancellationToken ct = default(CancellationToken))
        {
            using (await _Mutex.LockAsync().ConfigureAwait(false))
            {
                return (await _sqlConnection.Table<ModelsCache.TemporaryHaul>().ToListAsync());
            }
        }

        static Task<IEnumerable<ModelsCache.TemporaryHaul>> CreateEntitiesFromHaulsAsync(IEnumerable<Haul> hauls, CancellationToken ct = default(CancellationToken))
        {
            if (hauls.Count() == 0)
            {
                return Task.FromResult(Enumerable.Empty<ModelsCache.TemporaryHaul>());
            }

            return Task.Run(() => hauls.Select(haul => CreatEntityFromHaul(haul)).ToList().AsEnumerable(), ct);
        }

        static Task<IEnumerable<Haul>> CreateHaulsFromEntitiesAsync(IEnumerable<ModelsCache.TemporaryHaul> hauls, CancellationToken ct = default(CancellationToken))
        {
            if (hauls.Count() == 0)
            {
                return Task.FromResult(Enumerable.Empty<Haul>());
            }

            return Task.Run(() => hauls.Select(cachedHaul => CreateHaulFromEntity(cachedHaul)).ToList().AsEnumerable(), ct);
        }

        static Haul CreateHaulFromEntity(ModelsCache.TemporaryHaul cachedHaul)
        {
            return JsonConvert.DeserializeObject<Haul>(cachedHaul.Data);
        }

        static ModelsCache.TemporaryHaul CreatEntityFromHaul(Haul haul)
        {
            return new ModelsCache.TemporaryHaul(haul.Id, haul.CreatedAt, JsonConvert.SerializeObject(haul));
        }

        async void CreateDatabase()
        {
            using (await _Mutex.LockAsync().ConfigureAwait(false))
            {
                await _sqlConnection.CreateTableAsync<ModelsCache.TemporaryHaul>();
            }
        }
    }
}
