﻿using NCX.Mobile.Helpers;
using NCX.Mobile.Models.Cache.PushOperations;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SQLiteAsyncConnection = SQLite.SQLiteAsyncConnection;

namespace NCX.Mobile.Data.Impl
{
    class PushOperationCache<T> : IPushOperationCache where T : PushOperation, new()
    {
        static readonly AsyncLock _Mutex;
        readonly SQLiteAsyncConnection _sqlConnection;

        static PushOperationCache()
        {
            _Mutex = SQLiteAsyncLock.Inst;
        }

        public PushOperationCache(Func<SQLiteAsyncConnection> sqlConnectionFactory)
        {
            _sqlConnection = sqlConnectionFactory();

            CreateDatabase();
        }

        public async Task AddAsync(IPushOperation operation)
        {
            using (await _Mutex.LockAsync().ConfigureAwait(false))
            {
                await _sqlConnection.InsertAsync(operation);
            }
        }

        public async Task<IEnumerable<IPushOperation>> GetAllAsync()
        {
            using (await _Mutex.LockAsync().ConfigureAwait(false))
            {
                return await _sqlConnection.Table<T>().OrderBy(pushOperation => pushOperation.CreatedOn).ToListAsync();
            }
        }

        public async Task DeleteAsync(IPushOperation operation)
        {
            using (await _Mutex.LockAsync().ConfigureAwait(false))
            {
                await _sqlConnection.DeleteAsync(operation);
            }
        }

        public async Task ClearAsync()
        {
            using (await _Mutex.LockAsync().ConfigureAwait(false))
            {
                await _sqlConnection.DeleteAllAsync<T>();
            }
        }

        async void CreateDatabase()
        {
            using (await _Mutex.LockAsync().ConfigureAwait(false))
            {
                await _sqlConnection.CreateTableAsync<T>();
            }
        }
    }
}
