﻿using NCX.Mobile.Models;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace NCX.Mobile.Data
{
    interface ITemporaryHaulCache
    {
        Task AddHaulsAsync(IEnumerable<Haul> hauls, CancellationToken ct = default(CancellationToken));

        Task<IEnumerable<Haul>> GetHaulsAsync(CancellationToken ct = default(CancellationToken));

        Task ClearAsync();
    }
}
