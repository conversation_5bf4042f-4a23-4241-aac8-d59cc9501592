﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:resources="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             xmlns:prism="clr-namespace:Prism.Behaviors;assembly=Prism.Maui"
             x:Class="NCX.Mobile.Views.EnterSecretCodeView">
    <StackLayout Spacing="20"
                 HorizontalOptions="CenterAndExpand"
                 VerticalOptions="CenterAndExpand"
                 BackgroundColor="White"
                 Padding="40">
        <Entry Text="{Binding SecretCodeText, Mode=TwoWay}"
               Placeholder="{x:Static resources:Resources.SecretCodeText}"
               IsPassword="True"
               Keyboard="Numeric"
               HorizontalOptions="Fill">
            <Entry.Behaviors>
                <prism:EventToCommandBehavior EventName="Completed"
                                              Command="{Binding EnterSecretCodeCommand}" />
            </Entry.Behaviors>
        </Entry>
        <Button Text="{x:Static resources:Resources.GoText}"
                HeightRequest="50"
                WidthRequest="200"
                HorizontalOptions="Center"
                Style="{StaticResource BtnFormStyle}"
                Command="{Binding EnterSecretCodeCommand}" />
    </StackLayout>
</ContentView>
