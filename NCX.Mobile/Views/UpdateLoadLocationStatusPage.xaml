﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             xmlns:abstractions="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             xmlns:controls="clr-namespace:NCX.Mobile.Controls;assembly=NCX.Mobile.Forms"
             xmlns:models="clr-namespace:NCX.Mobile.Models;assembly=NCX.Mobile.Abstractions"
             prism:ViewModelLocator.AutowireViewModel="Automatic"
             x:Class="NCX.Mobile.Views.UpdateLoadLocationStatusPage"
             x:Name="page"
             Style="{StaticResource PageStyle}"
             Title="{x:Static abstractions:Resources.UpdateStatusText}"
             BackgroundColor="#F7F7F7">

    <ContentPage.Resources>
        <ResourceDictionary>
            <Style x:Key="BtnUpdateStatusStyle"
                   TargetType="Button"
                   BasedOn="{StaticResource BtnFormStyle}">
                <Setter Property="BackgroundColor"
                        Value="{StaticResource BtnDisabledColor}" />
                <Setter Property="VerticalOptions"
                        Value="Start" />
                <Setter Property="Margin"
                        Value="14,0" />
            </Style>
        </ResourceDictionary>
    </ContentPage.Resources>

    <StackLayout Padding="14"
                 Spacing="14">
        <!-- Header-->
        <controls:LoadLocationItem LoadOperation="{Binding LoadLocationItem.LoadOperation}"
                               ItemPosition="{Binding LoadLocationItem.ItemPosition}"
                               ItemTotalCount="{Binding LoadLocationItem.ItemTotalCount}"
                               CallLocationContactCommand="{Binding CallLocationContactCommand}"
                               StartMapDirectionsTaskCommand="{Binding StartMapDirectionsTaskCommand}"
                               VerticalOptions="Start" />

        <BoxView Style="{StaticResource SeparatorStyle}"
                 Margin="-14,0" />

        <!-- Last reported delay label -->
        <Label Text="{Binding Load.LastReportedDelay, Converter={StaticResource LastReportedDelayTextConverter}}"
               IsVisible="{Binding Load.LastReportedDelay, Converter={StaticResource ObjectToBoolConverter}}"
               VerticalOptions="Start"
               FontSize="Medium"
               Margin="0,0,14,0" />

        <!-- Update to En Route button -->
        <Button Text="{x:Static abstractions:Resources.EnRouteText}"
                Style="{StaticResource BtnUpdateStatusStyle}"
                Command="{Binding UpdateStatusCommand}"
                CommandParameter="{x:Static models:LoadOperationStatus.En_Route}">
            <Button.Triggers>
                <DataTrigger TargetType="Button"
                             Binding="{Binding NextLoadOperationStatus}"
                             Value="{x:Static models:LoadOperationStatus.En_Route}">
                    <Setter Property="BackgroundColor"
                            Value="{StaticResource BtnFormColor}" />
                </DataTrigger>
            </Button.Triggers>
        </Button>

        <!-- Update to Checked In button -->
        <Button Text="{x:Static abstractions:Resources.CheckedInText}"
                Style="{StaticResource BtnUpdateStatusStyle}"
                Command="{Binding UpdateStatusCommand}"
                CommandParameter="{x:Static models:LoadOperationStatus.Checked_In}">
            <Button.Triggers>
                <DataTrigger TargetType="Button"
                             Binding="{Binding NextLoadOperationStatus}"
                             Value="{x:Static models:LoadOperationStatus.Checked_In}">
                    <Setter Property="BackgroundColor"
                            Value="{StaticResource BtnFormColor}" />
                </DataTrigger>
            </Button.Triggers>
        </Button>

        <!-- Update to Completed button -->
        <Button Text="{x:Static abstractions:Resources.CompletedText}"
                Style="{StaticResource BtnUpdateStatusStyle}"
                Command="{Binding UpdateStatusCommand}"
                CommandParameter="{x:Static models:LoadOperationStatus.Completed}">
            <Button.Triggers>
                <DataTrigger TargetType="Button"
                             Binding="{Binding NextLoadOperationStatus}"
                             Value="{x:Static models:LoadOperationStatus.Completed}">
                    <Setter Property="BackgroundColor"
                            Value="{StaticResource BtnFormColor}" />
                </DataTrigger>
            </Button.Triggers>
        </Button>

        <!-- Report a delay button -->
        <Button Text="{x:Static abstractions:Resources.ReportDelayButton}"
                Style="{StaticResource BtnReportDelayStyle}"
                Command="{Binding ReportDelayCommand}"
                IsVisible="{Binding CanReportDelay}"
                VerticalOptions="EndAndExpand" />
    </StackLayout>
</ContentPage>
