﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             xmlns:abstractions="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             prism:ViewModelLocator.AutowireViewModel="Automatic"
             x:Class="NCX.Mobile.Views.ChangeUserPasswordPage"
             Title="{x:Static abstractions:Resources.UpdatePasswordPageTitle}"
             Style="{StaticResource PageStyle}"
             BackgroundColor="#323538">
    <Grid>
        <StackLayout HorizontalOptions="Center" 
                     VerticalOptions="Center" 
                     Padding="14,0">
            <BoxView HeightRequest="6" 
                     BackgroundColor="#D0021B"/>
            <StackLayout BackgroundColor="White"
                         WidthRequest="332"
                         Padding="30">
                <Label Text="{x:Static abstractions:Resources.YourCurrentPassword}" 
                       FontAttributes="Bold"/>
                <Entry Text="{Binding CurrentPassword}" 
                       IsPassword="True" 
                       x:Name="_currentPasswordEntry"/>

                <Label Text="{x:Static abstractions:Resources.YourNewPassword}" 
                       Margin="0,15,0,0" 
                       FontAttributes="Bold"/>
                <Entry Text="{Binding NewPassword}" 
                       IsPassword="True" 
                       x:Name="_newPasswordEntry"/>

                <Label Text="{x:Static abstractions:Resources.ConfirmYourNewPassword}" 
                       Margin="0,15,0,0" 
                       FontAttributes="Bold"/>
                <Entry Text="{Binding NewPasswordConfirmation}" 
                       IsPassword="True" 
                       x:Name="_newPasswordConfirmationEntry"/>

                <Button x:Name="_btnShowPasswords"
                        Style="{StaticResource BtnTransparentStyle}"
                        Text="{x:Static abstractions:Resources.ShowPasswordsButtonText}"
                        HorizontalOptions="End"
                        Clicked="_btnShowPasswords_Clicked"/>

                <!-- Use a 4-value margin instead of "0,16" -->
                <Button Text="{x:Static abstractions:Resources.ChangePasswordBtnText}" 
                        Command="{Binding UpdatePasswordCommand}" 
                        Style="{StaticResource BtnFormStyle}" 
                        Margin="0,16,0,0"/>
            </StackLayout>
        </StackLayout>
    </Grid>
</ContentPage>
