﻿using Microsoft.Maui.Controls;
using NCX.Mobile.ViewModels;
using NCX.Mobile.Converters;
using System.ComponentModel;
using System.Globalization;
using NCX.Mobile.Models;

namespace NCX.Mobile.Views
{
    public partial class UploadPODPage : ContentPage
    {
        BrowsableItemCollectionViewModel<PODItem> _photoViewerViewModel;

        public UploadPODPage()
        {
            InitializeComponent();
            UpdateLabelPhotoNavStatusText();
        }

        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();

            if (BindingContext != null)
            {
                SubscribeToPhotoViewerViewModelChanges(true);
            }
            else
            {
                SubscribeToPhotoViewerViewModelChanges(false);
            }
        }

        void SubscribeToPhotoViewerViewModelChanges(bool subscribe)
        {
            if (subscribe)
            {
                if (_photoViewerViewModel != null)
                {
                    SubscribeToPhotoViewerViewModelChanges(false);
                }

                _photoViewerViewModel = (BindingContext as UploadPODPageViewModel).PhotoViewerViewModel;
                _photoViewerViewModel.PropertyChanged += PhotoViewerViewModel_PropertyChanged;
            }
            else
            {
                if (_photoViewerViewModel != null)
                {
                    _photoViewerViewModel.PropertyChanged -= PhotoViewerViewModel_PropertyChanged;
                    _photoViewerViewModel = null;
                }
            }
        }

        void PhotoViewerViewModel_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            UpdateLabelPhotoNavStatusText();
        }

        void UpdateLabelPhotoNavStatusText()
        {
            if (LabelPhotoNavStatus == null)
                return;

            var converter = (PhotoViewerStatusTextConverter)Application.Current.Resources[nameof(PhotoViewerStatusTextConverter)];
            LabelPhotoNavStatus.Text = converter.Convert(_photoViewerViewModel, null, null, CultureInfo.CurrentUICulture) as string;
        }
    }
}
