﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             xmlns:abstractions="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             xmlns:controls="clr-namespace:NCX.Mobile.Controls;assembly=NCX.Mobile.Forms"
             prism:ViewModelLocator.AutowireViewModel="Automatic"
             x:Class="NCX.Mobile.Views.NewLoadPopupPage"
             Style="{StaticResource PageStyle}"
             Title="{x:Static abstractions:Resources.NewLoadPageTitle}"
             BackgroundColor="#F7F7F7">

    <Frame BackgroundColor="White"
           Margin="14"
           HasShadow="True"
           HorizontalOptions="Center"
           VerticalOptions="Center">
        <StackLayout>
            <Label Text="{x:Static abstractions:Resources.NewLoadTitleText}"
                   HorizontalOptions="Center"
                   FontAttributes="Bold"
                   VerticalOptions="Start"
                   FontSize="Large"
                   Margin="0,15,0,0" />

            <!-- Load properties-->
            <controls:LoadItem VerticalOptions="StartAndExpand"
                          BindingContext="{Binding LoadItem}"
                          Margin="15" />

            <Button Text="{x:Static abstractions:Resources.CloseText}"
                    Command="{Binding UpdateStatusToViewedCommand}"
                    VerticalOptions="End"
                    HorizontalOptions="End"
                    Margin="0,0,14,14" />
        </StackLayout>
    </Frame>
</ContentPage>
