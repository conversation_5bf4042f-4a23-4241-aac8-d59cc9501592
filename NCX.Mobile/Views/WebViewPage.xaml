﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             prism:ViewModelLocator.AutowireViewModel="Automatic"
             x:Class="NCX.Mobile.Views.WebViewPage"
             Title="{Binding Title}"
             BackgroundColor="White">

    <Grid>
        <WebView Source="{Binding WebPageUri}"
                 x:Name="_webView" />

        <ActivityIndicator x:Name="_busyIndicator"
                           VerticalOptions="Center"
                           HorizontalOptions="Center"
                           IsRunning="True"
                           IsVisible="{Binding IsBusy}" />
    </Grid>
</ContentPage>
