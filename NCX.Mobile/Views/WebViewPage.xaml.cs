﻿using Microsoft.Maui.Controls; // Updated namespace for MAUI

namespace NCX.Mobile.Views
{
    public partial class WebViewPage : ContentPage
    {
        public WebViewPage()
        {
            InitializeComponent();

            _webView.Navigating += OnWebViewNavigating;
            _webView.Navigated += OnWebViewNavigated;
        }

        private void OnWebViewNavigating(object sender, WebNavigatingEventArgs e)
        {
            _busyIndicator.IsRunning = true;
            _busyIndicator.IsVisible = true;
        }

        private void OnWebViewNavigated(object sender, WebNavigatedEventArgs e)
        {
            _busyIndicator.IsRunning = false;
            _busyIndicator.IsVisible = false;
        }
    }
}
