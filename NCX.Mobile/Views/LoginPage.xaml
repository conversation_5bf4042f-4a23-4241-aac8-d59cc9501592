<?xml version="1.0" encoding="utf-8"?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             xmlns:abstractions="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             xmlns:common="clr-namespace:NCX.Mobile.Controls.Common;assembly=NCX.Mobile.Forms"
             prism:ViewModelLocator.AutowireViewModel="Automatic"
             x:Class="NCX.Mobile.Views.LoginPage"
             Style="{StaticResource PageStyle}"
             BackgroundColor="#323538"
             NavigationPage.HasNavigationBar="False">
    <Grid x:Name="layoutRoot">
        <!-- Define Grid to fill the page -->
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <ScrollView Grid.Row="0" Grid.Column="0"
                    VerticalOptions="FillAndExpand"
                    HorizontalOptions="FillAndExpand">
            <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="CenterAndExpand" Padding="14,0">
                <Image Source="logo_bg"
                       HeightRequest="60"
                       HorizontalOptions="Center"
                       Margin="0,28,0,0">
                    <Image.GestureRecognizers>
                        <TapGestureRecognizer x:Name="logoTapGestureRecognizer" />
                    </Image.GestureRecognizers>
                </Image>

                <Label Text="{Binding Backend, Converter={StaticResource BackendDescriptionConverter}}"
                       TextColor="Red"
                       FontSize="16"
                       FontAttributes="Bold"
                       HorizontalOptions="Center"
                       Margin="0,0,0,20" />
                <BoxView HeightRequest="6"
                         BackgroundColor="#D0021B"
                         Margin="0,0,0,20" />
                <StackLayout BackgroundColor="White"
                             WidthRequest="332"
                             Padding="30,30,30,10">
                    <Label Text="{x:Static abstractions:Resources.UsernameText}"
                           FontAttributes="Bold" />
                    <Entry Text="{Binding Username}"
                           Keyboard="Email"
                           PlaceholderColor="{StaticResource LightTextColor}" />

                    <Label Text="{x:Static abstractions:Resources.PasswordText}"
                           FontAttributes="Bold"
                           Margin="0,15,0,0" />
                    <Entry x:Name="entryPassword"
                           Text="{Binding Password}"
                           IsPassword="True"
                           Placeholder="{x:Static abstractions:Resources.PasswordText}"
                           PlaceholderColor="{StaticResource LightTextColor}" />

                    <StackLayout Orientation="Horizontal"
                                 Margin="0,0,0,10">
                        <common:CheckBox x:Name="btnShowPassword"
                                     Style="{StaticResource BtnTransparentStyle}"
                                     Text="{x:Static abstractions:Resources.ShowPasswordText}"
                                     TextColor="{StaticResource LightTextColor}"
                                     HorizontalOptions="StartAndExpand"
                                     Checked="btnShowPassword_CheckedChanged" />

                        <Button Text="{x:Static abstractions:Resources.ForgotPasswordText}"
                                HorizontalOptions="End"
                                Command="{Binding ShowForgotPasswordCommand}"
                                Style="{StaticResource ButtonHyperlinkStyle}" />
                    </StackLayout>

                    <Button Text="{x:Static abstractions:Resources.LoginText}"
                            Command="{Binding LoginCommand}"
                            Style="{StaticResource BtnFormStyle}" />

                    <Label Text="{x:Static abstractions:Resources.SignInAcceptTermsLabel}"
                           TextColor="{StaticResource LightTextColor}" />

                    <Button Text="{x:Static abstractions:Resources.TutorialBtnText}"
                            Command="{Binding ShowTutorialCommand}"
                            Style="{StaticResource BtnSecondaryFormStyle}"
                            Margin="0,15,0,0" />
                </StackLayout>

                <!-- Change language links -->
                <StackLayout BindableLayout.ItemsSource="{Binding Languages}"
                             Orientation="Horizontal"
                             HorizontalOptions="Center"
                             Margin="0,10,0,0"
                             Spacing="25">
                    <BindableLayout.ItemTemplate>
                        <DataTemplate>
                            <Button Text="{Binding DisplayName}"
                                    TextColor="{StaticResource FormBkgndLabelColor}"
                                    Command="{Binding BindingContext.ChangeLanguageCommand, Source={x:Reference layoutRoot}}"
                                    CommandParameter="{Binding .}"
                                    Style="{StaticResource ButtonHyperlinkStyle}" />
                        </DataTemplate>
                    </BindableLayout.ItemTemplate>
                </StackLayout>

                <!-- Terms of service and Privacy policy links -->
                <StackLayout Orientation="Horizontal"
                             HorizontalOptions="Center"
                             Margin="-14,0">
                    <Button Text="{x:Static abstractions:Resources.TermsOfServiceLabel}"
                            TextColor="{StaticResource FormBkgndLabelColor}"
                            Command="{Binding ShowTermsOfServiceCommand}"
                            Style="{StaticResource ButtonHyperlinkStyle}"
                            HorizontalOptions="StartAndExpand" />
                    <Button Text="{x:Static abstractions:Resources.PrivacyPolicyLabel}"
                            TextColor="{StaticResource FormBkgndLabelColor}"
                            Command="{Binding ShowPrivacyPolicyCommand}"
                            Style="{StaticResource ButtonHyperlinkStyle}"
                            HorizontalOptions="EndAndExpand" />
                </StackLayout>

                <!-- Copyright text -->
                <Label Text="{x:Static abstractions:Resources.CopyrightText}"
                       HorizontalTextAlignment="Center"
                       Margin="14"
                       TextColor="{StaticResource FormBkgndLabelColor}"
                       FontSize="Micro" />
            </StackLayout>
        </ScrollView>
    </Grid>
</ContentPage>
