﻿using Microsoft.AppCenter.Crashes;
using NCX.Mobile.ViewModels;
using Microsoft.Maui.Controls;
using NCX.Mobile.Controls.Common;

namespace NCX.Mobile.Views
{
    public partial class LoginPage : ContentPage
    {
        int _logoTapCount;

        public LoginPage()
        {
            InitializeComponent();

            var tapGesture = new TapGestureRecognizer();
            tapGesture.Tapped += (s, e) => {
                if (btnShowPassword is ToggleButton toggle)
                {
                    toggle.IsChecked = !toggle.IsChecked;
                    entryPassword.IsPassword = !toggle.IsChecked;
                }
            };
            btnShowPassword.GestureRecognizers.Add(tapGesture);

            logoTapGestureRecognizer.Tapped += (s, e) =>
            {
                ++_logoTapCount;

                if (_logoTapCount == 5)
                {
                    _logoTapCount = 0;

                    (BindingContext as LoginPageViewModel)?.ShowEnterSecretCodeCommand.Execute(null);
                }
            };
        }

        private void btnShowPassword_CheckedChanged(object sender, ToggledEventArgs e)
        {
            if (sender is ToggleButton toggleButton)
            {
                entryPassword.IsPassword = !toggleButton.IsChecked;
            }
        }
    }
}
