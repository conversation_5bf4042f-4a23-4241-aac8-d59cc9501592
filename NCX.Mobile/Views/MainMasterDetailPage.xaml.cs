﻿using NCX.Mobile.Helpers;
using NCX.Mobile.Models;
using NCX.Mobile.ViewModels;
using Microsoft.Maui.Controls;

namespace NCX.Mobile.Views
{
    public partial class MainMasterDetailPage : FlyoutPage
    {
        public MainMasterDetailPage()
        {
            InitializeComponent();

            if (this is IFlyoutPageController controller)
            {
                if (controller.CanChangeIsPresented)
                {
                    closeMenu.GestureRecognizers.Add(new TapGestureRecognizer()
                    {
                        Command = new Command(() => IsPresented = false)
                    });
                }
                else
                {
                    closeMenu.IsVisible = false;
                }
            }
        }

        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();

            if (BindingContext is MainMasterDetailPageViewModel vm)
            {
                menuItemsListView.ItemsSource = new FlatHierachyItemsDataSource<MainMenuItem>(vm.MenuItems);
            }
        }

        void MenuItemsListView_ItemTapped(object sender, ItemTappedEventArgs e)
        {
            IsPresented = false;
        }
    }
}
