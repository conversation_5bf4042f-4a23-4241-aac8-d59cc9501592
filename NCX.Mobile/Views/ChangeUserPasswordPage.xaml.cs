﻿using System;
using Microsoft.Maui.Controls; // Updated namespace for MAUI
using NCX.Mobile.Properties; // Correct namespace for resources

namespace NCX.Mobile.Views
{
    public partial class ChangeUserPasswordPage : ContentPage
    {
        public ChangeUserPasswordPage()
        {
            InitializeComponent();
        }

        private void _btnShowPasswords_Clicked(object sender, EventArgs e)
        {
            // Toggle the IsPassword property for each Entry to show/hide passwords
            _currentPasswordEntry.IsPassword = !_currentPasswordEntry.IsPassword;
            _newPasswordEntry.IsPassword = !_newPasswordEntry.IsPassword;
            _newPasswordConfirmationEntry.IsPassword = !_newPasswordConfirmationEntry.IsPassword;

            // Update the button text based on the current state
            _btnShowPasswords.Text = _currentPasswordEntry.IsPassword
                ? NCX.Mobile.Properties.Resources.ShowPasswordsButtonText
                : NCX.Mobile.Properties.Resources.HidePasswordsButtonText;
        }
    }
}
