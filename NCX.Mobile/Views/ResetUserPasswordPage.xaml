﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             xmlns:abstractions="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             xmlns:ncx="clr-namespace:NCX.Mobile.Controls;assembly=NCX.Mobile.Abstractions"
             prism:ViewModelLocator.AutowireViewModel="Automatic"
             x:Class="NCX.Mobile.Views.ResetUserPasswordPage"
             Style="{StaticResource PageStyle}"
             BackgroundColor="#323538">
    <ContentPage.Resources>
        <ResourceDictionary>
            <!-- Define any additional resources here if needed -->
        </ResourceDictionary>
    </ContentPage.Resources>

    <Grid>
        <StackLayout HorizontalOptions="Center"
                     VerticalOptions="Center"
                     Padding="14,0">
            <Image Source="logo_bg"
                   HeightRequest="80"
                   HorizontalOptions="Center"
                   Margin="0,0,0,20" />

            <Label Text="{Binding Backend, Converter={StaticResource BackendDescriptionConverter}}"
                   TextColor="Red"
                   FontSize="16"
                   FontAttributes="Bold"
                   HorizontalOptions="Center"
                   Margin="0,0,0,20" />

            <BoxView HeightRequest="6"
                     BackgroundColor="#D0021B" />
            <StackLayout BackgroundColor="White"
                         WidthRequest="332"
                         Padding="30">
                <Label Text="{x:Static abstractions:Resources.EmailText}"
                       FontAttributes="Bold" />
                <Entry Text="{Binding Email}"
                       Keyboard="Email" />

                <Button Text="{x:Static abstractions:Resources.ResetPasswordText}"
                        Command="{Binding ResetPasswordCommand}"
                        Style="{StaticResource BtnFormStyle}"
                        Margin="0,15,0,0" />

                <Button HorizontalOptions="Center"
                        Text="{x:Static abstractions:Resources.BackToLoginText}"
                        Style="{StaticResource ButtonHyperlinkStyle}"
                        Margin="0,30,0,0"
                        Command="{Binding NavigateToLoginCommand}" />
            </StackLayout>
        </StackLayout>

        <Label Text="{x:Static abstractions:Resources.CopyrightText}"
               HorizontalOptions="Center"
               VerticalOptions="End"
               Margin="0,0,0,14"
               TextColor="#999999"
               FontSize="Micro" />
    </Grid>
</ContentPage>
