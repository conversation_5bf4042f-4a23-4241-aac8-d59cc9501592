﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             xmlns:abstractions="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             xmlns:models="clr-namespace:NCX.Mobile.Models;assembly=NCX.Mobile.Abstractions"
             prism:ViewModelLocator.AutowireViewModel="Automatic"
             x:Class="NCX.Mobile.Views.ReportDelayPage"
             Title="{x:Static abstractions:Resources.ReportDelayHeader}"
             BackgroundColor="#F7F7F7">
    <ContentPage.Resources>
        <ResourceDictionary>
            <Style x:Key="BtnDelayTypeStyle"
                   TargetType="Button"
                   BasedOn="{StaticResource BtnFormStyle}">
                <Setter Property="BackgroundColor"
                        Value="#FF8E01" />
            </Style>
        </ResourceDictionary>
    </ContentPage.Resources>

    <StackLayout HorizontalOptions="Center"
                 VerticalOptions="Center"
                 Spacing="15">
        <Button Text="{x:Static abstractions:Resources.DelayReasonFlatTireText}"
                Style="{StaticResource BtnDelayTypeStyle}"
                Command="{Binding ReportDelayCommand}"
                CommandParameter="{x:Static models:LoadDelayReason.Flat_Tire}" />

        <Button Text="{x:Static abstractions:Resources.DelayReasonRoadClosureText}"
                Style="{StaticResource BtnDelayTypeStyle}"
                Command="{Binding ReportDelayCommand}"
                CommandParameter="{x:Static models:LoadDelayReason.Road_Closure}" />

        <Button Text="{x:Static abstractions:Resources.DelayReasonHeavyTrafficText}"
                Style="{StaticResource BtnDelayTypeStyle}"
                Command="{Binding ReportDelayCommand}"
                CommandParameter="{x:Static models:LoadDelayReason.Heavy_Traffic}" />

        <Button Text="{x:Static abstractions:Resources.DelayReasonBadWeatherText}"
                Style="{StaticResource BtnDelayTypeStyle}"
                Command="{Binding ReportDelayCommand}"
                CommandParameter="{x:Static models:LoadDelayReason.Bad_Weather}" />

        <Button Text="{x:Static abstractions:Resources.DelayReasonBreakdownText}"
                Style="{StaticResource BtnDelayTypeStyle}"
                Command="{Binding ReportDelayCommand}"
                CommandParameter="{x:Static models:LoadDelayReason.Minor_Breakdown}" />

        <Button Text="{x:Static abstractions:Resources.DelayReasonAccidentText}"
                Style="{StaticResource BtnDelayTypeStyle}"
                Command="{Binding ReportDelayCommand}"
                CommandParameter="{x:Static models:LoadDelayReason.Minor_Accident}" />
    </StackLayout>
</ContentPage>
