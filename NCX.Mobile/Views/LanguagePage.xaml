﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             xmlns:abstractions="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             xmlns:behaviours="clr-namespace:NCX.Mobile.Behaviors;assembly=NCX.Mobile.Forms"
             prism:ViewModelLocator.AutowireViewModel="Automatic"
             x:Class="NCX.Mobile.Views.LanguagePage"
             Style="{StaticResource PageStyle}"
             Title="{x:Static abstractions:Resources.SelectLanguageText}">
    <ListView ItemsSource="{Binding Languages}">
        <ListView.ItemTemplate>
            <DataTemplate>
                <TextCell Text="{Binding DisplayName}"
                          TextColor="Black" />
            </DataTemplate>
        </ListView.ItemTemplate>
        <ListView.Behaviors>
            <behaviours:ListViewSelectedItemBehavior Command="{Binding LanguageSelectionCommand}" />
        </ListView.Behaviors>
    </ListView>
</ContentPage>
