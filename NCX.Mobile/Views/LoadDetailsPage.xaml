﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             xmlns:abstractions="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             prism:ViewModelLocator.AutowireViewModel="Automatic"
             x:Class="NCX.Mobile.Views.LoadDetailsPage"
             x:Name="page"
             Padding="14"
             Style="{StaticResource PageStyle}"
             Title="{x:Static abstractions:Resources.LoadDetails}">
    <ContentPage.Resources>
        <ResourceDictionary>
            <!-- Cargo Item Template -->
            <DataTemplate x:Key="CargoItemTemplate">
                <Frame Padding="10" Margin="0,5" BackgroundColor="#F9F9F9" CornerRadius="5">
                    <Grid RowSpacing="8">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <!-- Header -->
                            <RowDefinition Height="1" />
                            <!-- Separator -->
                            <RowDefinition Height="Auto" />
                            <!-- Description -->
                            <RowDefinition Height="1" />
                            <!-- Separator -->
                            <RowDefinition Height="Auto" />
                            <!-- Additional Fields -->
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!-- Header -->
                        <StackLayout Orientation="Horizontal" Grid.ColumnSpan="2">
                            <Label Text="{Binding IsPickup, Converter={StaticResource CargoItemHeaderTextConverter}}"
                                   Style="{StaticResource HeaderStyle}" />
                            <Label Text="{Binding ., Converter={StaticResource CargoItemHeaderTextConverter}, ConverterParameter={x:Reference cargoItemsList}}"
                                   Margin="10,0,0,0"
                                   TextColor="{StaticResource RowHeaderColor}"
                                   VerticalOptions="Center" />
                        </StackLayout>
                        <BoxView Style="{StaticResource Separator2ColsStyle}" Grid.Row="1" Grid.ColumnSpan="2" />

                        <!-- Item Description -->
                        <Label Text="{x:Static abstractions:Resources.ItemDescriptionText}"
                               TextColor="{StaticResource RowHeaderColor}"
                               Grid.Row="2"
                               Margin="0,0,10,0"/>
                        <Label Text="{Binding Description}"
                               Grid.Row="2" Grid.Column="1"
                               HorizontalOptions="Start" />
                        <BoxView Style="{StaticResource Separator2ColsStyle}" Grid.Row="3" Grid.ColumnSpan="2" />
                    </Grid>
                </Frame>
            </DataTemplate>

            <!-- Location Item Template -->
            <DataTemplate x:Key="LocationItemTemplate">
                <Frame Padding="10" Margin="0,5" BackgroundColor="#F9F9F9" CornerRadius="5">
                    <Grid RowSpacing="8">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <!-- Header -->
                            <RowDefinition Height="1" />
                            <!-- Separator -->
                            <RowDefinition Height="Auto" />
                            <!-- Company -->
                            <RowDefinition Height="1" />
                            <!-- Separator -->
                            <RowDefinition Height="Auto" />
                            <!-- Additional Fields -->
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!-- Header -->
                        <StackLayout Orientation="Horizontal" Grid.ColumnSpan="2">
                            <Label Text="{Binding LoadOperation.SourceType, Converter={StaticResource LocationItemHeaderTextConverter}}"
                                   Style="{StaticResource HeaderStyle}" />
                            <Label Text="{Binding ., Converter={StaticResource LocationItemHeaderTextConverter}}"
                                   Margin="10,0,0,0"
                                   TextColor="{StaticResource RowHeaderColor}"
                                   VerticalOptions="Center" />
                        </StackLayout>
                        <BoxView Style="{StaticResource Separator2ColsStyle}" Grid.Row="1" Grid.ColumnSpan="2" />

                        <!-- Company -->
                        <Label Text="{x:Static abstractions:Resources.Company}"
                               TextColor="{StaticResource RowHeaderColor}"
                               Grid.Row="2"
                               Margin="0,0,10,0"/>
                        <Label Text="{Binding LoadOperation.Location.CompanyName}"
                               Grid.Row="2" Grid.Column="1"
                               HorizontalOptions="Start" />
                        <BoxView Style="{StaticResource Separator2ColsStyle}" Grid.Row="3" Grid.ColumnSpan="2" />
                    </Grid>
                </Frame>
            </DataTemplate>
        </ResourceDictionary>
    </ContentPage.Resources>

    <Grid>
        <!-- Loading Indicator -->
        <Label Text="Loading load details..."
               VerticalOptions="Center"
               HorizontalOptions="Center"
               IsVisible="{Binding IsBusy, Converter={StaticResource ObjectToBoolConverter}}" />

        <!-- Main Content -->
        <ScrollView VerticalOptions="FillAndExpand"
                    IsVisible="{Binding IsBusy, Converter={StaticResource ObjectToBoolInverseConverter}}">
            <Grid RowSpacing="10" x:Name="ItemsGrid" Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <!-- Update Button -->
                    <RowDefinition Height="Auto" />
                    <!-- ID -->
                    <RowDefinition Height="Auto" />
                    <!-- Notes -->
                    <RowDefinition Height="Auto" />
                    <!-- Separator -->
                    <RowDefinition Height="Auto" />
                    <!-- Equipment Type Header -->
                    <RowDefinition Height="Auto" />
                    <!-- Separator -->
                    <RowDefinition Height="Auto" />
                    <!-- Equipment Type -->
                    <RowDefinition Height="Auto" />
                    <!-- Separator -->
                    <RowDefinition Height="Auto" />
                    <!-- Cargo Items Header -->
                    <RowDefinition Height="Auto" />
                    <!-- Cargo Items -->
                    <RowDefinition Height="Auto" />
                    <!-- Location Items Header -->
                    <RowDefinition Height="Auto" />
                    <!-- Location Items -->
                    <RowDefinition Height="*" />
                    <!-- Spacer to push content up -->
                </Grid.RowDefinitions>

                <!-- Update Button -->
                <Button Text="{x:Static abstractions:Resources.UpdateStatusText}"
                        Command="{Binding UpdateLoadStatusCommand}"
                        CommandParameter="{Binding Haul}"
                        Style="{StaticResource BtnFormStyle}"
                        Grid.Row="0" Grid.ColumnSpan="2"
                        HorizontalOptions="Center" Margin="0,0,0,10" />

                <!-- ID and Notes -->
                <Label Text="{Binding Load.Eid}"
                       FontSize="Medium"
                       FontAttributes="Bold"
                       HorizontalOptions="Center"
                       Grid.Row="1" Grid.ColumnSpan="2" />
                <Label Text="{Binding Load.Notes}"
                       Grid.Row="2" Grid.ColumnSpan="2"
                       HorizontalOptions="Center"
                       TextColor="{StaticResource LightTextColor}"
                       IsVisible="{Binding Load.Notes, Converter={StaticResource ObjectToBoolConverter}}" />
                <BoxView Style="{StaticResource Separator2ColsStyle}"
                         Margin="-14,0"
                         Grid.Row="3" Grid.ColumnSpan="2" />

                <!-- Equipment Type Section -->
                <Label Text="{x:Static abstractions:Resources.EquipmentTypeText}"
                       FontSize="Medium"
                       FontAttributes="Bold"
                       Grid.Row="4" Grid.ColumnSpan="2"
                       HorizontalOptions="Start" />
                <BoxView Style="{StaticResource Separator2ColsStyle}" Grid.Row="5" Grid.ColumnSpan="2" />
                <Label Text="{x:Static abstractions:Resources.EquipmentTypeText}"
                       TextColor="{StaticResource RowHeaderColor}"
                       Grid.Row="6" />
                <Label Text="{Binding Haul.EquipmentType, Converter={StaticResource EquipmentTypeConverter}}"
                       Grid.Row="6" Grid.Column="1"
                       HorizontalOptions="Start" />
                <BoxView Style="{StaticResource Separator2ColsStyle}" Grid.Row="7" Grid.ColumnSpan="2" />

                <!-- Cargo Items Section -->
                <Label Text="Cargo Items"
                       FontSize="Medium"
                       FontAttributes="Bold"
                       Grid.Row="8" Grid.ColumnSpan="2"
                       HorizontalOptions="Start" Margin="0,10,0,0" />
                <StackLayout x:Name="cargoItemsList"
                             BindableLayout.ItemsSource="{Binding CargoItems}"
                             BindableLayout.ItemTemplate="{StaticResource CargoItemTemplate}"
                             Grid.Row="9" Grid.ColumnSpan="2" />

                <!-- Location Items Section -->
                <Label Text="Locations"
                       FontSize="Medium"
                       FontAttributes="Bold"
                       Grid.Row="10" Grid.ColumnSpan="2"
                       HorizontalOptions="Start" Margin="0,10,0,0" />
                <StackLayout x:Name="locationItemsList"
                             BindableLayout.ItemsSource="{Binding LocationItems}"
                             BindableLayout.ItemTemplate="{StaticResource LocationItemTemplate}"
                             Grid.Row="11" Grid.ColumnSpan="2" />
            </Grid>
        </ScrollView>
    </Grid>
</ContentPage>