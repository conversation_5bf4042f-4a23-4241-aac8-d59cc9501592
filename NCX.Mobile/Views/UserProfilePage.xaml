﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             xmlns:ncx="clr-namespace:NCX.Mobile.Controls;assembly=NCX.Mobile.Abstractions"
             xmlns:abstractions="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             xmlns:behaviours="clr-namespace:NCX.Mobile.Behaviors;assembly=NCX.Mobile.Forms"
             xmlns:helpers="clr-namespace:NCX.Mobile.Helpers;assembly=NCX.Mobile.Forms"
             xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
             xmlns:fftransformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Maui"
             prism:ViewModelLocator.AutowireViewModel="Automatic"
             x:Class="NCX.Mobile.Views.UserProfilePage"
             x:Name="page"
             Title="{x:Static abstractions:Resources.DriverProfileTitle}"
             Style="{StaticResource PageStyle}"
             BackgroundColor="#F7F7F7"
             Padding="14">

    <Grid>
        <StackLayout IsVisible="{Binding IsBusy, Converter={StaticResource ObjectToBoolInverseConverter}}">
            <StackLayout Orientation="Horizontal"
                         VerticalOptions="Start">
                <Grid Margin="14,0,28,0"
                      HorizontalOptions="Start"
                      VerticalOptions="Start">
                    <ffimageloading:CachedImage Aspect="AspectFill"
                                                WidthRequest="70"
                                                HeightRequest="70"
                                                IsVisible="{Binding UserAvatarUri, Converter={StaticResource ObjectToBoolConverter}}">
                        <ffimageloading:CachedImage.Source>
                            <UriImageSource Uri="{Binding UserAvatarUri}"
                                            CacheValidity="365"
                                            CachingEnabled="True" />
                        </ffimageloading:CachedImage.Source>
                        <ffimageloading:CachedImage.Transformations>
                            <fftransformations:RoundedTransformation Radius="35"/>
                        </ffimageloading:CachedImage.Transformations>
                    </ffimageloading:CachedImage>
                    <Label Style="{StaticResource IconStyle}"
                           Text="{x:Static helpers:FontIcons.User}"
                           FontSize="40"
                           TextColor="#212121"
                           VerticalOptions="Center"
                           IsVisible="{Binding UserAvatarUri, Converter={StaticResource ObjectToBoolInverseConverter}}" />
                </Grid>
                <StackLayout VerticalOptions="Center"
                             HorizontalOptions="StartAndExpand">
                    <!-- Driver's info -->
                    <Label Text="{Binding User.Name}"
                           FontSize="Medium"
                           TextColor="Black" />
                    <Label Text="{Binding User.Email}" />
                    <Label Text="{Binding User.PhoneNumber}" />

                    <Button Text="{x:Static abstractions:Resources.UserProfileUpdatePasswordButton}"
                            Style="{StaticResource ButtonHyperlinkStyle}"
                            Command="{Binding ShowChangePasswordCommand}" 
                            Margin="-10,0,0,0"/>
                </StackLayout>
            </StackLayout>

            <ListView ItemsSource="{Binding DriverInfoItemList}"
                      InputTransparent="True"
                      HasUnevenRows="True"
                      VerticalOptions="StartAndExpand"
                      SeparatorColor="{StaticResource SeparatorColor}"
                      IsGroupingEnabled="True"
                      BackgroundColor="Transparent">
                <ListView.GroupHeaderTemplate>
                    <DataTemplate>
                        <ViewCell>
                            <Label Text="{Binding Name}"
                                   Margin="0,14,0,7" />
                        </ViewCell>
                    </DataTemplate>
                </ListView.GroupHeaderTemplate>
                <ListView.ItemTemplate>
                    <DataTemplate>
                        <ViewCell>
                            <StackLayout>
                                <Grid Margin="0,7">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <Label Text="{Binding Name}"
                                           TextColor="{StaticResource RowHeaderColor}" />
                                    <Label Text="{Binding Value}"
                                           Grid.Column="1" />
                                </Grid>
                            </StackLayout>
                        </ViewCell>
                    </DataTemplate>
                </ListView.ItemTemplate>
                <ListView.Behaviors>
                    <behaviours:ListViewSelectedItemBehavior />
                </ListView.Behaviors>
            </ListView>
        </StackLayout>
    </Grid>
</ContentPage>
