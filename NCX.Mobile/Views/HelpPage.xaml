﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             xmlns:abstractions="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             xmlns:helpers="clr-namespace:NCX.Mobile.Helpers;assembly=NCX.Mobile.Forms"
             prism:ViewModelLocator.AutowireViewModel="Automatic"
             x:Class="NCX.Mobile.Views.HelpPage"
             Padding="14"
             Title="{x:Static abstractions:Resources.Help}">
    <StackLayout>
        <!-- Assuming MakeUppercase is a custom MarkupExtension in NCX.Mobile.Helpers namespace -->
        <Label Text="{helpers:MakeUppercase Text={x:Static abstractions:Resources.Help}}"
               Margin="0, 10, 0, 0" />
        <Button Text="{x:Static abstractions:Resources.TutorialBtnText}"
                Command="{Binding ShowTutorialCommand}" />
        <Button Text="{x:Static abstractions:Resources.HelpFAQ}"
                Command="{Binding ShowHelpFAQCommand}" />
        <Button Text="{x:Static abstractions:Resources.ContactUsByEmail}"
                Command="{Binding StartComposeSupportEmailCommand}" />
        <Button Text="{x:Static abstractions:Resources.ContactUsByPhone}"
                Command="{Binding StartSupportCallCommand}" />

        <Label Text="{x:Static abstractions:Resources.Legal}"
               Margin="0, 10, 0, 0" />
        <Button Text="{x:Static abstractions:Resources.TermsOfServiceLabel}"
                Command="{Binding ShowTermsOfServiceCommand}" />
        <Button Text="{x:Static abstractions:Resources.PrivacyPolicyLabel}"
                Command="{Binding ShowPrivacyPolicyCommand}" />
    </StackLayout>
</ContentPage>
