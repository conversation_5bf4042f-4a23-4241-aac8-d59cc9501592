﻿<?xml version="1.0" encoding="utf-8" ?>
<Frame xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
       xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
       xmlns:prism="http://prismlibrary.com"
       xmlns:abstractions="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
       xmlns:behaviours="clr-namespace:NCX.Mobile.Behaviors;assembly=NCX.Mobile.Forms"
       xmlns:controls="clr-namespace:NCX.Mobile.Controls;assembly=NCX.Mobile.Forms"
       x:Class="NCX.Mobile.Views.SelectLoadLocationToEnRoutePopupView"
       x:Name="this"
       BackgroundColor="White"
       Margin="14"
       Padding="14,10"
       HasShadow="True"
       HorizontalOptions="Center"
       VerticalOptions="Center">

    <StackLayout>
        <!-- Header -->
        <Label Text="{x:Static abstractions:Resources.EnRouteText}"
               TextColor="{StaticResource StatusEnRouteColor}"
               FontSize="Medium"
               VerticalOptions="Start" />
        <Label Text="{x:Static abstractions:Resources.SelectLocationEnRouteHeader}"
               VerticalOptions="Start" />

        <BoxView Style="{StaticResource SeparatorStyle}"
                 VerticalOptions="Start"
                 Margin="-14,10,-14,0" />

        <!-- Location list-->
        <ListView x:Name="locationList"
                  ItemsSource="{Binding Locations}"
                  Margin="0,14,0,0"
                  HasUnevenRows="True"
                  VerticalOptions="StartAndExpand"
                  CachingStrategy="RetainElement">
            <ListView.ItemTemplate>
                <DataTemplate>
                    <ViewCell>
                        <StackLayout Margin="0,14">
                            <controls:LoadLocationItem ItemPosition="{Binding LoadLocationItem.ItemPosition}"
                                                   ItemTotalCount="{Binding LoadLocationItem.ItemTotalCount}"
                                                   LoadOperation="{Binding LoadLocationItem.LoadOperation}"
                                                   CallLocationContactCommand="{Binding BindingContext.CallLocationContactCommand, Source={x:Reference this}}"
                                                   StartMapDirectionsTaskCommand="{Binding BindingContext.StartMapDirectionsTaskCommand, Source={x:Reference this}}"
                                                   VerticalOptions="Start" />

                            <Button Text="{x:Static abstractions:Resources.EnRouteText}"
                                    Style="{StaticResource BtnFormStyle}"
                                    Command="{Binding BindingContext.UpdateLocationStatusCommand, Source={x:Reference locationList}}"
                                    CommandParameter="{Binding LoadOperation}"
                                    Margin="0,14,0,0" />
                        </StackLayout>
                    </ViewCell>
                </DataTemplate>
            </ListView.ItemTemplate>
            <ListView.Behaviors>
                <behaviours:ListViewSelectedItemBehavior />
            </ListView.Behaviors>
        </ListView>
    </StackLayout>
</Frame>
