﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             xmlns:abstractions="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             xmlns:helpers="clr-namespace:NCX.Mobile.Helpers;assembly=NCX.Mobile.Abstractions"
             prism:ViewModelLocator.AutowireViewModel="Automatic"
             x:Class="NCX.Mobile.Views.SelectApiHostPage"
             Style="{StaticResource PageStyle}"
             BackgroundColor="#323538">

    <StackLayout BackgroundColor="White"
                 VerticalOptions="CenterAndExpand"
                 HorizontalOptions="CenterAndExpand"
                 Padding="15"
                 Spacing="10">
        <Label Text="Set API Host"
               FontSize="Large" />
        <Button Text="Set to Dev"
                Margin="0,10,0,0"
                Command="{Binding SetApiHostCommand}"
                CommandParameter="{x:Static helpers:Constants.HostDev}"
                HorizontalOptions="Start" />
        <Button Text="Set to QA3"
                Command="{Binding SetApiHostCommand}"
                CommandParameter="{x:Static helpers:Constants.HostQA3}"
                HorizontalOptions="Start" />
        <Button Text="Set to QA4"
                Command="{Binding SetApiHostCommand}"
                CommandParameter="{x:Static helpers:Constants.HostQA4}"
                HorizontalOptions="Start" />
        <Button Text="Set to Production"
                Command="{Binding SetApiHostCommand}"
                CommandParameter="{x:Static helpers:Constants.HostProd}"
                HorizontalOptions="Start" />
        <StackLayout Orientation="Horizontal"
                     Margin="0,5,0,0">
            <Entry Placeholder="Custom API endpoint"
                   WidthRequest="200"
                   x:Name="customApiHostEntry" />
            <Button Text="Set"
                    Command="{Binding SetApiHostCommand}"
                    CommandParameter="{Binding Text, Source={x:Reference customApiHostEntry}}"
                    HorizontalOptions="Start" />
        </StackLayout>
    </StackLayout>
</ContentPage>
