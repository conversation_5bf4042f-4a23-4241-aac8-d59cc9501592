﻿<?xml version="1.0" encoding="utf-8" ?>
<FlyoutPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
           xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
           xmlns:prism="http://prismlibrary.com"
           xmlns:behaviours="clr-namespace:NCX.Mobile.Behaviors;assembly=NCX.Mobile.Forms"
           xmlns:helpers="clr-namespace:NCX.Mobile.Helpers;assembly=NCX.Mobile.Forms"
           xmlns:controls="clr-namespace:NCX.Mobile.Controls;assembly=NCX.Mobile.Forms"
           xmlns:common="clr-namespace:NCX.Mobile.Controls.Common;assembly=NCX.Mobile.Forms"
           xmlns:ncx="clr-namespace:NCX.Mobile;assembly=NCX.Mobile"
           xmlns:pages="clr-namespace:NCX.Mobile.Views"
           xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
           xmlns:fftransformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Maui"
           x:Class="NCX.Mobile.Views.MainMasterDetailPage"
           Style="{StaticResource PageStyle}"
           BackgroundColor="#323538">
    <!-- Lowercase 'true' -->
    <FlyoutPage.Resources>
        <ResourceDictionary>
            <DataTemplate x:Key="MenuItemCellTemplate">
                <ViewCell>
                    <StackLayout Padding="22,14,7,14"
                                 HorizontalOptions="FillAndExpand"
                                 Orientation="Horizontal">
                        <Label HorizontalOptions="Start"
                               VerticalOptions="Center"
                               TextColor="#B1B1B1"
                               FontSize="Large"
                               Text="{Binding Icon}"
                               Style="{StaticResource IconStyle}" />
                        <Label HorizontalOptions="StartAndExpand"
                               VerticalOptions="Center"
                               Text="{Binding Title}"
                               TextColor="White"
                               FontSize="Medium"
                               Margin="14,0,0,0" />
                    </StackLayout>
                </ViewCell>
            </DataTemplate>
            <DataTemplate x:Key="DropdownMenuItemCellTemplate">
                <ViewCell>
                    <controls:DropdownListViewItemView ContentTemplate="{StaticResource MenuItemCellTemplate}" />
                </ViewCell>
            </DataTemplate>
            <DataTemplate x:Key="DropdownChildMenuItemTemplate">
                <ViewCell>
                    <common:ContentControl ContentTemplate="{StaticResource MenuItemCellTemplate}"
                                          Padding="35,0,0,0" />
                </ViewCell>
            </DataTemplate>
            <controls:MainMenuItemSelector x:Key="MainMenuItemSelector"
                                       MenuItemTemplate="{StaticResource MenuItemCellTemplate}"
                                       DropdownMenuItemTemplate="{StaticResource DropdownMenuItemCellTemplate}"
                                       DropdownChildMenuItemTemplate="{StaticResource DropdownChildMenuItemTemplate}" />
        </ResourceDictionary>
    </FlyoutPage.Resources>
    <FlyoutPage.Flyout>
        <ContentPage Title="NCX">
            <Grid BackgroundColor="#323538">
                <Image Source="logo_gray"
                       VerticalOptions="End"
                       HorizontalOptions="Center"
                       WidthRequest="163"
                       HeightRequest="64"
                       Margin="0,0,0,42" />
                <Label Text="{Binding AppVersion, StringFormat='v{0}'}"
                       FontSize="Small"
                       TextColor="#5A5A5A"
                       VerticalOptions="End"
                       HorizontalOptions="Start"
                       Margin="10,0,0,10" />
                <StackLayout>
                    <ContentView BackgroundColor="{StaticResource SecondaryColor}"
                                 VerticalOptions="Start"
                                 HeightRequest="80">
                        <StackLayout Orientation="Horizontal"
                                     VerticalOptions="Center"
                                     Margin="20,0,0,0">
                            <Grid Margin="0,0,10,0"
                                  HorizontalOptions="Start"
                                  VerticalOptions="Center"
                                  HeightRequest="50">
                                <ffimageloading:CachedImage Aspect="AspectFill"
                                                            WidthRequest="50"
                                                            HeightRequest="50"
                                                            IsVisible="{Binding UserAvatarUri, Converter={StaticResource ObjectToBoolConverter}}">
                                    <ffimageloading:CachedImage.Source>
                                        <UriImageSource Uri="{Binding UserAvatarUri}"
                                                        CacheValidity="365"
                                                        CachingEnabled="true" />
                                    </ffimageloading:CachedImage.Source>
                                    <ffimageloading:CachedImage.Transformations>
                                        <fftransformations:RoundedTransformation Radius="25"/>
                                    </ffimageloading:CachedImage.Transformations>
                                </ffimageloading:CachedImage>
                                <Label Style="{StaticResource IconStyle}"
                                       Text="{x:Static helpers:FontIcons.User}"
                                       FontSize="40"
                                       TextColor="White"
                                       VerticalOptions="Center"
                                       IsVisible="{Binding UserAvatarUri, Converter={StaticResource ObjectToBoolInverseConverter}}" />
                            </Grid>
                            <Label Text="{Binding User.FirstName, Converter={StaticResource MainMenuHeaderConverter}}"
                                   VerticalOptions="Center"
                                   HorizontalOptions="StartAndExpand"
                                   FontSize="18"
                                   TextColor="White" />
                            <Label x:Name="closeMenu"
                                   Style="{StaticResource IconStyle}"
                                   Text="{x:Static helpers:FontIcons.Times}"
                                   FontSize="28"
                                   TextColor="White"
                                   HorizontalOptions="End"
                                   WidthRequest="34"
                                   HeightRequest="34" />
                        </StackLayout>
                    </ContentView>
                    <ListView x:Name="menuItemsListView"
                              SeparatorVisibility="Default"
                              SeparatorColor="#4F5153"
                              HasUnevenRows="true"
                              BackgroundColor="Transparent"
                              VerticalOptions="StartAndExpand"
                              ItemTemplate="{StaticResource MainMenuItemSelector}"
                              CachingStrategy="RetainElement"
                              Margin="14,21"
                              ItemsSource="{Binding MenuItems}"
                              ItemTapped="MenuItemsListView_ItemTapped">
                        <ListView.Behaviors>
                            <behaviours:ListViewSelectedItemBehavior Command="{Binding ExecuteMainMenuItemCommand}" />
                        </ListView.Behaviors>
                    </ListView>
                </StackLayout>
            </Grid>
        </ContentPage>
    </FlyoutPage.Flyout>
</FlyoutPage>