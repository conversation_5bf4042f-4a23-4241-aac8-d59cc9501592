﻿using Prism.Navigation;
using Microsoft.Maui.Controls; // Updated namespace for MAUI

namespace NCX.Mobile.Views
{
    public partial class NewLoadPopupPage : ContentPage
    {
        public NewLoadPopupPage()
        {
            InitializeComponent();
        }

        protected override bool OnBackButtonPressed()
        {
            // Ensure BindingContext implements IConfirmNavigation
            if (BindingContext is IConfirmNavigation confirmNavigation)
            {
                return confirmNavigation.CanNavigate(null);
            }
            return base.OnBackButtonPressed();
        }
    }
}
