﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             xmlns:abstractions="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             xmlns:behaviours="clr-namespace:NCX.Mobile.Behaviors;assembly=NCX.Mobile.Forms"
             xmlns:controls="clr-namespace:NCX.Mobile.Controls;assembly=NCX.Mobile.Forms"
             xmlns:converters="clr-namespace:NCX.Mobile.Converters;assembly=NCX.Mobile.Forms"
             prism:ViewModelLocator.AutowireViewModel="Automatic"
             x:Class="NCX.Mobile.Views.SelectLoadLocationToUpdatePage"
             x:Name="page"
             Style="{StaticResource PageStyle}"
             Title="{x:Static abstractions:Resources.UpdateStatusText}"
             BackgroundColor="#323538">

    <ContentPage.Resources>
        <ResourceDictionary>
            <converters:FirstLoadLocationSourceTypeConverter x:Key="FirstLoadLocationSourceTypeConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>

    <StackLayout Padding="14">
        <Label Text="{Binding Locations, Converter={StaticResource FirstLoadLocationSourceTypeConverter}}"
               FontSize="Medium"
               VerticalOptions="Start" />
        <Label Text="{x:Static abstractions:Resources.SelectLocationHeader}"
               VerticalOptions="Start" />
        <BoxView Style="{StaticResource SeparatorStyle}"
                 Margin="-14,14,-14,0"
                 VerticalOptions="Start" />

        <ListView ItemsSource="{Binding Locations}"
                  HasUnevenRows="True"
                  VerticalOptions="FillAndExpand"
                  BackgroundColor="Transparent"
                  SeparatorColor="{StaticResource SeparatorColor}"
                  CachingStrategy="RetainElement">
            <ListView.ItemTemplate>
                <DataTemplate>
                    <ViewCell>
                        <StackLayout Margin="0,14">
                            <controls:LoadLocationItem ItemPosition="{Binding ItemPosition}"
                                                   ItemTotalCount="{Binding ItemTotalCount}"
                                                   LoadOperation="{Binding LoadOperation}"
                                                   CallLocationContactCommand="{Binding BindingContext.CallLocationContactCommand, Source={x:Reference page}}"
                                                   StartMapDirectionsTaskCommand="{Binding BindingContext.StartMapDirectionsTaskCommand, Source={x:Reference page}}"
                                                   VerticalOptions="Start" />

                            <Button Text="{x:Static abstractions:Resources.UpdateThisLocationButton}"
                                    Style="{StaticResource BtnFormStyle}"
                                    Command="{Binding BindingContext.UpdateLocationStatusCommand, Source={x:Reference page}}"
                                    CommandParameter="{Binding LoadOperation}"
                                    Margin="14,20,14,0" />
                        </StackLayout>
                    </ViewCell>
                </DataTemplate>
            </ListView.ItemTemplate>
            <ListView.Behaviors>
                <behaviours:ListViewSelectedItemBehavior />
            </ListView.Behaviors>
        </ListView>
    </StackLayout>
</ContentPage>
