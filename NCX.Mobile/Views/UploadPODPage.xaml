﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:prism="http://prismlibrary.com"
             xmlns:converters="clr-namespace:NCX.Mobile.Converters;assembly=NCX.Mobile.Forms"
             xmlns:abstractions="clr-namespace:NCX.Mobile.Properties;assembly=NCX.Mobile.Abstractions"
             xmlns:helpers="clr-namespace:NCX.Mobile.Helpers;assembly=NCX.Mobile.Forms"
             prism:ViewModelLocator.AutowireViewModel="Automatic"
             x:Class="NCX.Mobile.Views.UploadPODPage"
             x:Name="page"
             Title="{x:Static abstractions:Resources.SendPODText}"
             Padding="14"
             BackgroundColor="#323538">

    <ContentPage.Resources>
        <Style TargetType="Label">
            <Setter Property="TextColor" Value="White"/>
        </Style>
        <ResourceDictionary>
            <converters:FirstLoadLocationSourceTypeConverter x:Key="FirstLoadLocationSourceTypeConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Photos navigation panel -->
        <StackLayout Orientation="Horizontal"
                     IsVisible="{Binding PhotoViewerViewModel.CurrentItem, Converter={StaticResource ObjectToBoolConverter}}"
                     Margin="0, -14, 0, 0">
            <Label x:Name="LabelPhotoNavStatus"
                   VerticalOptions="Center"
                   HorizontalOptions="StartAndExpand"
                   FontSize="Medium" />
            <Button Text="{x:Static helpers:FontIcons.ArrowLeft}"
                    Command="{Binding ShowPreviousPhotoCommand}"
                    Style="{StaticResource BtnIconStyle}"
                    HorizontalOptions="End" />
            <Button Text="{x:Static helpers:FontIcons.ArrowRight}"
                    Command="{Binding ShowNextPhotoCommand}"
                    Style="{StaticResource BtnIconStyle}"
                    HorizontalOptions="End"
                    Margin="7,0" />
            <Button Text="{x:Static helpers:FontIcons.Trash}"
                    Command="{Binding RemovePODItemCommand}"
                    CommandParameter="{Binding PhotoViewerViewModel.CurrentItem}"
                    HorizontalOptions="End"
                    Style="{StaticResource BtnIconStyle}" />
        </StackLayout>

        <!-- Photo preview -->
        <Image Source="{Binding PhotoViewerViewModel.CurrentItem.Path}"
               Aspect="AspectFit"
               BackgroundColor="Transparent"
               IsVisible="{Binding PhotoViewerViewModel.CurrentItem, Converter={StaticResource ObjectToBoolConverter}}"
               Grid.Row="1" />

        <Label Text="{x:Static abstractions:Resources.CapturePODInfoText}"
               IsVisible="{Binding PhotoViewerViewModel.CurrentItem, Converter={StaticResource ObjectToBoolInverseConverter}}"
               Grid.Row="1" />

        <!-- Take photo button -->
        <Button Text="{x:Static abstractions:Resources.TakePhotoText}"
                Style="{StaticResource BtnSecondaryFormStyle}"
                Command="{Binding TakePhotoCommand}"
                Margin="14"
                Grid.Row="2">
            <Button.Triggers>
                <DataTrigger TargetType="Button"
                             Binding="{Binding PhotoViewerViewModel.CurrentItem, Converter={StaticResource ObjectToBoolConverter}}"
                             Value="True">
                    <Setter Property="Text"
                            Value="{x:Static abstractions:Resources.TakeAnotherPhotoText}" />
                </DataTrigger>
            </Button.Triggers>
        </Button>

        <!-- Photo library button -->
        <Button Text="{x:Static abstractions:Resources.PhotoLibraryText}"
                Style="{StaticResource BtnSecondaryFormStyle}"
                Command="{Binding ChoosePhotoFromLibraryCommand}"
                Margin="14,0,14,14"
                Grid.Row="3" />

        <!-- Send button -->
        <Button Text="{x:Static abstractions:Resources.SendText}"
                Style="{StaticResource BtnFormStyle}"
                Command="{Binding SendAllPODItemsCommand}"
                IsVisible="{Binding PhotoViewerViewModel.CurrentItem, Converter={StaticResource ObjectToBoolConverter}}"
                Grid.Row="4"
                Margin="14,0" />
    </Grid>
</ContentPage>
