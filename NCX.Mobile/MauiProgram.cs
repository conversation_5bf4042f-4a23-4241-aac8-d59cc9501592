﻿using FFImageLoading.Maui;
using Microsoft.Maui.Controls.Compatibility.Hosting;
using Prism.Behaviors;
using NCX.Logging.Commands;
using NCX.Logging.Interfaces;
using NCX.Logging.Services;
using NCX.Mobile.Data;
using NCX.Mobile.Data.Impl;
using NCX.Mobile.Events;
using NCX.Mobile.Helpers;
using NCX.Mobile.IFTA;
using NCX.Mobile.Services;
using NCX.Mobile.Services.Impl;
using NCX.Mobile.ViewModels;
using NCX.Mobile.Views;
using Microsoft.AppCenter.Crashes;
using NCX.Mobile.Controls;
using NCX.Mobile.Models;
using NCX.Mobile.Behaviors;
using SQLite;
using Shiny;
using DryIoc;

using NCXGeolocation = NCX.Mobile.Services.IGeolocation;
using Shiny.Locations;
using Microsoft.Maui.LifecycleEvents;
#if ANDROID || IOS
using Plugin.Firebase.Crashlytics;
#endif

#if IOS
using Plugin.Firebase.Core.Platforms.iOS;
#elif ANDROID
using Plugin.Firebase.Core.Platforms.Android;
#endif
using NCX.Logging.AppCenter;

#if ANDROID
using NCX.Mobile.Droid.Services;
#endif


namespace NCX.Mobile
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            // Create and configure your DryIoc container with tracking for disposable transients.
            var dryContainer = new Container(rules => rules.WithTrackingDisposableTransients());

            var builder = MauiApp.CreateBuilder();

            // Register Shiny
            builder.UseShiny();
            builder.Services.AddNotifications();
            builder.Services.AddJobs();
            builder.Services.AddGps<BackgroudnGpsRunnerDelegate>();

            builder
                .UseMauiApp<App>()
                .UseMauiCompatibility()
                .RegisterFirebase()
                .UsePrism(prism =>
                {
                    prism.RegisterTypes(RegisterTypes)
                          .ConfigureModuleCatalog(moduleCatalog =>
                          {
                              moduleCatalog.AddModule<IFTAModule>();
                          })
                          .OnInitialized(OnInitialized);
                })
                .UseFFImageLoading()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                })
                .UseMauiMaps();

            // Register custom behavior factory
            builder.Services.AddSingleton<IPageBehaviorFactory, NCXPageBehaviorFactory>();

            // Initialize ServiceStack as a singleton and perform additional registrations.
            builder.Services.AddSingleton<ServiceStack>(provider =>
            {
                var containerProvider = provider.GetRequiredService<IContainerProvider>();
                var containerRegistry = provider.GetRequiredService<IContainerRegistry>();
                var serviceStack = new ServiceStack(containerProvider, containerRegistry);
                serviceStack.InitializeInternal();
                return serviceStack;
            });

#if ANDROID
            AndroidStartup.ConfigureAndroidServices(builder.Services);
#endif

            return builder.Build();
        }

        static async Task NavigateToFirstViewAsync(bool isUserLoggedIn)
        {
            var container = ContainerLocator.Container;
            var navigationService = container.Resolve<INavigationService>();

            INavigationResult result;

            if (isUserLoggedIn)
            {
                result = await navigationService.NavigateAsync(NavigationServiceKeys.MainUri, new NavigationParameters("?loadHaulsFromOnline=true"));
            }
            else
            {
                result = await navigationService.NavigateAsync(NavigationServiceKeys.Login);
            }

            if (!result.Success)
            {
                Crashes.TrackError(new Exception($"Navigation to {(isUserLoggedIn ? "MainPage" : "LoginPage")} failed."));
            }
        }

        private static void RegisterTypes(IContainerRegistry containerRegistry)
        {
            containerRegistry.RegisterSingleton<ShowPopupOnNewLoadsCommand>();
            containerRegistry.RegisterSingleton<IAppUpdateCheckerService, AppUpdateCheckerService>();
            containerRegistry.Register<IHaulCommandProvider, HaulCommandProvider>();
            containerRegistry.Register<ILoadLocationItemCommandProvider, LoadLocationItemCommandProvider>();

            // Register navigation pages
            containerRegistry.RegisterForNavigation<NavigationPage>();
            containerRegistry.RegisterForNavigation<LoginPage, LoginPageViewModel>();
            containerRegistry.RegisterForNavigation<MainPage, MainPageViewModel>();
            containerRegistry.RegisterForNavigation<MainMasterDetailPage, MainMasterDetailPageViewModel>();
            containerRegistry.RegisterForNavigation<RootPage>();
            containerRegistry.RegisterForNavigation<LoadDetailsPage, LoadDetailsPageViewModel>();
            containerRegistry.RegisterForNavigation<UploadPODPage, UploadPODPageViewModel>();
            containerRegistry.RegisterForNavigation<ViewPODItemPage, ViewPODItemPageViewModel>();
            containerRegistry.RegisterForNavigation<SelectApiHostPage, SelectApiHostPageViewModel>();
            containerRegistry.RegisterForNavigation<ReportDelayPage, ReportDelayPageViewModel>();
            containerRegistry.RegisterForNavigation<ResetUserPasswordPage, ResetUserPasswordPageViewModel>();
            containerRegistry.RegisterForNavigation<LanguagePage, LanguagePageViewModel>();
            containerRegistry.RegisterForNavigation<UserProfilePage, UserProfilePageViewModel>();
            containerRegistry.RegisterForNavigation<ChangeUserPasswordPage, ChangeUserPasswordPageViewModel>();
            containerRegistry.RegisterForNavigation<SelectLoadLocationToUpdatePage, SelectLoadLocationToUpdatePageViewModel>();
            containerRegistry.RegisterForNavigation<UpdateLoadLocationStatusPage, UpdateLoadLocationStatusPageViewModel>();
            containerRegistry.RegisterForNavigation<HelpPage, HelpPageViewModel>();
            containerRegistry.RegisterForNavigation<WebViewPage, WebViewPageViewModel>();
            containerRegistry.RegisterForNavigation<NewLoadPopupPage, NewLoadPopupPageViewModel>();

            ViewModelLocationProvider.Register<DashboardClock, DashboardClockViewModel>();

            // Register logging services
            containerRegistry.RegisterSingleton<ILogProcessor, LogProcessor>();
            containerRegistry.RegisterSingleton<ILogService, LogService>();
            RegisterLogCommands(containerRegistry);

            // Register API and other services
            containerRegistry.RegisterSingleton<INCXApi>(provider =>
            {
                var factory = provider.Resolve<NCXApiFactory>();
                return factory.GetInstance();
            });
            containerRegistry.RegisterSingleton<INCXApiService, NCXApiService>();
            containerRegistry.RegisterSingleton<UrlResolver>();
            containerRegistry.RegisterSingleton<IUserSessionService, UserSessionService>();
            containerRegistry.RegisterSingleton<IUserService, UserService>();
            containerRegistry.RegisterSingleton<IUserServiceLogoutCommand, UserServiceLogoutCommand>();

            // Register platform-agnostic services
            containerRegistry.RegisterSingleton<NCXGeolocation, GeolocationImplementation>();

            // Register the missing IHaulService if not already registered
            containerRegistry.RegisterSingleton<IHaulService, HaulService>();

            containerRegistry.RegisterSingleton<IHaulCache, HaulCache>();
            containerRegistry.RegisterSingleton<ITemporaryHaulCache, TemporaryHaulCache>();
            containerRegistry.RegisterSingleton<IPushOperationCacheFactory, PushOperationCacheFactory>();
            containerRegistry.RegisterSingleton<IPushOperationCacheService, PushOperationCacheService>();
            containerRegistry.RegisterSingleton<IPushOperationProcessor, PushOperationProcessor>();
            containerRegistry.RegisterSingleton<IPushOperationService, PushOperationService>();
            containerRegistry.RegisterSingleton<ApiHostManager>();

            containerRegistry.RegisterSingleton<IAppPermissionsService, AppPermissionsService>();
            containerRegistry.RegisterSingleton<ITranslator, Translator>();
            containerRegistry.RegisterSingleton<IMessagingService, MessagingService>();
            containerRegistry.RegisterSingleton<IMapDirectionsTask, MapDirectionsTask>();

            containerRegistry.RegisterSingleton<PushNotificationService>();
            containerRegistry.Register<IPushNotificationListener, PushNotificationService>();
            containerRegistry.Register<IPushNotificationService, PushNotificationService>();

            containerRegistry.RegisterSingleton<IRefreshHaulsCommand, RefreshHaulsCommand>();
            containerRegistry.RegisterSingleton<IUserSessionChangeListener, NavigateToLogoutPageOnUserSessionChange>();
            containerRegistry.RegisterSingleton<IPushNotificationTokenCacheService, PushNotificationTokenCacheService>();
            containerRegistry.RegisterSingleton<RefreshHaulsPushNotificationListener>();
            containerRegistry.RegisterSingleton<ShowPopupOnNewLoadsService>();

            containerRegistry.RegisterSingleton<ICurrentPageService, CurrentPageService>();
            containerRegistry.RegisterSingleton<IUserInteractionService, UserInteractionService>();
            // containerRegistry.RegisterSingleton<IApplicationProvider, ApplicationProvider>();

            // Register the SQLite connection factory as a delegate
            containerRegistry.RegisterSingleton<Func<SQLiteAsyncConnection>>(() => new Func<SQLiteAsyncConnection>(SQLiteAsyncConnectionFactory));

            containerRegistry.RegisterSingleton<ILocalize, Localize>();
            containerRegistry.RegisterSingleton<IAppSettings, AppSettings>();

            containerRegistry.RegisterSingleton<NCXApiFactory>();

            // Register .NET Memory Cache for caching
            containerRegistry.RegisterInstance(new Microsoft.Extensions.Caching.Memory.MemoryCache(new Microsoft.Extensions.Caching.Memory.MemoryCacheOptions()));

            // Register additional tasks and jobs
            containerRegistry.RegisterSingleton<NCXAppStartupTask>();
            containerRegistry.RegisterSingleton<ApiCacheSyncJob>();
            containerRegistry.RegisterSingleton<DefaultChainedPushOperationErrorHandler>();

            containerRegistry.RegisterSingleton<IDocumentScanner, DocumentScanner>();
        }

        private static SQLiteAsyncConnection SQLiteAsyncConnectionFactory()
        {
            string dbFilePath = Path.Combine(FileSystem.AppDataDirectory, "ncx_v2.db3");
            return new SQLiteAsyncConnection(dbFilePath);
        }

        private static void RegisterLogCommands(IContainerRegistry containerRegistry)
        {
            var logProcessor = Prism.Ioc.ContainerLocator.Container.Resolve<ILogProcessor>();
#if !DEBUG
            logProcessor.RegisterCommand(new AppCenterLogCommand());
#else
            logProcessor.RegisterCommand(new ExceptionLogCommand());
            logProcessor.RegisterCommand(new DebugLogCommand());
#endif
        }

        private static async void OnInitialized(IContainerProvider containerProvider)
        {
            try
            {
                ReactiveUI.RxApp.DefaultExceptionHandler = new RxErrorHandler();

                var container = Prism.Ioc.ContainerLocator.Container;
                var ea = container.Resolve<Prism.Events.IEventAggregator>();
                ea.GetEvent<ShowLoginRequestEvent>().Subscribe(async reasonIsSessionExpired =>
                {
                    // Force navigation to login page
                    var navigationService = container.Resolve<INavigationService>();
                    var result = await navigationService.NavigateAsync($"/{NavigationServiceKeys.Login}");
                    if (!result.Success)
                    {
                        Crashes.TrackError(new Exception($"Navigation to LoginPage failed: {result.Exception}"));
                    }
                });

                container.Resolve<ILocalize>().Initialize();
                container.Resolve<ShowPopupOnNewLoadsService>().Initialize();

                var userSessionService = container.Resolve<IUserSessionService>();
                var navigationService = container.Resolve<INavigationService>();

                if (userSessionService.CurrentSession != null)
                {
                    var result = await navigationService.NavigateAsync(NavigationServiceKeys.MainUri,
                        new NavigationParameters("?loadHaulsFromOnline=true"));
                    if (!result.Success)
                    {
                        HandleNavigationError(result.Exception);
                    }

                    // Start GPS listener after navigation is complete
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        await Task.Delay(1000); // Short delay to ensure UI is ready
                        await container.Resolve<IUserService>().StartGpsListener();
                    });
                }
                else
                {
                    var result = await navigationService.NavigateAsync("RootPage/NavigationPage/LoginPage");
                    if (!result.Success)
                    {
                        HandleNavigationError(result.Exception);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("OnInitialized error: " + ex);
                Crashes.TrackError(ex);
            }
        }

        private static MauiAppBuilder RegisterFirebase(this MauiAppBuilder builder)
        {
            builder.ConfigureLifecycleEvents(events =>
            {
#if IOS
            events.AddiOS(iOS => iOS.WillFinishLaunching((_, __) => {
                CrossFirebase.Initialize();
                return false;
            }));
#elif ANDROID
                events.AddAndroid(android => android.OnCreate((activity, _) => {
                    CrossFirebase.Initialize(activity);
                }));
#endif
            });

            return builder;
        }

        public static void HandleNavigationError(Exception? ex)
        {
            if (ex == null) return;
            // Log the error details to the debug output.
            System.Diagnostics.Debug.WriteLine($"Navigation failed: {ex}");

            // Track the error with App Center.
            Crashes.TrackError(ex);

            // Attempt to display an alert on the main page, if available.
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var mainPage = Application.Current?.MainPage;
                if (mainPage != null)
                {
                    await mainPage.DisplayAlert("Navigation Error", $"An error occurred during navigation: {ex.Message}", "OK");
                }
            });
        }
    }

    public class RxErrorHandler : IObserver<Exception>
    {
        public void OnCompleted() { }
        public void OnError(Exception error) => Console.WriteLine(error);
        public void OnNext(Exception value) { }
    }
}
