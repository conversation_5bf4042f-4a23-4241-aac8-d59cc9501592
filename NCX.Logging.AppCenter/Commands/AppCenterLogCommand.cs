﻿using System.Collections.Generic;
using Microsoft.AppCenter.Analytics;
using Microsoft.AppCenter.Crashes;
using NCX.Logging.Enums;
using NCX.Logging.Interfaces;
using NCX.Logging.Models;

namespace NCX.Logging.AppCenter
{
    public class AppCenterLogCommand : ILogCommand
    {
        public bool CanExecute(LogParameter parameter)
        {
            return !(parameter.Type == LogType.Log && parameter.Exception == null);
        }

        public void Execute(LogParameter parameter)
        {
            switch (parameter.Type)
            {
                case LogType.Track:
                    Analytics.TrackEvent(string.IsNullOrEmpty(parameter.MemberName) ? parameter.Source : parameter.MemberName,
                        parameter.Data);
                    break;
                case LogType.Log:
                    if (parameter.Data == null)
                        parameter.Data = new Dictionary<string, string>();
                    
                    parameter.Data.Add("Exception", parameter.Exception.ToString());
                    Crashes.TrackError(parameter.Exception, parameter.Data);
                    break;
            }
        }
    }
}
