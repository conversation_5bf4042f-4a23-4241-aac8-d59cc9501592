{"version": "0.2.0", "configurations": [{"name": "Launch iOS Simulator (iPhone - Recommended)", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/NCX.Mobile/bin/Debug/net8.0-ios/iossimulator-x64/NCX.Mobile.dll", "args": [], "cwd": "${workspaceFolder}/NCX.Mobile", "stopAtEntry": false, "console": "internalConsole", "preLaunchTask": "build-and-run-ios-iphone", "env": {"DOTNET_ENVIRONMENT": "Development"}, "requireExactSource": false}, {"name": "Launch iOS Simulator (Direct)", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/NCX.Mobile/bin/Debug/net8.0-ios/iossimulator-x64/NCX.Mobile.dll", "args": [], "cwd": "${workspaceFolder}/NCX.Mobile", "stopAtEntry": false, "console": "internalConsole", "preLaunchTask": "build-ios-simulator", "env": {"DOTNET_ENVIRONMENT": "Development"}, "requireExactSource": false}, {"name": "Launch iOS Simulator (Clean Build)", "type": "coreclr", "request": "launch", "program": "dotnet", "args": ["build", "${workspaceFolder}/NCX.Mobile/NCX.Mobile.csproj", "--framework", "net8.0-ios", "--configuration", "Debug", "-t:Run"], "cwd": "${workspaceFolder}", "stopAtEntry": false, "console": "integratedTerminal", "preLaunchTask": "clean-ios-simulator", "env": {"DOTNET_ENVIRONMENT": "Development"}, "requireExactSource": false}, {"name": "Attach to iOS Simulator", "type": "coreclr", "request": "attach", "processName": "NCX.Mobile"}, {"name": "Launch iOS Simulator (External Terminal)", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/NCX.Mobile/bin/Debug/net8.0-ios/iossimulator-x64/NCX.Mobile.dll", "args": [], "cwd": "${workspaceFolder}/NCX.Mobile", "stopAtEntry": false, "console": "externalTerminal", "preLaunchTask": "build-ios-simulator", "env": {"DOTNET_ENVIRONMENT": "Development"}, "requireExactSource": false}, {"name": "Debug iOS Simulator (LLDB)", "type": "lldb", "request": "launch", "program": "${workspaceFolder}/NCX.Mobile/bin/Debug/net8.0-ios/iossimulator-x64/NCX.Mobile.app/NCX.Mobile", "args": [], "cwd": "${workspaceFolder}", "preLaunchTask": "build-ios-simulator", "env": {"DOTNET_ENVIRONMENT": "Development"}}, {"name": "Run iOS Simulator (Shell)", "type": "node", "request": "launch", "program": "/usr/local/share/dotnet/dotnet", "args": ["run", "--project", "${workspaceFolder}/NCX.Mobile/NCX.Mobile.csproj", "--framework", "net8.0-ios", "--configuration", "Debug"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "env": {"DOTNET_ENVIRONMENT": "Development"}}, {"name": "Launch iOS Simulator (Manual Install)", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/NCX.Mobile/bin/Debug/net8.0-ios/iossimulator-x64/NCX.Mobile.dll", "args": [], "cwd": "${workspaceFolder}/NCX.Mobile", "stopAtEntry": false, "console": "internalConsole", "preLaunchTask": "install-and-launch-ios-simulator", "env": {"DOTNET_ENVIRONMENT": "Development"}, "requireExactSource": false}, {"name": "Attach to iOS Simulator (After Manual Launch)", "type": "coreclr", "request": "attach", "processName": "NCX.Mobile", "justMyCode": false}]}