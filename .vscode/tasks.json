{"version": "2.0.0", "tasks": [{"label": "clean-and-build-ios-simulator", "dependsOrder": "sequence", "dependsOn": ["clean-ios-simulator", "build-ios-simulator"], "group": {"kind": "build", "isDefault": false}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": false, "clear": true}}, {"label": "build-ios-simulator", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/NCX.Mobile/NCX.Mobile.csproj", "--framework", "net8.0-ios", "--configuration", "Debug", "-p:RuntimeIdentifier=iossimulator-x64"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "dotnet: build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/NCX.Mobile/NCX.Mobile.csproj", "--framework", "net8.0-ios", "--configuration", "Debug"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "clean-ios-simulator", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/NCX.Mobile/NCX.Mobile.csproj", "--framework", "net8.0-ios", "--configuration", "Debug"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "run-ios-simulator", "command": "dotnet", "type": "process", "args": ["run", "--project", "${workspaceFolder}/NCX.Mobile/NCX.Mobile.csproj", "--framework", "net8.0-ios", "--configuration", "Debug"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": false}, "dependsOn": "build-ios-simulator", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}}, {"label": "install-and-launch-ios-simulator", "type": "shell", "command": "bash", "args": ["-c", "xcrun simctl install 00F0B77D-2686-4E5C-90A8-FF5C2C03ACAD ${workspaceFolder}/NCX.Mobile/bin/Debug/net8.0-ios/iossimulator-x64/NCX.Mobile.app && open -a Simulator && xcrun simctl launch 00F0B77D-2686-4E5C-90A8-FF5C2C03ACAD com.ncx.mobile"], "group": "build", "dependsOn": "build-ios-simulator", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": false, "clear": false}}, {"label": "build-and-run-ios-iphone", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/NCX.Mobile/NCX.Mobile.csproj", "--framework", "net8.0-ios", "--configuration", "Debug", "-p:RuntimeIdentifier=iossimulator-x64", "-p:_DeviceName=iPhone 16 Plus", "-t:Run"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": false}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": false, "clear": true}}, {"label": "build-and-install-ios-simulator", "type": "shell", "command": "bash", "args": ["-c", "dotnet build ${workspaceFolder}/NCX.Mobile/NCX.Mobile.csproj --framework net8.0-ios --configuration Debug -p:RuntimeIdentifier=iossimulator-x64 && xcrun simctl install 00F0B77D-2686-4E5C-90A8-FF5C2C03ACAD ${workspaceFolder}/NCX.Mobile/bin/Debug/net8.0-ios/iossimulator-x64/NCX.Mobile.app && echo 'App installed successfully. Please launch manually from simulator.'"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": false, "clear": true}}]}