{
  "omnisharp.enableRoslynAnalyzers": true,
  "omnisharp.enableEditorConfigSupport": true,
  "omnisharp.useModernNet": true,
  "dotnet.defaultSolution": "NCX.Mobile.sln",
  
  // Define conditional compilation symbols for C# files
  "csharp.suppressDotnetRestoreNotification": false,
  "csharp.suppressBuildAssetsNotification": false,
  "csharp.suppressHiddenDiagnostics": false,
  "csharp.suppressProjectJsonNotification": false,
  
  // Define conditional compilation symbols for OmniSharp
  "omnisharp.defaultLaunchSolution": "NCX.Mobile.sln",
  "omnisharp.enableMsBuildLoadProjectsOnDemand": true,
  "omnisharp.disableMSBuildDiagnosticWarning": false,
  
  // Define conditional compilation symbols
  "omnisharp.defaultRuntime": "net8.0",
  "omnisharp.useGlobalMono": "never",
  "omnisharp.waitForDebugger": false,
  "omnisharp.loggingLevel": "information",
  "omnisharp.projectLoadTimeout": 60,
  "omnisharp.monoPath": "",
  "omnisharp.path": "latest",
  
  // Define conditional compilation constants
  "omnisharp.enableRoslynAnalyzers": true,
  "omnisharp.analyzeOpenDocumentsOnly": false,
  "omnisharp.disableImplicitPackageReferences": false,
  "dotnet.unitTests.runSettingsPath": ""
}